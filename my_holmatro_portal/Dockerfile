ARG PYTHON_VERSION=3.12
FROM python:${PYTHON_VERSION}-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    gnupg \
    unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC
RUN curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg && \
    curl https://packages.microsoft.com/config/debian/12/prod.list | tee /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql18 mssql-tools18

# Set environment variables
ENV PATH="$PATH:/opt/mssql-tools18/bin"
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Install Poetry
RUN pip install --no-cache-dir poetry==1.8.2 \
    && poetry config virtualenvs.create false

# Copy project files
COPY pyproject.toml poetry.lock alembic.ini Makefile ./


# Install dependencies
ARG INSTALL_DEV=false
RUN poetry install --no-root $(test "${INSTALL_DEV}" = "false" && echo "--no-dev")

COPY src/ ./src/
COPY migrations/ ./migrations/
EXPOSE 8000
CMD ["make", "run"]
