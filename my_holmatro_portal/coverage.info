SF:src/__init__.py
end_of_record
SF:src/config.py
DA:1,1
DA:2,1
LF:2
LH:2
end_of_record
SF:src/http_api/__init__.py
end_of_record
SF:src/http_api/dependencies/__init__.py
end_of_record
SF:src/http_api/dependencies/required_header.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
LF:14
LH:14
FN:14,19,verify_subscription_key
FNDA:1,verify_subscription_key
FNF:1
FNH:1
end_of_record
SF:src/http_api/exception_handlers.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:16,0
DA:17,0
DA:19,0
DA:21,0
DA:24,1
DA:27,1
DA:35,1
DA:36,0
DA:38,0
DA:39,0
DA:40,0
DA:42,0
DA:44,1
DA:45,1
DA:47,1
DA:49,1
LF:27
LH:18
FN:14,21,standard_json_response
FNDA:0,standard_json_response
FN:24,49,register_exception_handlers
FNDA:1,register_exception_handlers
FN:35,42,register_exception_handlers.fallback_exception_handler
FNDA:0,register_exception_handlers.fallback_exception_handler
FNF:3
FNH:1
end_of_record
SF:src/http_api/middleware/__init__.py
end_of_record
SF:src/http_api/middleware/auth_middleware.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:29,1
DA:31,1
DA:33,1
DA:34,1
DA:35,0
DA:36,1
DA:37,1
DA:38,1
DA:39,1
DA:40,0
DA:41,0
DA:43,1
DA:44,1
DA:46,1
DA:51,0
DA:53,1
DA:55,1
DA:56,1
DA:57,1
DA:58,1
DA:59,1
DA:61,0
DA:63,0
DA:64,0
DA:66,0
DA:67,0
DA:68,0
DA:70,0
DA:71,0
DA:74,1
DA:76,1
DA:77,1
DA:78,1
DA:79,1
DA:80,1
DA:81,1
DA:82,1
DA:83,1
DA:84,1
DA:85,1
DA:88,1
DA:90,1
DA:91,1
DA:92,1
DA:93,1
DA:94,1
DA:95,1
DA:96,1
DA:97,1
DA:98,1
DA:99,1
DA:102,1
DA:103,1
DA:105,1
DA:108,1
DA:111,1
DA:112,1
DA:118,1
DA:119,1
DA:120,1
DA:121,1
DA:127,0
DA:128,0
DA:129,1
DA:130,0
DA:132,0
DA:133,0
DA:134,0
DA:135,0
DA:138,1
DA:140,1
DA:141,1
DA:142,1
DA:145,1
DA:147,0
DA:148,0
DA:149,0
DA:150,0
DA:151,0
LF:102
LH:78
FN:25,27,JWTValidationMiddleware.__init__
FNDA:1,JWTValidationMiddleware.__init__
FN:29,31,JWTValidationMiddleware.should_skip_validation
FNDA:1,JWTValidationMiddleware.should_skip_validation
FN:33,51,JWTValidationMiddleware.dispatch
FNDA:1,JWTValidationMiddleware.dispatch
FN:53,71,JWTValidationMiddleware._validate_jwt
FNDA:1,JWTValidationMiddleware._validate_jwt
FN:74,85,fetch_openid_config
FNDA:1,fetch_openid_config
FN:88,99,fetch_jwks
FNDA:1,fetch_jwks
FN:102,135,validate_jwt_with_authlib
FNDA:1,validate_jwt_with_authlib
FN:138,142,get_current_user
FNDA:1,get_current_user
FN:145,151,validate_api_key
FNDA:0,validate_api_key
FNF:9
FNH:8
end_of_record
SF:src/http_api/middleware/user_validation.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:17,1
DA:20,1
DA:21,1
DA:26,1
DA:27,1
DA:28,1
DA:30,1
DA:35,1
DA:36,1
DA:38,1
DA:39,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,1
DA:45,1
DA:47,1
DA:48,1
DA:49,1
DA:53,1
DA:54,1
DA:55,1
DA:59,1
DA:60,1
DA:61,1
DA:65,1
DA:66,1
DA:67,1
DA:69,1
DA:70,1
DA:72,1
DA:74,1
DA:76,1
DA:81,1
DA:82,0
DA:83,1
LF:51
LH:50
FN:21,28,UserValidationMiddleware.__init__
FNDA:1,UserValidationMiddleware.__init__
FN:30,70,UserValidationMiddleware.dispatch
FNDA:1,UserValidationMiddleware.dispatch
FN:72,74,UserValidationMiddleware.should_skip_validation
FNDA:1,UserValidationMiddleware.should_skip_validation
FN:76,83,UserValidationMiddleware.should_replace_email
FNDA:1,UserValidationMiddleware.should_replace_email
FNF:4
FNH:4
end_of_record
SF:src/http_api/routers/__init__.py
end_of_record
SF:src/http_api/routers/general.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:14,1
DA:15,1
DA:18,1
DA:21,1
DA:24,1
DA:27,1
DA:28,1
DA:32,1
LF:19
LH:19
FN:21,24,get_user_interactor
FNDA:1,get_user_interactor
FN:28,32,get_user
FNDA:1,get_user
FNF:2
FNH:2
end_of_record
SF:src/http_api/routers/health.py
DA:1,1
DA:3,1
DA:6,1
DA:7,1
DA:8,1
LF:5
LH:5
FN:7,8,health_check
FNDA:1,health_check
FNF:1
FNH:1
end_of_record
SF:src/http_api/routers/products.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:16,1
DA:19,1
DA:20,1
DA:23,1
DA:24,1
DA:29,1
DA:32,1
DA:33,1
DA:38,1
DA:41,1
DA:42,1
DA:47,1
LF:25
LH:25
FN:19,20,get_product_interactor
FNDA:1,get_product_interactor
FN:24,29,get_sales_info
FNDA:1,get_sales_info
FN:33,38,get_sales_price
FNDA:1,get_sales_price
FN:42,47,get_product_availability
FNDA:1,get_product_availability
FNF:4
FNH:4
end_of_record
SF:src/interactors/__init__.py
end_of_record
SF:src/interactors/access_validation_interactor.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:10,1
DA:11,1
DA:12,1
DA:14,1
DA:19,1
DA:20,1
DA:21,0
DA:22,1
DA:23,1
DA:24,1
DA:26,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
LF:20
LH:19
FN:11,12,AccessValidationInteractor.__init__
FNDA:1,AccessValidationInteractor.__init__
FN:14,24,AccessValidationInteractor.validate_user_access
FNDA:1,AccessValidationInteractor.validate_user_access
FN:26,31,AccessValidationInteractor._get_validated_user
FNDA:1,AccessValidationInteractor._get_validated_user
FNF:3
FNH:3
end_of_record
SF:src/interactors/product_interactor.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:12,1
DA:15,1
DA:16,1
DA:17,1
DA:19,1
DA:23,1
DA:24,1
DA:26,1
DA:30,1
DA:31,1
DA:33,1
DA:37,1
DA:38,1
LF:22
LH:22
FN:16,17,ProductInteractor.__init__
FNDA:1,ProductInteractor.__init__
FN:19,24,ProductInteractor.get_product_sales_info
FNDA:1,ProductInteractor.get_product_sales_info
FN:26,31,ProductInteractor.get_product_sales_price
FNDA:1,ProductInteractor.get_product_sales_price
FN:33,38,ProductInteractor.get_product_availability
FNDA:1,ProductInteractor.get_product_availability
FNF:4
FNH:4
end_of_record
SF:src/interactors/user_interactor.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:11,1
DA:12,1
DA:13,1
DA:15,1
DA:19,1
DA:20,1
LF:12
LH:12
FN:12,13,UserInteractor.__init__
FNDA:1,UserInteractor.__init__
FN:15,20,UserInteractor.get_user_by_email
FNDA:1,UserInteractor.get_user_by_email
FNF:2
FNH:2
end_of_record
SF:src/main.py
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:13,1
DA:15,1
DA:17,1
DA:19,1
DA:26,1
DA:27,1
DA:35,1
DA:36,1
DA:37,1
DA:38,1
DA:39,1
DA:40,1
DA:42,1
DA:45,1
DA:47,1
DA:51,0
DA:52,0
DA:54,0
DA:57,1
DA:58,0
LF:30
LH:26
FN:15,42,create_app
FNDA:1,create_app
FN:47,54,main
FNDA:0,main
FNF:2
FNH:1
end_of_record
SF:src/models/__init__.py
end_of_record
SF:src/models/db/__init__.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:23,1
LF:22
LH:22
end_of_record
SF:src/models/db/attributes.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:22,1
DA:25,1
DA:26,1
LF:18
LH:18
end_of_record
SF:src/models/db/base.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:7,1
LF:5
LH:5
end_of_record
SF:src/models/db/companies.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:27,1
DA:28,1
LF:20
LH:20
end_of_record
SF:src/models/db/contact_attributes.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:16,1
DA:17,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:25,1
DA:35,1
DA:36,1
LF:21
LH:21
end_of_record
SF:src/models/db/contact_relation_sell_to.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:22,1
LF:16
LH:16
end_of_record
SF:src/models/db/contacts.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:11,1
DA:12,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:21,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:32,1
DA:39,1
LF:22
LH:22
end_of_record
SF:src/models/db/countries.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:27,1
LF:19
LH:19
end_of_record
SF:src/models/db/currencies.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:27,1
DA:28,1
LF:20
LH:20
end_of_record
SF:src/models/db/employees.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:29,1
LF:19
LH:19
end_of_record
SF:src/models/db/languages.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:27,1
LF:19
LH:19
end_of_record
SF:src/models/db/pricelists.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:24,1
DA:27,1
DA:28,1
LF:20
LH:20
end_of_record
SF:src/models/db/product_availability.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:29,1
DA:38,1
DA:39,1
DA:40,1
DA:43,1
LF:27
LH:27
end_of_record
SF:src/models/db/product_salesprices.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:34,1
DA:57,1
DA:58,1
DA:59,1
DA:60,1
DA:61,1
LF:33
LH:33
end_of_record
SF:src/models/db/products.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:22,1
DA:25,1
DA:32,1
LF:18
LH:18
end_of_record
SF:src/models/db/relation_attributes.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:16,1
DA:17,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:25,1
DA:31,1
DA:32,1
LF:21
LH:21
end_of_record
SF:src/models/db/relations.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:26,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:38,1
LF:26
LH:26
end_of_record
SF:src/models/db/relations_sell_to.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:31,1
DA:34,1
DA:35,1
DA:36,1
DA:37,1
DA:40,1
DA:45,1
DA:52,1
DA:53,1
DA:54,1
DA:55,1
DA:58,1
LF:36
LH:36
end_of_record
SF:src/models/db/timestamp.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:9,1
DA:10,1
DA:11,1
LF:8
LH:8
end_of_record
SF:src/models/db/units.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:26,1
DA:27,1
LF:19
LH:19
end_of_record
SF:src/models/db/users.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:20,1
DA:23,1
DA:29,1
LF:18
LH:18
end_of_record
SF:src/models/db/warehouses.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:15,1
DA:16,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:24,1
DA:27,1
DA:28,1
LF:20
LH:20
end_of_record
SF:src/models/response/product_response.py
DA:1,1
DA:2,1
DA:3,1
DA:6,1
DA:7,1
DA:9,1
LF:6
LH:6
end_of_record
SF:src/models/response/user_response.py
DA:1,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:17,1
LF:13
LH:13
end_of_record
SF:src/models/schemas/__init__.py
end_of_record
SF:src/models/schemas/contact.py
DA:1,0
DA:2,0
DA:4,0
DA:7,0
DA:8,0
DA:9,0
DA:10,0
DA:11,0
DA:14,0
DA:15,0
DA:16,0
DA:17,0
DA:20,0
DA:21,0
DA:22,0
LF:15
LH:0
end_of_record
SF:src/models/schemas/jwt_claims.py
DA:1,1
DA:2,1
DA:4,1
DA:5,1
DA:6,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:19,1
DA:20,1
DA:25,1
DA:26,1
DA:27,1
LF:19
LH:19
FN:20,27,JWTClaims.email
FNDA:1,JWTClaims.email
FNF:1
FNH:1
end_of_record
SF:src/models/schemas/product.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:22,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:35,1
DA:38,1
DA:39,1
DA:40,1
DA:41,1
DA:42,1
DA:43,1
DA:44,1
DA:46,1
DA:49,1
DA:50,1
DA:53,1
DA:56,1
DA:57,1
DA:60,1
DA:63,1
DA:64,1
DA:67,1
LF:45
LH:45
end_of_record
SF:src/models/schemas/user.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:25,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:33,1
DA:36,1
DA:37,1
DA:38,1
DA:40,1
DA:43,1
DA:44,1
DA:45,1
DA:46,1
DA:47,1
DA:48,1
DA:49,1
DA:50,1
DA:51,1
DA:52,1
DA:54,1
DA:57,1
DA:58,1
DA:61,1
DA:62,1
DA:63,1
DA:64,1
DA:65,1
DA:66,1
DA:67,1
DA:68,1
DA:69,1
LF:52
LH:52
end_of_record
SF:src/pre_start.py
DA:1,0
DA:3,0
DA:4,0
DA:5,0
DA:6,0
DA:7,0
DA:8,0
DA:9,0
DA:11,0
DA:12,0
DA:13,0
DA:15,0
DA:16,0
DA:19,0
DA:20,0
DA:21,0
DA:22,0
DA:24,0
DA:25,0
DA:26,0
DA:27,0
DA:28,0
DA:29,0
DA:30,0
DA:31,0
DA:34,0
DA:35,0
DA:36,0
DA:37,0
DA:40,0
DA:41,0
LF:31
LH:0
FN:20,31,init
FNDA:0,init
FN:34,37,main
FNDA:0,main
FNF:2
FNH:0
end_of_record
SF:src/repositories/__init__.py
end_of_record
SF:src/repositories/product_repository.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:13,1
DA:14,1
DA:15,1
DA:16,0
DA:18,1
DA:19,1
DA:20,0
DA:22,1
DA:23,1
DA:24,0
LF:19
LH:16
FN:15,16,ProductRepository.get_product_sales_info
FNDA:0,ProductRepository.get_product_sales_info
FN:19,20,ProductRepository.get_product_price
FNDA:0,ProductRepository.get_product_price
FN:23,24,ProductRepository.get_product_availability
FNDA:0,ProductRepository.get_product_availability
FNF:3
FNH:0
end_of_record
SF:src/repositories/user_repository.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:9,1
DA:10,1
DA:11,1
DA:12,0
DA:14,1
DA:15,1
DA:16,0
LF:12
LH:10
FN:11,12,UserRepository.get_by_email
FNDA:0,UserRepository.get_by_email
FN:15,16,UserRepository.get_user
FNDA:0,UserRepository.get_user
FNF:2
FNH:0
end_of_record
SF:src/services/__init__.py
end_of_record
SF:src/services/product_repository_impl.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:37,1
DA:40,1
DA:41,1
DA:42,1
DA:44,1
DA:45,1
DA:46,1
DA:47,1
DA:48,1
DA:50,1
DA:51,1
DA:52,1
DA:53,1
DA:54,1
DA:55,1
DA:56,1
DA:57,1
DA:58,1
DA:60,1
DA:61,1
DA:63,1
DA:64,1
DA:66,1
DA:68,1
DA:69,1
DA:70,1
DA:71,1
DA:72,1
DA:74,1
DA:75,1
DA:76,1
DA:78,1
DA:79,1
DA:80,1
DA:81,1
DA:82,1
DA:83,1
DA:85,1
DA:86,1
DA:88,1
DA:89,1
DA:90,1
DA:92,1
DA:94,1
DA:95,1
DA:96,1
DA:97,1
DA:98,1
DA:100,1
DA:102,1
DA:103,1
DA:104,1
DA:105,1
DA:106,1
DA:108,1
DA:109,1
DA:111,1
DA:112,1
DA:113,1
DA:115,1
DA:117,1
DA:118,1
DA:119,1
DA:120,1
DA:121,1
DA:123,1
DA:124,1
DA:125,1
DA:126,1
DA:128,1
DA:130,1
DA:131,1
DA:133,1
DA:134,1
DA:135,1
DA:136,1
DA:137,1
DA:139,1
DA:171,1
DA:173,1
DA:204,1
DA:219,1
DA:220,0
DA:221,0
DA:223,1
DA:224,1
DA:226,1
DA:227,1
DA:236,1
DA:243,1
DA:251,1
DA:259,1
DA:262,1
DA:273,1
DA:274,1
DA:294,1
DA:295,1
DA:333,1
DA:334,1
DA:350,1
DA:351,1
DA:376,1
DA:377,1
DA:401,1
DA:402,1
DA:404,1
DA:405,1
DA:444,1
DA:445,1
DA:481,1
DA:482,1
LF:145
LH:143
FN:41,42,ProductRepositoryImpl.__init__
FNDA:1,ProductRepositoryImpl.__init__
FN:44,72,ProductRepositoryImpl.get_product_sales_info
FNDA:1,ProductRepositoryImpl.get_product_sales_info
FN:74,98,ProductRepositoryImpl.get_product_price
FNDA:1,ProductRepositoryImpl.get_product_price
FN:100,121,ProductRepositoryImpl.get_product_availability
FNDA:1,ProductRepositoryImpl.get_product_availability
FN:123,126,ProductRepositoryImpl._get_user_pricelists_query
FNDA:1,ProductRepositoryImpl._get_user_pricelists_query
FN:128,169,ProductRepositoryImpl._get_availability_query
FNDA:1,ProductRepositoryImpl._get_availability_query
FN:171,217,ProductRepositoryImpl._get_best_stock_query
FNDA:1,ProductRepositoryImpl._get_best_stock_query
FN:219,221,ProductRepositoryImpl._is_valid_user
FNDA:0,ProductRepositoryImpl._is_valid_user
FN:223,224,ProductRepositoryImpl._clean_article_numbers
FNDA:1,ProductRepositoryImpl._clean_article_numbers
FN:226,234,ProductRepositoryImpl._get_contacts_query
FNDA:1,ProductRepositoryImpl._get_contacts_query
FN:236,271,ProductRepositoryImpl._get_relation_sell_to_query
FNDA:1,ProductRepositoryImpl._get_relation_sell_to_query
FN:273,292,ProductRepositoryImpl._get_pricelists_query
FNDA:1,ProductRepositoryImpl._get_pricelists_query
FN:294,331,ProductRepositoryImpl._get_prices_query
FNDA:1,ProductRepositoryImpl._get_prices_query
FN:333,348,ProductRepositoryImpl._get_lowest_prices_query
FNDA:1,ProductRepositoryImpl._get_lowest_prices_query
FN:350,374,ProductRepositoryImpl._get_lowest_price_ids_query
FNDA:1,ProductRepositoryImpl._get_lowest_price_ids_query
FN:376,399,ProductRepositoryImpl._get_stock_query
FNDA:1,ProductRepositoryImpl._get_stock_query
FN:401,402,ProductRepositoryImpl._get_unit_names_query
FNDA:1,ProductRepositoryImpl._get_unit_names_query
FN:404,442,ProductRepositoryImpl._build_final_query
FNDA:1,ProductRepositoryImpl._build_final_query
FN:444,479,ProductRepositoryImpl._get_single_product_prices_query
FNDA:1,ProductRepositoryImpl._get_single_product_prices_query
FN:481,503,ProductRepositoryImpl._build_price_only_query
FNDA:1,ProductRepositoryImpl._build_price_only_query
FNF:20
FNH:19
end_of_record
SF:src/services/user_repository_impl.py
DA:1,1
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:16,1
DA:17,1
DA:18,1
DA:19,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:27,1
DA:28,1
DA:29,1
DA:30,1
DA:31,1
DA:32,1
DA:33,1
DA:36,1
DA:39,1
DA:40,1
DA:41,1
DA:43,1
DA:47,1
DA:49,1
DA:56,1
DA:58,1
DA:59,1
DA:61,1
DA:63,1
DA:75,1
DA:79,0
DA:80,0
DA:81,0
DA:82,0
DA:84,1
DA:86,1
DA:87,1
DA:88,1
DA:89,0
DA:90,1
DA:91,0
DA:92,1
DA:94,1
DA:98,1
DA:100,1
DA:101,1
DA:102,1
DA:104,1
DA:105,1
DA:106,1
DA:107,1
DA:108,1
DA:110,1
DA:111,1
DA:113,1
DA:114,1
DA:116,1
DA:117,1
DA:120,1
DA:121,1
DA:122,1
DA:123,1
DA:125,1
DA:127,1
DA:129,1
DA:130,1
DA:131,1
DA:133,1
DA:134,1
DA:135,1
DA:136,1
DA:138,1
DA:139,1
DA:140,1
DA:141,1
DA:143,1
DA:145,1
DA:146,1
DA:156,1
DA:157,1
DA:158,1
DA:164,1
DA:172,1
DA:180,1
DA:184,1
DA:217,1
DA:218,1
DA:219,1
DA:221,1
DA:222,1
DA:223,1
DA:224,1
DA:226,1
DA:227,1
DA:241,1
DA:243,1
DA:247,1
DA:248,1
DA:249,1
DA:250,1
DA:252,1
DA:270,1
DA:288,1
DA:289,1
DA:291,1
DA:292,1
DA:293,1
DA:295,1
DA:296,1
DA:297,1
DA:305,1
DA:307,1
DA:308,1
DA:314,1
DA:319,1
DA:323,1
DA:324,1
DA:325,0
DA:327,1
DA:330,1
DA:336,1
DA:337,1
DA:340,0
DA:344,0
DA:346,1
DA:348,1
DA:349,0
DA:350,1
DA:352,1
DA:354,0
DA:355,0
DA:357,1
DA:359,0
DA:360,0
DA:362,1
DA:364,1
DA:365,1
LF:152
LH:138
FN:40,41,UserRepositoryImpl.__init__
FNDA:1,UserRepositoryImpl.__init__
FN:43,73,UserRepositoryImpl.get_by_email
FNDA:1,UserRepositoryImpl.get_by_email
FN:75,82,UserRepositoryImpl.get_user
FNDA:0,UserRepositoryImpl.get_user
FN:84,92,UserRepositoryImpl._get_validated_user_and_contacts
FNDA:1,UserRepositoryImpl._get_validated_user_and_contacts
FN:94,125,UserRepositoryImpl._aggregate_contact_details
FNDA:1,UserRepositoryImpl._aggregate_contact_details
FN:127,143,UserRepositoryImpl._aggregate_relations
FNDA:1,UserRepositoryImpl._aggregate_relations
FN:145,154,UserRepositoryImpl._get_user_by_email
FNDA:1,UserRepositoryImpl._get_user_by_email
FN:156,241,UserRepositoryImpl._get_relations
FNDA:1,UserRepositoryImpl._get_relations
FN:243,305,UserRepositoryImpl._get_relation_sell_to_attributes
FNDA:1,UserRepositoryImpl._get_relation_sell_to_attributes
FN:307,312,UserRepositoryImpl._get_external_links
FNDA:1,UserRepositoryImpl._get_external_links
FN:314,344,UserRepositoryImpl._get_related_code
FNDA:1,UserRepositoryImpl._get_related_code
FN:346,350,UserRepositoryImpl._get_entity_by_code
FNDA:1,UserRepositoryImpl._get_entity_by_code
FN:352,355,UserRepositoryImpl._get_language_for_contact
FNDA:0,UserRepositoryImpl._get_language_for_contact
FN:357,360,UserRepositoryImpl._get_country_for_contact
FNDA:0,UserRepositoryImpl._get_country_for_contact
FN:362,365,UserRepositoryImpl._get_currency_for_contact
FNDA:1,UserRepositoryImpl._get_currency_for_contact
FNF:15
FNH:12
end_of_record
SF:src/shared/__init__.py
end_of_record
SF:src/shared/db.py
DA:2,1
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:10,1
DA:11,1
DA:14,1
DA:16,1
DA:19,1
DA:23,1
DA:24,1
DA:25,1
DA:27,1
DA:30,1
DA:34,1
LF:16
LH:16
FN:19,27,get_db_session
FNDA:1,get_db_session
FN:30,34,create_tables
FNDA:1,create_tables
FNF:2
FNH:2
end_of_record
SF:src/shared/exceptions.py
DA:1,1
DA:4,1
DA:5,1
DA:6,1
DA:9,1
DA:12,1
DA:13,1
DA:14,1
DA:15,1
DA:17,1
DA:18,0
DA:21,1
DA:22,1
DA:23,1
DA:26,1
DA:27,1
DA:28,1
DA:31,1
DA:32,1
DA:33,0
DA:36,1
DA:37,1
DA:38,0
DA:41,1
DA:44,1
DA:45,1
DA:46,1
DA:49,1
DA:50,1
DA:55,1
DA:56,1
DA:57,1
DA:58,1
DA:60,0
DA:62,1
DA:63,1
DA:64,1
DA:67,1
DA:68,1
DA:69,1
DA:70,1
DA:73,1
DA:74,1
DA:75,1
DA:76,1
DA:79,1
DA:80,1
DA:81,1
DA:82,1
LF:49
LH:45
FN:12,15,HolmatroBaseError.__init__
FNDA:1,HolmatroBaseError.__init__
FN:17,18,HolmatroBaseError.as_response
FNDA:0,HolmatroBaseError.as_response
FN:22,23,HolmatroNotFoundError.__init__
FNDA:1,HolmatroNotFoundError.__init__
FN:27,28,HolmatroBadRequestError.__init__
FNDA:1,HolmatroBadRequestError.__init__
FN:32,33,HolmatroConflictError.__init__
FNDA:0,HolmatroConflictError.__init__
FN:37,38,HolmatroTokenError.__init__
FNDA:0,HolmatroTokenError.__init__
FN:44,46,UserNotFoundError.__init__
FNDA:1,UserNotFoundError.__init__
FN:50,64,ProductNotFoundError.__init__
FNDA:1,ProductNotFoundError.__init__
FN:68,70,ProductPriceNotFoundError.__init__
FNDA:1,ProductPriceNotFoundError.__init__
FN:74,76,ProductAvailabilityNotFoundError.__init__
FNDA:1,ProductAvailabilityNotFoundError.__init__
FN:80,82,UserAccessDeniedError.__init__
FNDA:1,UserAccessDeniedError.__init__
FNF:11
FNH:8
end_of_record
SF:src/shared/mappers/product_availability_mapper.py
DA:1,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:11,1
DA:12,1
DA:13,1
DA:15,1
LF:9
LH:9
FN:6,15,ProductAvailabilityMapper.to_product_availability
FNDA:1,ProductAvailabilityMapper.to_product_availability
FNF:1
FNH:1
end_of_record
SF:src/shared/mappers/product_price_mapper.py
DA:1,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:11,1
DA:12,1
DA:13,1
DA:15,1
LF:9
LH:9
FN:6,15,ProductPriceMapper.to_product_price
FNDA:1,ProductPriceMapper.to_product_price
FNF:1
FNH:1
end_of_record
SF:src/shared/mappers/product_sales_info_mapper.py
DA:1,1
DA:2,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:12,1
DA:13,1
DA:14,1
DA:16,1
DA:18,1
DA:19,1
DA:21,1
LF:13
LH:13
FN:7,16,ProductSalesInfoMapper.to_product_sales_info
FNDA:1,ProductSalesInfoMapper.to_product_sales_info
FN:19,23,ProductSalesInfoMapper.to_product_sales_info_list
FNDA:1,ProductSalesInfoMapper.to_product_sales_info_list
FNF:2
FNH:2
end_of_record
SF:src/shared/mappers/user_mapper.py
DA:1,1
DA:2,1
DA:3,1
DA:6,1
DA:7,1
DA:8,1
DA:12,1
LF:7
LH:7
FN:8,27,UserMapper.to_user
FNDA:1,UserMapper.to_user
FNF:1
FNH:1
end_of_record
SF:src/shared/settings.py
DA:1,1
DA:2,1
DA:3,1
DA:5,1
DA:6,1
DA:7,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:14,1
DA:17,1
DA:18,1
DA:20,1
DA:21,1
DA:22,1
DA:23,1
DA:24,1
DA:25,1
DA:26,1
DA:29,1
DA:32,1
DA:33,1
DA:34,1
DA:35,1
DA:37,1
DA:39,1
DA:40,1
DA:41,1
DA:43,1
DA:44,1
DA:45,1
DA:46,1
DA:50,1
DA:52,1
DA:53,1
DA:54,0
LF:37
LH:36
FN:40,41,Settings.mssql_trust_server_certificate
FNDA:1,Settings.mssql_trust_server_certificate
FN:44,50,Settings.sqlalchemy_database_uri
FNDA:1,Settings.sqlalchemy_database_uri
FN:53,54,Settings.sentry_enabled
FNDA:0,Settings.sentry_enabled
FNF:3
FNH:2
end_of_record
SF:src/shared/utils/__init__.py
end_of_record
