.PHONY: run fix qa test dev security check-migrations sleep

run:
	@echo "Run migrations and start application..."
	@poetry run python -m src.pre_start
	@poetry run alembic upgrade head
	@poetry run python -m src.main

fix:
	@echo "Cleaning up code..."
	@poetry run autoflake .
	@poetry run black .
	@poetry run isort .
	@poetry run mypy . --explicit-package-base
	@echo "Code cleanup completed successfully"

qa:
	@echo "Running quality checks on code..."
	@poetry run flake8
	@poetry run mypy --explicit-package-bases .
	@echo "Quality checks completed successfully"

test:
	@echo "Running tests..."
	@poetry run pytest --cov=./src --cov-report=term-missing:skip-covered --cov-report=lcov:coverage.info -n auto --dist loadfile --random-order --log-level ERROR --junitxml=test-results.xml
	@echo "Tests completed successfully"

dev:
	@echo "Starting the fastapi server and database migrations..."
	@poetry run alembic upgrade head
	@poetry run python -m src.main

security:
	@echo "Running security checks..."
	@poetry run bandit -c pyproject.toml -r .
	@echo "Security checks completed successfully"

check-migrations:
	@echo "Checking for unapplied migrations..."
	@poetry run alembic upgrade head
	@poetry run alembic-autogen-check
	@echo "Migrations checked successfully"

sleep:
	@sleep 3600
