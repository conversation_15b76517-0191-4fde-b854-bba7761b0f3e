from uuid import UUID

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import UniqueConstraint
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class ContactAttributes(TimestampMixin, Base):
    __tablename__ = "contact_attributes"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    contact_code: Mapped[str] = mapped_column(ForeignKey("contacts.code"), nullable=False)
    attribute_code: Mapped[str] = mapped_column(ForeignKey("attributes.code"), nullable=False)
    attribute_value: Mapped[str] = mapped_column(Unicode(250), nullable=False)

    # Add unique constraint and index matching original schema
    __table_args__ = (
        UniqueConstraint(
            "contact_code",
            "attribute_code",
            name="uix_contact_attributes_contact_attribute",
        ),
        Index("contact_attributes_IX01", "contact_code", "attribute_code"),
    )

    # Relationships
    contact = relationship("Contacts", back_populates="contact_attributes")
    attribute = relationship("Attributes", back_populates="contact_attributes")
