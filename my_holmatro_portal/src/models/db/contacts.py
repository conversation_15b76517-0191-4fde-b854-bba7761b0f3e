from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Contacts(TimestampMixin, Base):
    __tablename__ = "contacts"

    code: Mapped[str] = mapped_column(Unicode(15), primary_key=True, nullable=False)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)
    language_code: Mapped[str] = mapped_column(ForeignKey("languages.code"), nullable=True)
    country_code: Mapped[str] = mapped_column(ForeignKey("countries.code"), nullable=True)
    email: Mapped[str] = mapped_column(Unicode(320), nullable=True)

    # Add index matching SQL schema's index
    __table_args__ = (Index("contacts_IX01", "code"),)

    # Relationships - using strings for class names to avoid circular dependencies
    language = relationship("Languages", foreign_keys=[language_code], back_populates="contacts")
    country = relationship("Countries", foreign_keys=[country_code], back_populates="contacts")
    contact_attributes = relationship("ContactAttributes", back_populates="contact")
    user = relationship(
        "Users", foreign_keys=[email], back_populates="contacts", primaryjoin="Contacts.email==Users.email"
    )

    # Use string references to avoid circular dependencies
    relations_sell_to = relationship(
        "RelationsSellTo",
        secondary="contact_relation_sell_to",
        back_populates="contacts",
    )

    # Use string references to avoid circular dependencies
    relations = relationship(
        "Relations",
        secondary="contact_relation_sell_to",
        primaryjoin="Contacts.code==ContactRelationSellTo.contact_code",
        secondaryjoin="and_(ContactRelationSellTo.relation_sell_to_code==RelationsSellTo.relation_code, "
        "RelationsSellTo.relation_code==Relations.code)",
        viewonly=True,
    )
