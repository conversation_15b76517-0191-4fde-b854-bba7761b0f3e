from uuid import UUID

from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Relations(TimestampMixin, Base):
    __tablename__ = "relations"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(15), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)
    language_code: Mapped[str] = mapped_column(ForeignKey("languages.code"), nullable=True)
    currency_code: Mapped[str] = mapped_column(ForeignKey("currencies.code"), nullable=True)
    country_code: Mapped[str] = mapped_column(ForeignKey("countries.code"), nullable=True)

    # Add index matching SQL schema's index
    __table_args__ = (Index("relations_IX01", "code"),)

    # Relationships
    language = relationship("Languages", back_populates="relations")
    currency = relationship("Currencies", back_populates="relations")
    country = relationship("Countries", foreign_keys=[country_code])
    relation_attributes = relationship("RelationAttributes", back_populates="relation")
    relations_sell_to = relationship(
        "RelationsSellTo",
        foreign_keys="[RelationsSellTo.relation_code]",
        back_populates="relation",
    )
    pricelist_relations = relationship(
        "RelationsSellTo",
        foreign_keys="[RelationsSellTo.relation_code_for_pricelist]",
        overlaps="relation_for_pricelist",
    )
