from uuid import UUID

from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import UniqueConstraint
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class ContactRelationSellTo(TimestampMixin, Base):
    __tablename__ = "contact_relation_sell_to"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    contact_code: Mapped[str] = mapped_column(ForeignKey("contacts.code"), nullable=False)
    relation_sell_to_code: Mapped[str] = mapped_column(ForeignKey("relations_sell_to.relation_code"), nullable=False)

    # Add unique constraint and index matching original schema
    __table_args__ = (
        UniqueConstraint("contact_code", "relation_sell_to_code", name="uix_contact_relation_sell_to"),
        Index("contact_relation_sell_to_IX01", "contact_code", "relation_sell_to_code"),
    )
