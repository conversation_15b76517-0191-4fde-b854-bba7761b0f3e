from uuid import UUID

from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Employees(TimestampMixin, Base):
    __tablename__ = "employees"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(15), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)
    email: Mapped[str] = mapped_column(Unicode(320), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("employees_IX01", "code"),)

    # Relationships
    int_sales_rep_relations = relationship(
        "RelationsSellTo", back_populates="int_sales_rep", foreign_keys="[RelationsSellTo.int_sales_rep_employee_code]"
    )
    ext_sales_rep_relations = relationship(
        "RelationsSellTo", back_populates="ext_sales_rep", foreign_keys="[RelationsSellTo.ext_sales_rep_employee_code]"
    )
