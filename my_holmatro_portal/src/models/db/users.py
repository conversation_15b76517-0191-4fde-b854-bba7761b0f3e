from uuid import UUID

from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import foreign
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Users(TimestampMixin, Base):
    __tablename__ = "users"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    email: Mapped[str] = mapped_column(Unicode(320), nullable=False, unique=True)
    portal_access: Mapped[bool] = mapped_column(nullable=False, default=False)

    # Create indexes matching SQL schema
    __table_args__ = (
        Index("ix_users_email", "email"),
        Index("users_IX01", "email"),
    )

    # Direct relationship with contacts through email
    contacts = relationship("Contacts", back_populates="user", primaryjoin="Users.email==foreign(Contacts.email)")
