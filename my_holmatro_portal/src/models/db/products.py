from uuid import UUID

from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Products(TimestampMixin, Base):
    __tablename__ = "products"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(50), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("products_IX01", "code"),)

    # Relationships
    availability = relationship(
        "ProductAvailability",
        primaryjoin="Products.code == ProductAvailability.product_code",
        back_populates="product",
        cascade="all, delete-orphan",
    )

    sales_prices = relationship(
        "ProductSalesPrices",
        primaryjoin="Products.code == ProductSalesPrices.product_code",
        back_populates="product",
        cascade="all, delete-orphan",
    )
