from uuid import UUID

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import UniqueConstraint
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class RelationAttributes(TimestampMixin, Base):
    __tablename__ = "relation_attributes"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    relation_code: Mapped[str] = mapped_column(ForeignKey("relations.code"), nullable=False)
    attribute_code: Mapped[str] = mapped_column(ForeignKey("attributes.code"), nullable=False)
    attribute_value: Mapped[str] = mapped_column(Unicode(250), nullable=False)

    # Add unique constraint and index matching original schema
    __table_args__ = (
        UniqueConstraint("relation_code", "attribute_code", name="uix_relation_attributes_relation_attribute"),
        Index("relation_attributes_IX01", "relation_code", "attribute_code"),
    )

    # Relationships
    relation = relationship("Relations", back_populates="relation_attributes")
    attribute = relationship("Attributes", back_populates="relation_attributes")
