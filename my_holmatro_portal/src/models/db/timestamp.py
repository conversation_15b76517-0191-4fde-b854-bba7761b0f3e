from datetime import datetime

from sqlalchemy import func
from sqlalchemy.dialects.mssql import DATETIME2
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column


class TimestampMixin(object):
    created_at: Mapped[datetime] = mapped_column(DATETIME2, nullable=False, server_default=func.sysutcdatetime())
    updated_at: Mapped[datetime] = mapped_column(
        DATETIME2, nullable=False, server_default=func.sysutcdatetime(), server_onupdate=func.sysutcdatetime()
    )
