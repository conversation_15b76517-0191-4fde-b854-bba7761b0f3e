from uuid import UUID

from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Units(TimestampMixin, Base):
    __tablename__ = "units"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(15), nullable=False, unique=True)
    code_iso: Mapped[str] = mapped_column(Unicode(15), nullable=False)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("units_IX01", "code"),)

    # Relationships
    product_availability = relationship("ProductAvailability", back_populates="unit")
    product_salesprices = relationship("ProductSalesPrices", back_populates="unit")
