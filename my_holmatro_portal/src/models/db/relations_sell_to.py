from uuid import UUID

from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class RelationsSellTo(TimestampMixin, Base):
    __tablename__ = "relations_sell_to"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    relation_code: Mapped[str] = mapped_column(ForeignKey("relations.code"), nullable=False, unique=True)
    language_code: Mapped[str] = mapped_column(ForeignKey("languages.code"), nullable=True)
    currency_code: Mapped[str] = mapped_column(ForeignKey("currencies.code"), nullable=True)
    country_code: Mapped[str] = mapped_column(ForeignKey("countries.code"), nullable=True)
    int_sales_rep_employee_code: Mapped[str] = mapped_column(ForeignKey("employees.code"), nullable=True)
    ext_sales_rep_employee_code: Mapped[str] = mapped_column(ForeignKey("employees.code"), nullable=True)
    company_code: Mapped[str] = mapped_column(ForeignKey("companies.code"), nullable=True)
    pricelist_code: Mapped[str] = mapped_column(ForeignKey("pricelists.code"), nullable=True)
    relation_code_for_pricelist: Mapped[str] = mapped_column(ForeignKey("relations.code"), nullable=True)
    status: Mapped[str] = mapped_column(Unicode(50), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("relations_sell_to_IX01", "relation_code"),)

    # Clean up relationships - remove duplicates
    relation = relationship("Relations", foreign_keys=[relation_code], back_populates="relations_sell_to")
    language = relationship("Languages", foreign_keys=[language_code])
    currency = relationship("Currencies", foreign_keys=[currency_code])
    country = relationship("Countries", foreign_keys=[country_code], back_populates="relations_sell_to")

    # Sales representatives
    int_sales_rep = relationship(
        "Employees",
        foreign_keys=[int_sales_rep_employee_code],
        back_populates="int_sales_rep_relations",
    )
    ext_sales_rep = relationship(
        "Employees",
        foreign_keys=[ext_sales_rep_employee_code],
        back_populates="ext_sales_rep_relations",
    )

    # Company and pricing
    company = relationship("Companies", foreign_keys=[company_code], back_populates="relations_sell_to")
    pricelist = relationship("Pricelists", foreign_keys=[pricelist_code])
    relation_for_pricelist = relationship("Relations", foreign_keys=[relation_code_for_pricelist])
    product_salesprices = relationship("ProductSalesPrices", back_populates="relation_sell_to")

    # Contact relationships
    contacts = relationship(
        "Contacts",
        secondary="contact_relation_sell_to",
        back_populates="relations_sell_to",
    )
