from uuid import UUID

from sqlalchemy import BIGINT
from sqlalchemy import Float
from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy import Index
from sqlalchemy import UniqueConstraint
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class ProductAvailability(TimestampMixin, Base):
    __tablename__ = "product_availability"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    product_code: Mapped[str] = mapped_column(ForeignKey("products.code"), nullable=False)
    company_code: Mapped[str] = mapped_column(ForeignKey("companies.code"), nullable=False)
    warehouse_code: Mapped[str] = mapped_column(ForeignKey("warehouses.code"), nullable=False)
    unit_code: Mapped[str] = mapped_column(ForeignKey("units.code"), nullable=False)
    stock: Mapped[float] = mapped_column(Float, nullable=True)
    dalt: Mapped[int] = mapped_column(BIGINT, nullable=True)

    # Add unique constraint matching original schema
    __table_args__ = (
        UniqueConstraint(
            "product_code", "company_code", "warehouse_code", "unit_code", name="uix_product_availability"
        ),
        Index("product_availability_v2_IX01", "product_code", "company_code", "warehouse_code", "unit_code"),
    )

    # Relationships
    # Core relationships
    company = relationship("Companies", back_populates="product_availability")
    warehouse = relationship("Warehouses", back_populates="product_availability")
    unit = relationship("Units", back_populates="product_availability")

    # Product relationship
    product = relationship("Products", back_populates="availability")
