from datetime import datetime
from uuid import UUID

from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import UniqueConstraint
from sqlalchemy import func
from sqlalchemy.dialects.mssql import DATETIME2
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class ProductSalesPrices(TimestampMixin, Base):
    __tablename__ = "product_salesprices"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    relation_sell_to_code: Mapped[str] = mapped_column(ForeignKey("relations_sell_to.relation_code"), nullable=True)
    pricelist_code: Mapped[str] = mapped_column(ForeignKey("pricelists.code"), nullable=False)
    currency_code: Mapped[str] = mapped_column(ForeignKey("currencies.code"), nullable=False)
    product_code: Mapped[str] = mapped_column(ForeignKey("products.code"), nullable=False)
    unit_code: Mapped[str] = mapped_column(ForeignKey("units.code"), nullable=False)
    min_quantity: Mapped[float] = mapped_column(nullable=True)
    gross_price: Mapped[float] = mapped_column(nullable=True)
    discount: Mapped[float] = mapped_column(nullable=True)
    nett_price: Mapped[float] = mapped_column(nullable=True)
    valid_from: Mapped[datetime] = mapped_column(DATETIME2, nullable=True)
    valid_until: Mapped[datetime] = mapped_column(DATETIME2, nullable=True)

    # Add unique constraint matching original schema
    __table_args__ = (
        UniqueConstraint(
            "relation_sell_to_code",
            "pricelist_code",
            "currency_code",
            "product_code",
            "unit_code",
            "min_quantity",
            "valid_from",
            name="uix_product_salesprices",
        ),
        Index(
            "product_salesprices_v2_IX01",
            "relation_sell_to_code",
            "pricelist_code",
            "currency_code",
            "product_code",
            "unit_code",
            "min_quantity",
            "valid_from",
        ),
    )

    relation_sell_to = relationship("RelationsSellTo", back_populates="product_salesprices")
    pricelist = relationship("Pricelists", back_populates="product_salesprices")
    currency = relationship("Currencies", back_populates="product_salesprices")
    unit = relationship("Units", back_populates="product_salesprices")
    product = relationship("Products", back_populates="sales_prices")
