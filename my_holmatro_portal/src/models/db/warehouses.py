from uuid import UUID

from sqlalchemy import ForeignKey
from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Warehouses(TimestampMixin, Base):
    __tablename__ = "warehouses"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(15), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)
    company_code: Mapped[str] = mapped_column(ForeignKey("companies.code"), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("warehouses_IX01", "code"),)

    # Relationships
    company = relationship("Companies", back_populates="warehouses")
    product_availability = relationship("ProductAvailability", back_populates="warehouse")
