from uuid import UUID

from sqlalchemy import Index
from sqlalchemy import Unicode
from sqlalchemy import func
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column
from sqlalchemy.orm import relationship
from sqlalchemy_utils import UUIDType
from src.models.db.base import Base
from src.models.db.timestamp import TimestampMixin


class Languages(TimestampMixin, Base):
    __tablename__ = "languages"

    id: Mapped[UUID] = mapped_column(UUIDType(binary=False), primary_key=True, server_default=func.newid())
    code: Mapped[str] = mapped_column(Unicode(15), nullable=False, unique=True)
    code_iso: Mapped[str] = mapped_column(Unicode(15), nullable=False)
    name: Mapped[str] = mapped_column(Unicode(250), nullable=False)

    # Add index matching SQL schema's index
    __table_args__ = (Index("languages_IX01", "code"),)

    # Relationships
    relations = relationship("Relations", back_populates="language")
    contacts = relationship("Contacts", back_populates="language")
