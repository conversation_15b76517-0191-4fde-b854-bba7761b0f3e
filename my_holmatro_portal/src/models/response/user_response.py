from typing import List

from pydantic import BaseModel
from pydantic import Field
from src.models.schemas.user import Attribute
from src.models.schemas.user import ExternalLink
from src.models.schemas.user import Relation
from src.models.schemas.user import User


class UserResponse(BaseModel):
    user: User = Field(..., alias="User")
    relations: List[Relation] = Field(..., alias="Relations")
    attributes: List[Attribute] = Field(..., alias="Attributes")
    external_links: List[ExternalLink] = Field(..., alias="ExternalLinks")

    model_config = {"populate_by_name": True}
