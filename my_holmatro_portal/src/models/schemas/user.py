from dataclasses import dataclass
from datetime import datetime
from types import SimpleNamespace
from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from src.models.db.countries import Countries
from src.models.db.languages import Languages
from src.models.db.users import Users


class Relation(BaseModel):
    company_id: Optional[str] = Field(None, alias="CompanyId")
    relation_id: Optional[str] = Field(None, alias="RelationId")
    relation_name: Optional[str] = Field(None, alias="RelationName")
    int_sales_rep_id: Optional[str] = Field(None, alias="IntSalesRepId")
    int_sales_rep_name: Optional[str] = Field(None, alias="IntSalesRepName")
    int_sales_rep_email: Optional[str] = Field(None, alias="IntSalesRepEmail")
    ext_sales_rep_id: Optional[str] = Field(None, alias="ExtSalesRepId")
    ext_sales_rep_name: Optional[str] = Field(None, alias="ExtSalesRepName")
    ext_sales_rep_email: Optional[str] = Field(None, alias="ExtSalesRepEmail")

    model_config = {"populate_by_name": True}


class Attribute(BaseModel):
    sf_proposition: str = Field("Holmatro Partner Portal", alias="SfProposition")
    sf_category: str = Field(..., alias="SfCategory")
    sf_sub_category: Optional[str] = Field(None, alias="SfSubCategory")

    model_config = {"populate_by_name": True}


class ExternalLink(BaseModel):
    link: str = Field(..., alias="Link")
    url: str = Field(..., alias="Url")

    model_config = {"populate_by_name": True}


class User(BaseModel):
    email_address: str = Field(..., alias="EmailAddress")
    contact_id: str = Field(..., alias="ContactId")
    contact_name: str = Field(..., alias="ContactName")
    relation_name: str = Field(..., alias="RelationName")
    country_id: Optional[str] = Field(None, alias="CountryId")
    language_id: Optional[str] = Field(None, alias="LanguageId")
    currency_id: Optional[str] = Field(None, alias="CurrencyId")
    created_ts_utc: datetime = Field(..., alias="CreatedTsUtc")
    updated_ts_utc: datetime = Field(..., alias="UpdatedTsUtc")

    model_config = {"populate_by_name": True}


@dataclass
class UserAggregate:
    """A container for all data needed to build a user response."""

    user_db: Users
    primary_contact: SimpleNamespace
    all_relations: List[Relation]
    primary_relation_name: str | None
    primary_currency_id: str | None
    country: Countries | None
    language: Languages | None
    attributes: List[Attribute]
    external_links: List[ExternalLink]
