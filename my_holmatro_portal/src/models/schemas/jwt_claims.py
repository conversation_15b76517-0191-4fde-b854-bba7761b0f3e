from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import EmailStr
from pydantic import Field


class JWTClaims(BaseModel):
    aud: str  # Audience
    iss: str  # Issuer
    sub: str  # Subject
    emails: List[EmailStr] = Field(default_factory=list)  # List of email addresses
    exp: Optional[int] = None  # Expiration time (optional)
    iat: Optional[int] = None  # Issued at time (optional)
    nbf: Optional[int] = None  # Not before time (optional)
    jti: Optional[str] = None  # JWT ID (optional)

    @property
    def email(self) -> EmailStr:
        """
        Returns the first email address from the list of emails.
        If no emails are present, returns None.
        """
        if not self.emails or not self.emails[0]:
            raise ValueError("No email found in JWT claims")
        return self.emails[0]
