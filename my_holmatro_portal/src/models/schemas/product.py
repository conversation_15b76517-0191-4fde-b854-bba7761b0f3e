from dataclasses import dataclass
from datetime import datetime
from typing import Any
from typing import Optional

from pydantic import BaseModel
from pydantic import Field


class ProductSalesInfo(BaseModel):
    article_no: str = Field(..., alias="ArticleNo")
    gross_price: float = Field(..., alias="GrossPrice")
    nett_price: float = Field(..., alias="NettPrice")
    discount_percentage: float = Field(..., alias="Discount%")
    currency: str = Field(..., alias="Currency")
    unit: str = Field(..., alias="Unit")
    stock: float = Field(..., alias="Stock")
    restock_days: int = Field(..., alias="RestockDays")
    created_ts_utc: datetime = Field(..., alias="CreatedTsUtc")
    updated_ts_utc: datetime = Field(..., alias="UpdatedTsUtc")

    model_config = {"populate_by_name": True}


class ProductPrice(BaseModel):
    article_no: str = Field(..., alias="ArticleNo")
    gross_price: Optional[float] = Field(None, alias="GrossPrice")
    nett_price: Optional[float] = Field(None, alias="NettPrice")
    discount_percentage: Optional[float] = Field(None, alias="Discount%")
    currency: Optional[str] = Field(None, alias="Currency")
    unit: Optional[str] = Field(None, alias="Unit")
    created_ts_utc: datetime = Field(..., alias="CreatedTsUtc")
    updated_ts_utc: datetime = Field(..., alias="UpdatedTsUtc")

    model_config = {"populate_by_name": True}


class ProductAvailability(BaseModel):
    article_no: str = Field(..., alias="ArticleNo")
    stock: Optional[float] = Field(None, alias="Stock")
    restock_days: Optional[int] = Field(None, alias="RestockDays")
    unit: Optional[str] = Field(None, alias="Unit")
    created_ts_utc: datetime = Field(..., alias="CreatedTsUtc")
    updated_ts_utc: datetime = Field(..., alias="UpdatedTsUtc")

    model_config = {"populate_by_name": True}


@dataclass
class ProductSalesInfoAggregate:
    """A container for the raw data needed to build a ProductSalesInfoResponse."""

    result: Optional[Any]


@dataclass
class ProductPriceAggregate:
    """A container for the raw data needed to build a ProductPrice response."""

    result: Optional[Any]


@dataclass
class ProductAvailabilityAggregate:
    """A container for the raw data needed to build a ProductAvailability response."""

    result: Optional[Any]
