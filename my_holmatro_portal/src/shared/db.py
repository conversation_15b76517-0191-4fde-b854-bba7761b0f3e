# my_holmatro_portal/src/database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from src.shared.settings import Settings

settings = Settings()

# Create SQLAlchemy engine
SQLALCHEMY_DATABASE_URL = settings.sqlalchemy_database_uri
engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_size=10, max_overflow=20, pool_pre_ping=True, pool_recycle=600)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db_session():
    """
    Dependency that provides a database session with automatic cleanup
    """
    database = SessionLocal()
    try:
        yield database
    finally:
        database.close()


def create_tables():
    """
    Initialize database tables (call during application startup)
    """
    Base.metadata.create_all(bind=engine)
