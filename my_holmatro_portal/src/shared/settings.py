from enum import Enum
from typing import Optional
from typing import Union

from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings
from pydantic_settings import SettingsConfigDict


class Stage(str, Enum):
    local = "local"
    test = "test"
    acceptance = "acceptance"
    production = "production"


class Settings(BaseSettings):
    model_config = SettingsConfigDict()

    stage: Stage = Stage.local
    override_harborn_user: Optional[str] = None
    cors_origins: list[Union[str, AnyHttpUrl]] = ["http://localhost:8000", "http://127.0.0.1:8001"]
    jwt_algorithm: str = "RS256"
    jwt_audience: str = "632fd4f4-86f8-4a53-aacc-4dee0de705c3"
    b2c_issuer: str = "https://myhmb2c.b2clogin.com/32fc4a30-cf52-45e5-b6bd-d5f5d43216e0/v2.0/"
    openid_b2c_config_url: str = (
        "https://myhmb2c.b2clogin.com/myhmb2c.onmicrosoft.com/v2.0/.well-known/openid-configuration?p=B2C_1_myhmb2c"
    )
    my_hm_subscription_token: str = ""

    # MySQL
    db_user: str = ""
    db_sa_password: str = ""
    db_server: str = ""
    db_name: str = ""

    sentry_url: Optional[str] = None

    @property
    def mssql_trust_server_certificate(self) -> bool:
        return self.stage == Stage.local

    @property
    def sqlalchemy_database_uri(self) -> str:
        trust_server_certificate = "yes" if self.mssql_trust_server_certificate else "no"
        sql = (
            f"mssql+pyodbc://{self.db_user}:{self.db_sa_password}@{self.db_server}:1433/{self.db_name}?"
            f"driver=ODBC+Driver+18+for+SQL+Server&TrustServerCertificate={trust_server_certificate}"
        )
        return sql

    @property
    def sentry_enabled(self) -> bool:
        return self.sentry_url is not None and self.stage is not Stage.local
