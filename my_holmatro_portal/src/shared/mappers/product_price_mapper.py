from src.models.schemas.product import ProductPrice


class ProductPriceMapper:
    @staticmethod
    def to_product_price(result_row) -> ProductPrice:
        """Map database result row to ProductPrice schema."""
        row_dict = result_row._asdict()

        # Round numeric fields to 2 decimal places
        for field in ["gross_price", "nett_price", "discount_percentage"]:
            if field in row_dict and row_dict[field] is not None:
                row_dict[field] = round(row_dict[field], 2)

        return ProductPrice(**row_dict)
