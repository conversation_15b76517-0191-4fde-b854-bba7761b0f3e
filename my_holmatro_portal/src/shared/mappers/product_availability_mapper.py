from src.models.schemas.product import ProductAvailability


class ProductAvailabilityMapper:
    @staticmethod
    def to_product_availability(result_row) -> ProductAvailability:
        """Map database result row to ProductAvailability schema."""
        row_dict = result_row._asdict()

        # Round numeric fields to 2 decimal places
        for field in ["stock", "restock_days"]:
            if field in row_dict and row_dict[field] is not None:
                row_dict[field] = round(row_dict[field], 2)

        return ProductAvailability(**row_dict)
