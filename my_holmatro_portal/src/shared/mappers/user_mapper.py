from src.models.response.user_response import UserResponse
from src.models.schemas.user import User
from src.models.schemas.user import UserAggregate


class UserMapper:
    @staticmethod
    def to_user(
        user_aggregate: UserAggregate,
    ) -> UserResponse:
        """Map the user response to the User model."""
        return UserResponse(
            user=User(
                email_address=user_aggregate.user_db.email,
                contact_id=user_aggregate.primary_contact.code,
                contact_name=user_aggregate.primary_contact.name,
                relation_name=user_aggregate.primary_relation_name,
                country_id=user_aggregate.country.code if user_aggregate.country else None,
                language_id=user_aggregate.language.code if user_aggregate.language else None,
                currency_id=user_aggregate.primary_currency_id,
                created_ts_utc=user_aggregate.user_db.created_at,
                updated_ts_utc=user_aggregate.user_db.updated_at,
            ),
            relations=user_aggregate.all_relations,
            attributes=user_aggregate.attributes,
            external_links=user_aggregate.external_links,
        )
