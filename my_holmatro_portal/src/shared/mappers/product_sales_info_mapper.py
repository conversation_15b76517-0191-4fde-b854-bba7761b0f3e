from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.product import ProductSalesInfo


class ProductSalesInfoMapper:
    @staticmethod
    def to_product_sales_info(result_row) -> ProductSalesInfo:
        """Map database result row to ProductSalesInfo schema."""
        row_dict = result_row._asdict()

        # Round numeric fields to 2 decimal places
        for field in ["gross_price", "nett_price", "discount_percentage", "stock"]:
            if field in row_dict and row_dict[field] is not None:
                row_dict[field] = round(row_dict[field], 2)

        return ProductSalesInfo(**row_dict)

    @staticmethod
    def to_product_sales_info_list(results) -> ProductSalesInfoResponse:
        """Map list of database result rows to list of ProductSalesInfo schemas."""
        return ProductSalesInfoResponse(
            sales_info=[ProductSalesInfoMapper.to_product_sales_info(row) for row in results]
        )
