from pydantic import BaseModel


class ErrorResponse(BaseModel):
    error_code: str
    message: str | None = None


class HolmatroBaseError(Exception):
    """Base class for all Holmatro exceptions"""

    def __init__(self, error_code: str, message: str):
        super().__init__(message)
        self.error_code = error_code
        self.message = message

    def as_response(self) -> ErrorResponse:
        return ErrorResponse(error_code=self.error_code, message=self.message)


class HolmatroNotFoundError(HolmatroBaseError):
    def __init__(self, message: str = "Resource not found"):
        super().__init__(error_code="NotFound", message=message)


class HolmatroBadRequestError(HolmatroBaseError):
    def __init__(self, message: str = "Bad request") -> None:
        super().__init__(error_code="BadRequest", message=message)


class HolmatroConflictError(HolmatroBaseError):
    def __init__(self, message: str = "Conflict occurred") -> None:
        super().__init__(error_code="Conflict", message=message)


class HolmatroTokenError(HolmatroBaseError):
    def __init__(self, message: str = "Invalid or expired token"):
        super().__init__(error_code="TokenError", message=message)


class UserNotFoundError(HolmatroNotFoundError):
    """Exception raised when a user is not found"""

    def __init__(self, email: str) -> None:
        super().__init__(message=f"User with email '{email}' not found")
        self.error_code = "USER_NOT_FOUND"


class ProductNotFoundError(HolmatroNotFoundError):
    def __init__(
        self,
        article_number: str | None = None,
        article_numbers: list[str] | None = None,
    ):
        if article_number:
            message = f"Product with article number '{article_number}' not found"
        elif article_numbers is not None:
            message = f"No products found for article numbers: {', '.join(article_numbers)}"
        else:
            message = "Product not found"

        super().__init__(message=message)
        self.error_code = "PRODUCT_NOT_FOUND"
        self.error_code = "PRODUCT_NOT_FOUND"


class ProductPriceNotFoundError(HolmatroNotFoundError):
    def __init__(self, article_number: str):
        super().__init__(message=f"Product price for article number '{article_number}' not found")
        self.error_code = "PRODUCT_PRICE_NOT_FOUND"


class ProductAvailabilityNotFoundError(HolmatroNotFoundError):
    def __init__(self, article_number: str):
        super().__init__(message=f"Product availability for article number '{article_number}' not found")
        self.error_code = "PRODUCT_AVAILABILITY_NOT_FOUND"


class UserAccessDeniedError(HolmatroBadRequestError):
    def __init__(self, email: str):
        super().__init__(message=f"User '{email}' does not have portal access")
        self.error_code = "USER_ACCESS_DENIED"
