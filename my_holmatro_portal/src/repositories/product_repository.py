from abc import ABC
from abc import abstractmethod
from typing import List

from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.product import ProductAvailability
from src.models.schemas.product import ProductAvailabilityAggregate
from src.models.schemas.product import ProductPrice
from src.models.schemas.product import ProductPriceAggregate
from src.models.schemas.product import ProductSalesInfoAggregate


class ProductRepository(ABC):
    @abstractmethod
    def get_product_sales_info(self, user_email: str, article_numbers: List[str]) -> ProductSalesInfoAggregate:
        pass

    @abstractmethod
    def get_product_price(self, user_email: str, article_number: str) -> ProductPriceAggregate:
        pass

    @abstractmethod
    def get_product_availability(self, user_email: str, article_number: str) -> ProductAvailabilityAggregate:
        pass
