# import sentry_sdk
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.http_api.exception_handlers import register_exception_handlers
from src.http_api.middleware.auth_middleware import JWTValidationMiddleware
from src.http_api.middleware.user_validation import UserValidationMiddleware
from src.http_api.routers import general
from src.http_api.routers import health
from src.http_api.routers import products
from src.shared.settings import Settings

settings = Settings()


def create_app() -> FastAPI:
    """Creates and configures a new FastAPI application instance."""
    app = FastAPI()

    validation_skip_paths = [
        "/health",
        "/docs",
        "/openapi.json",
        "/redoc",
    ]

    if settings.cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.cors_origins],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    register_exception_handlers(app)
    app.include_router(general.router, tags=["general"], prefix="/general")
    app.include_router(products.router, tags=["products"], prefix="/products")
    app.include_router(health.router, tags=["healthcheck"], prefix="/health", include_in_schema=False)
    app.add_middleware(UserValidationMiddleware, skip_paths=validation_skip_paths)
    app.add_middleware(JWTValidationMiddleware, skip_paths=validation_skip_paths)

    return app


app = create_app()


def main() -> None:
    """
    Create the FastAPI app and run it
    """
    if settings.stage == "local":
        uvicorn.run("src.main:app", host="0.0.0.0", port=8000, reload=True)  # nosec B104
    else:
        uvicorn.run(app, host=["::", "0.0.0.0"], port=8000)  # type: ignore[arg-type] #nosec B104


if __name__ == "__main__":
    main()
