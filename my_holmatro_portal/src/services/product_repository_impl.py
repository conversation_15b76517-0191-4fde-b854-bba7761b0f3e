import datetime
import logging
from typing import List

from sqlalchemy import Float
from sqlalchemy import and_
from sqlalchemy import case
from sqlalchemy import cast
from sqlalchemy import func
from sqlalchemy import literal
from sqlalchemy import or_
from sqlalchemy.sql import select
from src.models.db.contact_relation_sell_to import ContactRelationSellTo
from src.models.db.contacts import Contacts
from src.models.db.currencies import Currencies
from src.models.db.product_availability import ProductAvailability
from src.models.db.product_salesprices import ProductSalesPrices
from src.models.db.products import Products
from src.models.db.relation_attributes import RelationAttributes
from src.models.db.relations_sell_to import RelationsSellTo
from src.models.db.units import Units
from src.models.db.users import Users
from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.product import ProductAvailability as ProductAvailabilitySchema
from src.models.schemas.product import ProductAvailabilityAggregate
from src.models.schemas.product import ProductPrice
from src.models.schemas.product import ProductPriceAggregate
from src.models.schemas.product import ProductSalesInfoAggregate
from src.repositories.product_repository import ProductRepository
from src.shared.exceptions import ProductAvailabilityNotFoundError
from src.shared.exceptions import ProductNotFoundError
from src.shared.exceptions import ProductPriceNotFoundError
from src.shared.mappers.product_availability_mapper import ProductAvailabilityMapper
from src.shared.mappers.product_price_mapper import ProductPriceMapper
from src.shared.mappers.product_sales_info_mapper import ProductSalesInfoMapper

_logging = logging.getLogger(__name__)


class ProductRepositoryImpl(ProductRepository):
    def __init__(self, session):
        self.session = session

    def get_product_sales_info(self, user_email: str, article_numbers: List[str]) -> ProductSalesInfoAggregate:
        try:
            article_numbers = self._clean_article_numbers(article_numbers)
            if not article_numbers:
                raise ProductNotFoundError(article_numbers=article_numbers)

            now = datetime.datetime.now(datetime.timezone.utc)
            contacts_query = self._get_contacts_query(user_email)
            relation_sell_to_query = self._get_relation_sell_to_query(contacts_query)
            pricelists_query = self._get_pricelists_query(relation_sell_to_query)
            prices_query = self._get_prices_query(pricelists_query, article_numbers, now)
            lowest_prices_query = self._get_lowest_prices_query(prices_query)
            lowest_price_ids_query = self._get_lowest_price_ids_query(prices_query, lowest_prices_query)
            stock_query = self._get_stock_query(pricelists_query, article_numbers)
            unit_names_query = self._get_unit_names_query()

            result_query = self._build_final_query(prices_query, lowest_price_ids_query, stock_query, unit_names_query)
            result = self.session.execute(result_query).all()

            if not result:
                raise ProductNotFoundError(article_numbers=article_numbers)

            return result

        except Exception as e:
            _logging.exception(f"Error retrieving product sales info for user '{user_email}': {e}")
            if isinstance(e, ProductNotFoundError):
                raise
            raise ProductNotFoundError(article_numbers=article_numbers) from e

    def get_product_price(self, user_email: str, article_number: str) -> ProductPriceAggregate:
        try:
            now = datetime.datetime.now(datetime.timezone.utc)

            contacts_query = self._get_contacts_query(user_email)
            relation_sell_to_query = self._get_relation_sell_to_query(contacts_query)
            pricelists_query = self._get_pricelists_query(relation_sell_to_query)
            prices_query = self._get_single_product_prices_query(pricelists_query, article_number, now)
            lowest_prices_query = self._get_lowest_prices_query(prices_query)
            lowest_price_ids_query = self._get_lowest_price_ids_query(prices_query, lowest_prices_query)

            final_query = self._build_price_only_query(prices_query, lowest_price_ids_query)
            result = self.session.execute(final_query).first()

            if not result:
                _logging.warning(f"No price found for article number '{article_number}' for user '{user_email}'")
                raise ProductPriceNotFoundError(article_number)

            return result

        except Exception as e:
            _logging.exception(f"Error retrieving product sales price for article number '{article_number}': {e}")
            if isinstance(e, ProductPriceNotFoundError):
                raise
            raise ProductPriceNotFoundError(article_number) from e

    def get_product_availability(self, user_email: str, article_number: str) -> ProductAvailabilityAggregate:
        """Retrieves stock and availability information for a single product."""
        try:
            clean_article_number = article_number.strip()
            if not clean_article_number:
                _logging.warning("No article number provided.")
                raise ProductAvailabilityNotFoundError(article_number)

            final_query = self._get_availability_query(user_email, clean_article_number)
            result = self.session.execute(final_query).first()

            if not result:
                _logging.warning(f"No availability found for article number '{article_number}' for user '{user_email}'")
                raise ProductAvailabilityNotFoundError(article_number)

            return result

        except Exception as e:
            _logging.exception(f"Error retrieving product availability for article number '{article_number}': {e}")
            if isinstance(e, ProductAvailabilityNotFoundError):
                raise
            raise ProductAvailabilityNotFoundError(article_number) from e

    def _get_user_pricelists_query(self, user_email: str):
        contacts_query = self._get_contacts_query(user_email)
        relation_sell_to_query = self._get_relation_sell_to_query(contacts_query)
        return self._get_pricelists_query(relation_sell_to_query)

    def _get_availability_query(self, user_email: str, article_number: str):
        """Builds the complete SQLAlchemy query for product availability, only if user has access (valid price)."""
        now = datetime.datetime.now(datetime.timezone.utc)
        pricelists_query = self._get_user_pricelists_query(user_email)

        prices_query = self._get_prices_query(pricelists_query, [article_number], now)
        lowest_prices_query = self._get_lowest_prices_query(prices_query)
        lowest_price_ids_query = self._get_lowest_price_ids_query(prices_query, lowest_prices_query)
        stock_query = self._get_best_stock_query(pricelists_query, [article_number])
        unit_names_query = self._get_unit_names_query()

        return (
            select(
                func.trim(prices_query.c.product_code).label("article_no"),
                func.coalesce(unit_names_query.c.display_name, prices_query.c.unit_code).label("unit"),
                cast(func.coalesce(stock_query.c.stock, 0.0), Float).label("stock"),
                case(
                    (func.coalesce(stock_query.c.dalt, 999) <= 0, literal(999)),
                    else_=func.coalesce(stock_query.c.dalt, 999),
                ).label("restock_days"),
                prices_query.c.created_ts_utc.label("created_ts_utc"),
                prices_query.c.updated_ts_utc.label("updated_ts_utc"),
            )
            .select_from(prices_query)
            .join(
                lowest_price_ids_query,
                lowest_price_ids_query.c.product_salesprices_id == prices_query.c.product_salesprices_id,
            )
            .join(
                stock_query,
                and_(
                    stock_query.c.email == prices_query.c.email,
                    func.trim(stock_query.c.product_code) == func.trim(prices_query.c.product_code),
                ),
            )
            .outerjoin(
                unit_names_query,
                unit_names_query.c.code == func.coalesce(stock_query.c.unit_code, prices_query.c.unit_code),
            )
            .where(func.trim(prices_query.c.product_code) == article_number.strip())
            .limit(1)
        )

    def _get_best_stock_query(self, pricelists_query, article_numbers):
        """Get the best stock record for each product based on stock (desc) then dalt (asc)."""
        stock_with_row_number = (
            select(
                pricelists_query.c.email,
                ProductAvailability.product_code,
                ProductAvailability.unit_code.label("unit_code"),
                ProductAvailability.stock,
                ProductAvailability.dalt,
                ProductAvailability.created_at.label("created_ts_utc"),
                ProductAvailability.updated_at.label("updated_ts_utc"),
                func.row_number()
                .over(
                    partition_by=[
                        pricelists_query.c.email,
                        ProductAvailability.product_code,
                    ],
                    order_by=[
                        ProductAvailability.stock.desc(),
                        ProductAvailability.dalt.asc(),
                    ],
                )
                .label("row_num"),
            )
            .select_from(pricelists_query)
            .join(
                ProductAvailability,
                ProductAvailability.company_code == pricelists_query.c.company_code,
            )
            .where(func.trim(ProductAvailability.product_code).in_([code.strip() for code in article_numbers]))
            .subquery()
        )

        return (
            select(
                stock_with_row_number.c.email,
                stock_with_row_number.c.product_code,
                stock_with_row_number.c.unit_code,
                stock_with_row_number.c.stock,
                stock_with_row_number.c.dalt,
                stock_with_row_number.c.created_ts_utc,
                stock_with_row_number.c.updated_ts_utc,
            )
            .select_from(stock_with_row_number)
            .where(stock_with_row_number.c.row_num == 1)
            .subquery()
        )

    def _is_valid_user(self, user_email: str) -> Users:
        user = self.session.query(Users).filter(Users.email == user_email, Users.portal_access).first()
        return user

    def _clean_article_numbers(self, article_numbers: List[str]) -> List[str]:
        return [code.strip() for code in article_numbers if code and code.strip()]

    def _get_contacts_query(self, user_email: str):
        return (
            select(
                Contacts.email,
                Contacts.code.label("contact_code"),
            )
            .where(Contacts.email == user_email)
            .subquery()
        )

    def _get_relation_sell_to_query(self, contacts_query):
        """
        A user can be a contact with multiple relations, but some of those relations might not be valid for the portal.
        We filter out relations that do not have the required attributes.
        Specifically, we look for relations that have both a category and sub-category attribute set to "JA".
        This ensures we only return relations that are relevant for the portal.
        """
        category_relations = (
            self.session.query(RelationAttributes.relation_code)
            .filter(
                RelationAttributes.attribute_code.like("C0004C%"),
                RelationAttributes.attribute_value.ilike("JA"),
            )
            .distinct()
        )
        sub_category_relations = (
            self.session.query(RelationAttributes.relation_code)
            .filter(
                RelationAttributes.attribute_code.like("C0004S%"),
                RelationAttributes.attribute_value.ilike("JA"),
            )
            .distinct()
        )
        valid_relation_codes = set(r[0] for r in category_relations.all()) & set(
            r[0] for r in sub_category_relations.all()
        )
        return (
            select(contacts_query.c.email, ContactRelationSellTo.relation_sell_to_code)
            .select_from(ContactRelationSellTo)
            .join(
                contacts_query,
                ContactRelationSellTo.contact_code == contacts_query.c.contact_code,
            )
            .where(ContactRelationSellTo.relation_sell_to_code.in_(valid_relation_codes))
            .subquery()
        )

    def _get_pricelists_query(self, relation_sell_to_query):
        return (
            select(
                relation_sell_to_query.c.email,
                Currencies.code.label("currency_code"),
                RelationsSellTo.company_code,
                func.coalesce(RelationsSellTo.relation_code_for_pricelist, RelationsSellTo.relation_code).label(
                    "relation_code_for_pricelist"
                ),
                RelationsSellTo.pricelist_code,
            )
            .select_from(relation_sell_to_query)
            .join(
                RelationsSellTo,
                RelationsSellTo.relation_code == relation_sell_to_query.c.relation_sell_to_code,
            )
            .outerjoin(Currencies, Currencies.code == RelationsSellTo.currency_code)
            .distinct()
            .subquery()
        )

    def _get_prices_query(self, pricelists_query, article_numbers, now):
        return (
            select(
                pricelists_query.c.email,
                pricelists_query.c.currency_code,
                Products.code.label("product_code"),
                ProductSalesPrices.unit_code.label("unit_code"),
                cast(ProductSalesPrices.gross_price, Float).label("gross_price"),
                cast(ProductSalesPrices.discount, Float).label("discount"),
                cast(ProductSalesPrices.nett_price, Float).label("nett_price"),
                ProductSalesPrices.id.label("product_salesprices_id"),
                ProductSalesPrices.created_at.label("created_ts_utc"),
                ProductSalesPrices.updated_at.label("updated_ts_utc"),
                func.coalesce(Units.name, Units.code, ProductSalesPrices.unit_code).label("unit_display_name"),
            )
            .select_from(pricelists_query)
            .join(
                ProductSalesPrices,
                and_(
                    or_(
                        func.trim(ProductSalesPrices.relation_sell_to_code)
                        == func.trim(pricelists_query.c.relation_code_for_pricelist),
                        func.trim(ProductSalesPrices.relation_sell_to_code) == "",
                    ),
                    ProductSalesPrices.pricelist_code == pricelists_query.c.pricelist_code,
                    ProductSalesPrices.currency_code == pricelists_query.c.currency_code,
                    (ProductSalesPrices.valid_from.is_(None) | (ProductSalesPrices.valid_from <= now)),
                    (ProductSalesPrices.valid_until.is_(None) | (ProductSalesPrices.valid_until >= now)),
                    ProductSalesPrices.nett_price > 0.0,
                    ProductSalesPrices.gross_price > 0.0,
                ),
            )
            .join(Products, func.trim(Products.code) == func.trim(ProductSalesPrices.product_code))
            .outerjoin(Units, Units.code == ProductSalesPrices.unit_code)
            .where(func.trim(Products.code).in_([code.strip() for code in article_numbers]))
            .distinct()
            .subquery()
        )

    def _get_lowest_prices_query(self, prices_query):
        return (
            select(
                prices_query.c.email,
                prices_query.c.currency_code,
                prices_query.c.product_code,
                func.min(prices_query.c.nett_price).label("nett_price"),
            )
            .select_from(prices_query)
            .group_by(
                prices_query.c.email,
                prices_query.c.currency_code,
                prices_query.c.product_code,
            )
            .subquery()
        )

    def _get_lowest_price_ids_query(self, prices_query, lowest_prices_query):
        return (
            select(
                lowest_prices_query.c.email,
                lowest_prices_query.c.currency_code,
                lowest_prices_query.c.product_code,
                func.min(prices_query.c.product_salesprices_id).label("product_salesprices_id"),
            )
            .select_from(lowest_prices_query)
            .join(
                prices_query,
                and_(
                    prices_query.c.email == lowest_prices_query.c.email,
                    prices_query.c.currency_code == lowest_prices_query.c.currency_code,
                    prices_query.c.product_code == lowest_prices_query.c.product_code,
                    prices_query.c.nett_price == lowest_prices_query.c.nett_price,
                ),
            )
            .group_by(
                lowest_prices_query.c.email,
                lowest_prices_query.c.currency_code,
                lowest_prices_query.c.product_code,
            )
            .subquery()
        )

    def _get_stock_query(self, pricelists_query, article_numbers):
        return (
            select(
                pricelists_query.c.email,
                ProductAvailability.product_code,
                ProductAvailability.unit_code.label("unit_code"),
                func.max(ProductAvailability.stock).label("stock"),
                func.min(ProductAvailability.dalt).label("dalt"),
                func.max(ProductAvailability.created_at).label("created_ts_utc"),
                func.max(ProductAvailability.updated_at).label("updated_ts_utc"),
            )
            .select_from(pricelists_query)
            .join(
                ProductAvailability,
                ProductAvailability.company_code == pricelists_query.c.company_code,
            )
            .where(func.trim(ProductAvailability.product_code).in_([code.strip() for code in article_numbers]))
            .group_by(
                pricelists_query.c.email,
                ProductAvailability.product_code,
                ProductAvailability.unit_code,
            )
            .subquery()
        )

    def _get_unit_names_query(self):
        return select(Units.code, func.coalesce(Units.name, Units.code).label("display_name")).subquery()

    def _build_final_query(self, prices_query, lowest_price_ids_query, stock_query, unit_names_query):
        return (
            select(
                func.trim(prices_query.c.product_code).label("article_no"),
                cast(prices_query.c.gross_price, Float).label("gross_price"),
                cast(prices_query.c.nett_price, Float).label("nett_price"),
                cast(prices_query.c.discount, Float).label("discount_percentage"),
                prices_query.c.currency_code.label("currency"),
                func.coalesce(unit_names_query.c.display_name, prices_query.c.unit_code).label("unit"),
                cast(func.coalesce(stock_query.c.stock, 0.0), Float).label("stock"),
                case(
                    (func.coalesce(stock_query.c.dalt, 999) <= 0, 999),
                    else_=func.coalesce(stock_query.c.dalt, 999),
                ).label("restock_days"),
                prices_query.c.created_ts_utc.label("created_ts_utc"),
                prices_query.c.updated_ts_utc.label("updated_ts_utc"),
            )
            .select_from(prices_query)
            .join(
                lowest_price_ids_query,
                lowest_price_ids_query.c.product_salesprices_id == prices_query.c.product_salesprices_id,
            )
            .outerjoin(
                stock_query,
                and_(
                    stock_query.c.email == prices_query.c.email,
                    func.trim(stock_query.c.product_code) == func.trim(prices_query.c.product_code),
                ),
            )
            .outerjoin(
                unit_names_query,
                unit_names_query.c.code == func.coalesce(prices_query.c.unit_code, stock_query.c.unit_code),
            )
            .order_by(
                func.trim(prices_query.c.product_code).asc(),
                prices_query.c.nett_price.asc(),
                prices_query.c.product_salesprices_id.asc(),
            )
        )

    def _get_single_product_prices_query(self, pricelists_query, article_number, now):
        return (
            select(
                pricelists_query.c.email,
                pricelists_query.c.currency_code,
                Products.code.label("product_code"),
                ProductSalesPrices.unit_code.label("unit_code"),
                cast(ProductSalesPrices.gross_price, Float).label("gross_price"),
                cast(ProductSalesPrices.discount, Float).label("discount"),
                cast(ProductSalesPrices.nett_price, Float).label("nett_price"),
                ProductSalesPrices.id.label("product_salesprices_id"),
                func.coalesce(Units.name, Units.code).label("unit_display_name"),
                ProductSalesPrices.created_at.label("created_ts_utc"),
                ProductSalesPrices.updated_at.label("updated_ts_utc"),
            )
            .select_from(pricelists_query)
            .join(
                ProductSalesPrices,
                and_(
                    func.coalesce(func.trim(ProductSalesPrices.relation_sell_to_code), "").in_(
                        [pricelists_query.c.relation_code_for_pricelist, ""]
                    ),
                    ProductSalesPrices.pricelist_code == pricelists_query.c.pricelist_code,
                    ProductSalesPrices.currency_code == pricelists_query.c.currency_code,
                    (ProductSalesPrices.valid_from.is_(None) | (ProductSalesPrices.valid_from <= now)),
                    (ProductSalesPrices.valid_until.is_(None) | (ProductSalesPrices.valid_until >= now)),
                    ProductSalesPrices.nett_price > 0.0,
                    ProductSalesPrices.gross_price > 0.0,
                ),
            )
            .join(Products, func.trim(Products.code) == func.trim(ProductSalesPrices.product_code))
            .outerjoin(Units, Units.code == ProductSalesPrices.unit_code)
            .where(func.trim(Products.code) == article_number.strip())
            .distinct()
            .subquery()
        )

    def _build_price_only_query(self, prices_query, lowest_price_ids_query):
        return (
            select(
                func.trim(prices_query.c.product_code).label("article_no"),
                cast(prices_query.c.gross_price, Float).label("gross_price"),
                cast(prices_query.c.nett_price, Float).label("nett_price"),
                cast(prices_query.c.discount, Float).label("discount_percentage"),
                prices_query.c.currency_code.label("currency"),
                prices_query.c.unit_display_name.label("unit"),
                prices_query.c.updated_ts_utc.label("updated_ts_utc"),
                prices_query.c.created_ts_utc.label("created_ts_utc"),
            )
            .select_from(prices_query)
            .join(
                lowest_price_ids_query,
                lowest_price_ids_query.c.product_salesprices_id == prices_query.c.product_salesprices_id,
            )
            .order_by(
                prices_query.c.nett_price.asc(),
                prices_query.c.product_salesprices_id.asc(),
            )
            .limit(1)
        )
