from types import SimpleNamespace
from typing import List
from typing import Optional
from typing import Set
from typing import <PERSON><PERSON>
from typing import Type
from typing import TypeVar

from sqlalchemy.orm import Session
from sqlalchemy.orm import aliased
from sqlalchemy.orm import joinedload
from src.models.db.attributes import Attributes
from src.models.db.companies import Companies
from src.models.db.contact_attributes import ContactAttributes
from src.models.db.contact_relation_sell_to import ContactRelationSellTo
from src.models.db.contacts import Contacts
from src.models.db.countries import Countries
from src.models.db.currencies import Currencies
from src.models.db.employees import Employees
from src.models.db.languages import Languages
from src.models.db.relation_attributes import RelationAttributes
from src.models.db.relations import Relations
from src.models.db.relations_sell_to import RelationsSellTo
from src.models.db.users import Users
from src.models.schemas.user import Attribute
from src.models.schemas.user import ExternalLink
from src.models.schemas.user import Relation
from src.models.schemas.user import User
from src.models.schemas.user import UserAggregate
from src.repositories.user_repository import UserRepository
from src.shared.exceptions import UserAccessDeniedError
from src.shared.exceptions import UserNotFoundError
from src.shared.mappers.user_mapper import UserMapper

# Define a generic type for our DB models
T = TypeVar("T")


class UserRepositoryImpl(UserRepository):
    def __init__(self, session: Session):
        self.session = session

    def get_by_email(self, user_email: str) -> UserAggregate:
        """
        Retrieves user data by email, handling aggregation for multiple contacts.
        """
        user_db, contacts = self._get_validated_user_and_contacts(user_email)

        (
            contact_for_mapper,
            final_country,
            final_language,
            final_currency_id,
        ) = self._aggregate_contact_details(contacts)

        all_relations, primary_relation_name = self._aggregate_relations(contacts)

        relation_ids = {rel.relation_id for rel in all_relations if rel.relation_id}
        all_attributes = self._get_relation_sell_to_attributes(list(relation_ids))

        external_links = self._get_external_links()

        return UserAggregate(
            user_db=user_db,
            primary_contact=contact_for_mapper,
            all_relations=all_relations,
            primary_relation_name=primary_relation_name,
            primary_currency_id=final_currency_id,
            country=final_country,
            language=final_language,
            attributes=all_attributes,
            external_links=external_links,
        )

    def get_user(self, user_email: str) -> Optional[User]:
        """
        Retrieves a user by email, raising an exception if the user does not exist.
        """
        try:
            return self.session.query(Users).filter(Users.email == user_email, Users.portal_access).first()
        except Exception as e:
            raise UserNotFoundError(user_email) from e

    def _get_validated_user_and_contacts(self, user_email: str) -> Tuple[Users, List[Contacts]]:
        """Fetches a user and their contacts, raising errors if not found or access is denied."""
        if not (user_db := self._get_user_by_email(user_email)):
            raise UserNotFoundError(user_email)
        if not user_db.portal_access:
            raise UserAccessDeniedError(user_email)
        if not user_db.contacts:
            raise UserNotFoundError(user_email)
        return user_db, user_db.contacts

    def _aggregate_contact_details(
        self, contacts: List[Contacts]
    ) -> Tuple[SimpleNamespace, Optional[Countries], Optional[Languages], Optional[str]]:
        """Aggregates details from a list of contacts into a single representation."""
        primary_contact = contacts[0]

        all_countries = [c.country or self._get_country_for_contact(c) for c in contacts]
        all_languages = [c.language or self._get_language_for_contact(c) for c in contacts]
        all_currencies = [self._get_currency_for_contact(c) for c in contacts]

        unique_names = {c.name for c in contacts}
        unique_codes = {c.code for c in contacts}
        unique_country_codes = {country.code for country in all_countries if country}
        unique_language_codes = {lang.code for lang in all_languages if lang}
        unique_currency_codes = {curr.code for curr in all_currencies if curr}

        final_name = primary_contact.name if len(unique_names) == 1 else "*"
        final_code = primary_contact.code if len(unique_codes) == 1 else "*"

        final_country = all_countries[0] if len(unique_country_codes) <= 1 else Countries(code="*", name="*")
        final_language = all_languages[0] if len(unique_language_codes) <= 1 else Languages(code="EN", name="English")

        currency = all_currencies[0] if all_currencies else None
        final_currency_id = currency.code if len(unique_currency_codes) <= 1 and currency else "*"

        # Use a SimpleNamespace to create a flexible object for the mapper
        contact_dict = {k: v for k, v in primary_contact.__dict__.items() if not k.startswith("_")}
        contact_dict["name"] = final_name
        contact_dict["code"] = final_code
        contact_for_mapper = SimpleNamespace(**contact_dict)

        return contact_for_mapper, final_country, final_language, final_currency_id

    def _aggregate_relations(self, contacts: List[Contacts]) -> Tuple[List[Relation], Optional[str]]:
        """Aggregates unique relations from a list of contacts."""
        all_relations: List[Relation] = []
        seen_relation_ids: Set[str] = set()
        primary_relation_name: Optional[str] = None

        for idx, contact in enumerate(contacts):
            relations_list, rel_name, _ = self._get_relations(contact.code)
            if idx == 0:
                primary_relation_name = rel_name

            for rel in relations_list:
                if rel.relation_id and rel.relation_id not in seen_relation_ids:
                    all_relations.append(rel)
                    seen_relation_ids.add(rel.relation_id)

        return all_relations, primary_relation_name

    def _get_user_by_email(self, user_email: str) -> Optional[Users]:
        return (
            self.session.query(Users)
            .options(
                joinedload(Users.contacts).joinedload(Contacts.country),
                joinedload(Users.contacts).joinedload(Contacts.language),
            )
            .filter(Users.email == user_email, Users.portal_access)
            .first()
        )

    def _get_relations(self, contact_code: str) -> Tuple[List[Relation], Optional[str], Optional[str]]:
        int_sales_rep = aliased(Employees)
        ext_sales_rep = aliased(Employees)

        # A user can be a contact with multiple relations, but some of those relations might not be valid for the portal.
        # We filter out relations that do not have the required attributes.
        # Specifically, we look for relations that have both a category and sub-category attribute set to "JA".
        # This ensures we only return relations that are relevant for the portal.
        category_relations = (
            self.session.query(RelationAttributes.relation_code)
            .filter(
                RelationAttributes.attribute_code.like("C0004C%"),
                RelationAttributes.attribute_value.ilike("JA"),
            )
            .distinct()
        )
        sub_category_relations = (
            self.session.query(RelationAttributes.relation_code)
            .filter(
                RelationAttributes.attribute_code.like("C0004S%"),
                RelationAttributes.attribute_value.ilike("JA"),
            )
            .distinct()
        )
        valid_relation_codes = set(r[0] for r in category_relations.all()) & set(
            r[0] for r in sub_category_relations.all()
        )

        relations_data = (
            self.session.query(
                RelationsSellTo,
                Relations.name.label("relation_name"),
                Companies.name.label("company_name"),
                int_sales_rep.code.label("int_sales_rep_code"),
                int_sales_rep.name.label("int_sales_rep_name"),
                int_sales_rep.email.label("int_sales_rep_email"),
                ext_sales_rep.code.label("ext_sales_rep_code"),
                ext_sales_rep.name.label("ext_sales_rep_name"),
                ext_sales_rep.email.label("ext_sales_rep_email"),
                Currencies.code.label("currency_code"),
            )
            .join(
                ContactRelationSellTo,
                ContactRelationSellTo.relation_sell_to_code == RelationsSellTo.relation_code,
            )
            .join(Relations, Relations.code == RelationsSellTo.relation_code)
            .join(Companies, Companies.code == RelationsSellTo.company_code)
            .outerjoin(
                int_sales_rep,
                int_sales_rep.code == RelationsSellTo.int_sales_rep_employee_code,
            )
            .outerjoin(
                ext_sales_rep,
                ext_sales_rep.code == RelationsSellTo.ext_sales_rep_employee_code,
            )
            .outerjoin(Currencies, Currencies.code == RelationsSellTo.currency_code)
            .filter(ContactRelationSellTo.contact_code == contact_code)
            .filter(RelationsSellTo.relation_code.in_(valid_relation_codes))
            .all()
        )

        relations_list = []
        primary_relation_name = None
        primary_currency_id = None

        if relations_data:
            first_relation = relations_data[0]
            primary_relation_name = first_relation.relation_name
            primary_currency_id = first_relation.currency_code

            for rel_data in relations_data:
                relations_list.append(
                    Relation(
                        company_id=rel_data.company_name,
                        relation_id=rel_data.RelationsSellTo.relation_code,
                        relation_name=rel_data.relation_name,
                        int_sales_rep_id=rel_data.int_sales_rep_code,
                        int_sales_rep_name=rel_data.int_sales_rep_name,
                        int_sales_rep_email=rel_data.int_sales_rep_email,
                        ext_sales_rep_id=rel_data.ext_sales_rep_code,
                        ext_sales_rep_name=rel_data.ext_sales_rep_name,
                        ext_sales_rep_email=rel_data.ext_sales_rep_email,
                    )
                )

        return relations_list, primary_relation_name, primary_currency_id

    def _get_relation_sell_to_attributes(self, relation_codes: List[str]) -> List[Attribute]:
        """
        Retrieves attributes for a list of relation codes.
        """
        AttributeCategory = aliased(Attributes)
        RelationAttributeCategory = aliased(RelationAttributes)
        AttributeSubCategory = aliased(Attributes)
        RelationAttributeSubCategory = aliased(RelationAttributes)

        categories_query = (
            self.session.query(AttributeCategory.name)
            .join(
                RelationAttributeCategory,
                AttributeCategory.code == RelationAttributeCategory.attribute_code,
            )
            .join(
                RelationsSellTo,
                RelationsSellTo.relation_code == RelationAttributeCategory.relation_code,
            )
            .filter(
                RelationsSellTo.relation_code.in_(relation_codes),
                RelationAttributeCategory.attribute_code.like("C0004C%"),
                RelationAttributeCategory.attribute_value.ilike("JA"),
            )
            .distinct()
        )

        sub_categories_query = (
            self.session.query(AttributeSubCategory.name)
            .join(
                RelationAttributeSubCategory,
                AttributeSubCategory.code == RelationAttributeSubCategory.attribute_code,
            )
            .join(
                RelationsSellTo,
                RelationsSellTo.relation_code == RelationAttributeSubCategory.relation_code,
            )
            .filter(
                RelationsSellTo.relation_code.in_(relation_codes),
                RelationAttributeSubCategory.attribute_code.like("C0004S%"),
                RelationAttributeSubCategory.attribute_value.ilike("JA"),
            )
            .distinct()
        )

        categories = [row[0] for row in categories_query.all()]
        sub_categories = [row[0] for row in sub_categories_query.all()]

        attributes_list = []
        if not categories or not sub_categories:
            return []

        for category in categories:
            for sub_category in sub_categories:
                attributes_list.append(
                    Attribute(
                        sf_proposition="Holmatro Partner Portal",
                        sf_category=category,
                        sf_sub_category=sub_category,
                    )
                )

        return attributes_list

    def _get_external_links(self) -> List[ExternalLink]:
        return [
            ExternalLink(link="Centix", url="www.centix.com"),
            ExternalLink(link="SyncForce", url="www.syncforce.com"),
            ExternalLink(link="CatalogCreator", url="www.catalogcreator.com"),
        ]

    def _get_related_code(self, contact_code: str, code_attribute: str) -> Optional[str]:
        """
        Generic helper to retrieve a related code (e.g., country_code, language_code)
        for a contact by checking RelationsSellTo and then Relations.
        """
        relation_sell_to_code_query = self.session.query(ContactRelationSellTo.relation_sell_to_code).filter(
            ContactRelationSellTo.contact_code == contact_code
        )

        relation_code_result = relation_sell_to_code_query.first()
        if not relation_code_result:
            return None

        relation_code = relation_code_result[0]

        # Prioritize code from RelationsSellTo
        code_result = (
            self.session.query(getattr(RelationsSellTo, code_attribute))
            .filter(RelationsSellTo.relation_code == relation_code)
            .first()
        )

        if code_result and code_result[0]:
            return code_result[0]

        # Fallback to Relations
        code_result = (
            self.session.query(getattr(Relations, code_attribute)).filter(Relations.code == relation_code).first()
        )

        return code_result[0] if code_result else None

    def _get_entity_by_code(self, model: Type[T], code: Optional[str]) -> Optional[T]:
        """Generic helper to query a model by its code."""
        if not code:
            return None
        return self.session.query(model).filter(getattr(model, "code") == code).first()

    def _get_language_for_contact(self, contact: Contacts) -> Optional[Languages]:
        """Retrieves the language object for a contact."""
        language_code = self._get_related_code(contact.code, "language_code")
        return self._get_entity_by_code(Languages, language_code)

    def _get_country_for_contact(self, contact: Contacts) -> Optional[Countries]:
        """Retrieves the country object for a contact."""
        country_code = self._get_related_code(contact.code, "country_code")
        return self._get_entity_by_code(Countries, country_code)

    def _get_currency_for_contact(self, contact: Contacts) -> Optional[Currencies]:
        """Retrieves the currency object for a contact."""
        currency_code = self._get_related_code(contact.code, "currency_code")
        return self._get_entity_by_code(Currencies, currency_code)
