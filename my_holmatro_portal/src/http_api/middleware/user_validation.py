import logging
from typing import Optional

from fastapi import Request
from fastapi.responses import JSONResponse
from src.interactors.access_validation_interactor import AccessValidationInteractor
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.user import User
from src.services.user_repository_impl import UserRepositoryImpl
from src.shared.db import get_db_session
from src.shared.exceptions import UserAccessDeniedError
from src.shared.exceptions import UserNotFoundError
from src.shared.settings import Settings
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

_logging = logging.getLogger(__name__)


class UserValidationMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        skip_paths: Optional[list[str]],
    ):
        super().__init__(app)
        self.skip_paths = skip_paths or []
        self.settings = Settings()

    async def dispatch(
        self,
        request: Request,
        call_next,
    ):
        if self.should_skip_validation(request):
            return await call_next(request)

        if not hasattr(request.state, "user_email"):
            _logging.error("User email not found in request state")
            raise UserNotFoundError("User email is required for validation")
        try:
            session = next(get_db_session())
            user_repository = UserRepositoryImpl(session)
            interactor = AccessValidationInteractor(user_repository)
            email = self.should_replace_email(request.state.user_email)

            user = interactor.validate_user_access(email)
            request.state.validated_user = user
            request.state.jwt_claims = JWTClaims(
                emails=[email], aud=self.settings.jwt_audience, iss=self.settings.b2c_issuer, sub=""
            )

        except UserNotFoundError as e:
            _logging.warning(f"User not found: {e}")
            return JSONResponse(
                status_code=404,
                content={"detail": str(e)},
            )
        except UserAccessDeniedError as e:
            _logging.warning(f"Access denied: {e}")
            return JSONResponse(
                status_code=403,
                content={"detail": str(e)},
            )
        except Exception as e:
            _logging.error(f"Unexpected error in user validation: {e}", exc_info=True)
            raise

        response = await call_next(request)
        return response

    def should_skip_validation(self, request: Request) -> bool:
        """Define paths that should skip validation"""
        return any(request.url.path.startswith(path) for path in self.skip_paths)

    def should_replace_email(self, email: str) -> str:
        """
        When testing the MyHolmatroPortal API we override a user that contains a harborn account,
        because in the database we don't have users from harborn stored.
        """
        if self.settings.override_harborn_user and email.__contains__("@harborn.com"):
            return self.settings.override_harborn_user
        return email
