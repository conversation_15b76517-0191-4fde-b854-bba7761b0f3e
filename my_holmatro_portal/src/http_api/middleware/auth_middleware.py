import asyncio
import logging

from aiocache import <PERSON><PERSON><PERSON>ory<PERSON><PERSON>
from authlib.jose import <PERSON><PERSON><PERSON><PERSON><PERSON>oken
from fastapi import Request
from httpx import AsyncClient
from src.models.schemas.jwt_claims import JWTClaims
from src.shared.exceptions import HolmatroBadRequestError
from src.shared.exceptions import HolmatroTokenError
from src.shared.settings import Settings
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from starlette.status import HTTP_400_BAD_REQUEST
from starlette.status import HTTP_401_UNAUTHORIZED

_logging = logging.getLogger(__name__)
jwks_cache = SimpleMemoryCache()
jwks_lock = asyncio.Lock()
settings = Settings()
client = AsyncClient()


class JWTValidationMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, skip_paths=None):
        super().__init__(app)
        self.skip_paths = skip_paths or []

    def should_skip_validation(self, request: Request) -> bool:
        """Define paths that should skip validation"""
        return any(request.url.path.startswith(path) for path in self.skip_paths)

    async def dispatch(self, request: Request, call_next):
        if self.should_skip_validation(request):
            return await call_next(request)
        try:
            await self._validate_jwt(request)
        except (HolmatroBadRequestError, HolmatroTokenError) as exc:
            if isinstance(exc, HolmatroTokenError):
                status_code = HTTP_401_UNAUTHORIZED
                _logging.warning(f"Authentication token error: {exc.message}")
            else:
                status_code = HTTP_400_BAD_REQUEST
                _logging.warning(f"Bad request in auth middleware: {exc.message}")

            return JSONResponse(
                status_code=status_code,
                content={"detail": exc.message},
            )

        return await call_next(request)

    async def _validate_jwt(self, request: Request) -> None:
        """Extract and validate JWT, set user_email in request state"""
        auth_header = request.headers.get("Authorization")
        content_type = request.headers.get("Content-Type")
        if not auth_header or not auth_header.startswith("Bearer ") or content_type != "application/json":
            _logging.error("Missing or invalid headers")
            raise HolmatroBadRequestError(message="Missing or invalid headers")

        await validate_api_key(request)

        token = auth_header.split("Bearer ")[1]
        claims = await validate_jwt_with_authlib(token, settings, client)

        if not claims.email:
            _logging.error(f"Invalid JWT claims: {claims}")
            raise HolmatroTokenError(message="User email is required")

        request.state.user_email = claims.email
        request.state.jwt_claims = claims


async def fetch_openid_config(client: AsyncClient, url: str) -> dict:
    """Fetch OpenID Connect configuration and cache the result."""
    async with jwks_lock:
        cached = await jwks_cache.get(url)
        if cached:
            return cached
        _logging.info(f"Fetching OpenID Connect configuration from {url}")
        response = await client.get(url)
        response.raise_for_status()
        openid_config = response.json()
        await jwks_cache.set(url, openid_config, ttl=3600)
    return openid_config


async def fetch_jwks(client: AsyncClient, url: str) -> dict:
    """Fetch JWKS and cache the result."""
    async with jwks_lock:
        cached_jwks = await jwks_cache.get(url)
        if cached_jwks:
            return cached_jwks
        _logging.info(f"Fetching JWKS from {url}")
        response = await client.get(url)
        response.raise_for_status()
        jwks_data = response.json()
        await jwks_cache.set(url, jwks_data, ttl=3600)
    return jwks_data


async def validate_jwt_with_authlib(token: str, settings: Settings, client: AsyncClient) -> JWTClaims:
    try:
        # Fetch OpenID Connect configuration
        openid_config = await fetch_openid_config(client, settings.openid_b2c_config_url)

        # Fetch JWKS
        jwks = await fetch_jwks(client, openid_config["jwks_uri"])

        # Validate the token
        jwt = JsonWebToken(settings.jwt_algorithm)
        claims_options = {
            "aud": {"essential": True},
            "iss": {"essential": True},
            "sub": {"essential": True},
            "emails": {"essential": True},
        }
        _logging.info("Decoding JWT token")
        claims = jwt.decode(token, jwks, claims_options=claims_options)
        claims.validate()
        if (
            claims.get("aud") != settings.jwt_audience
            or claims.get("iss") != settings.b2c_issuer
            or not claims.get("sub")
            or not claims.get("emails")
        ):
            _logging.info("Invalid token")
            raise HolmatroTokenError(message="Invalid token")
        return JWTClaims(**claims)
    except HolmatroTokenError:
        # Re-raise custom exceptions as-is
        raise
    except Exception as e:
        _logging.error(f"Error validating JWT: {e}", exc_info=True)
        raise HolmatroTokenError(message="Error validating JWT")


async def get_current_user(request: Request) -> JWTClaims:
    """Get the current user from request state (populated by middleware)."""
    if not hasattr(request.state, "jwt_claims"):
        raise HolmatroBadRequestError(message="JWT claims not found")
    return request.state.jwt_claims


async def validate_api_key(request: Request) -> None:
    """Validate API key from request headers."""
    api_key = request.headers.get("Ocp-Apim-Subscription-Key")
    if not api_key or api_key != settings.my_hm_subscription_token:
        _logging.error("Invalid or missing API key")
        raise HolmatroBadRequestError(message="Invalid or missing API key")
    _logging.info("API key validated successfully")
