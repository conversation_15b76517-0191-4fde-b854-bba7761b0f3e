import logging
from builtins import Exception<PERSON><PERSON> as EG

from fastapi import FastAP<PERSON>
from fastapi import <PERSON>TTPException
from src.shared.exceptions import HolmatroBadRequestError
from src.shared.exceptions import HolmatroConflictError
from src.shared.exceptions import HolmatroNotFoundError
from src.shared.exceptions import HolmatroTokenError
from starlette.responses import JSONResponse
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR


def standard_json_response(status_code: int, detail: str, exception: Exception) -> JSONResponse:
    """Create a standard HTTP error response that matches customer portal expectations."""
    if status_code == HTTP_500_INTERNAL_SERVER_ERROR:
        logging.exception("Handling internal server error")
    else:
        logging.warning(f"Handling expected exception: '{exception}'")

    return JSONResponse(status_code=status_code, content={"detail": detail})


def register_exception_handlers(app: FastAPI) -> None:
    """Register exception handlers in FastAPI app."""

    specific_exception_handlers = {
        HTTPException: lambda request, exc: JSONResponse(status_code=exc.status_code, content={"detail": exc.detail}),
        HolmatroBadRequestError: lambda request, exc: standard_json_response(400, exc.message, exc),
        HolmatroNotFoundError: lambda request, exc: standard_json_response(404, exc.message, exc),
        HolmatroConflictError: lambda request, exc: standard_json_response(409, exc.message, exc),
        HolmatroTokenError: lambda request, exc: standard_json_response(401, exc.message, exc),
    }

    def fallback_exception_handler(request, exc):
        actual_exc = exc.exceptions[0] if isinstance(exc, ExceptionGroup) else exc  # noqa: F821

        handler = specific_exception_handlers.get(type(actual_exc))
        if handler:
            return handler(request, actual_exc)

        return standard_json_response(500, "Internal server error", exc)

    for exc_class, handler in specific_exception_handlers.items():
        app.add_exception_handler(exc_class, handler)

    app.add_exception_handler(Exception, fallback_exception_handler)

    app.add_exception_handler(EG, fallback_exception_handler)
