from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from src.http_api.dependencies.required_header import verify_subscription_key
from src.http_api.middleware.auth_middleware import get_current_user
from src.interactors.user_interactor import UserInteractor
from src.models.response.user_response import UserResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.services.user_repository_impl import UserRepositoryImpl
from src.shared.db import get_db_session
from src.shared.settings import Settings

router = APIRouter(dependencies=[Depends(verify_subscription_key)])
settings = Settings()


from sqlalchemy.orm import Session


def get_user_interactor(
    session: Annotated[Session, Depends(get_db_session)],
) -> UserInteractor:
    return UserInteractor(UserRepositoryImpl(session))


@router.get("/user")
def get_user(
    current_user: Annotated[JWTClaims, Depends(get_current_user)],
    interactor: Annotated[UserInteractor, Depends(get_user_interactor)],
) -> UserResponse:
    return interactor.get_user_by_email(current_user)
