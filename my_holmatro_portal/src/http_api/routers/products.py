from typing import Annotated
from typing import Callable

from fastapi import APIRouter
from fastapi import Depends
from fastapi import Query
from src.http_api.middleware.auth_middleware import get_current_user
from src.interactors.product_interactor import ProductInteractor
from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.product import ProductAvailability
from src.models.schemas.product import ProductPrice
from src.services.product_repository_impl import ProductRepositoryImpl
from src.shared.db import get_db_session

router = APIRouter()


def get_product_interactor(session: Annotated[Callable, Depends(get_db_session)]) -> ProductInteractor:
    return ProductInteractor(ProductRepositoryImpl(session))


@router.get("/salesinfo")
def get_sales_info(
    articleno: Annotated[list[str], Query(...)],
    current_user: Annotated[JWTClaims, Depends(get_current_user)],
    interactor: Annotated[ProductInteractor, Depends(get_product_interactor)],
) -> ProductSalesInfoResponse:
    return interactor.get_product_sales_info(current_user, articleno)


@router.get("/{articleno}/salesprices")
def get_sales_price(
    articleno: str,
    current_user: Annotated[JWTClaims, Depends(get_current_user)],
    interactor: Annotated[ProductInteractor, Depends(get_product_interactor)],
) -> ProductPrice:
    return interactor.get_product_sales_price(current_user, articleno)


@router.get("/{articleno}/availability")
def get_product_availability(
    articleno: str,
    current_user: Annotated[JWTClaims, Depends(get_current_user)],
    interactor: Annotated[ProductInteractor, Depends(get_product_interactor)],
) -> ProductAvailability:
    return interactor.get_product_availability(current_user, articleno)
