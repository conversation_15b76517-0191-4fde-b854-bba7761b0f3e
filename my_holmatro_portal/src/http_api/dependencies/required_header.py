import logging

from fastapi import Depends
from fastapi import H<PERSON>P<PERSON>xception
from fastapi.security import <PERSON><PERSON>eyHeader
from src.shared.settings import Settings
from starlette.status import HTTP_401_UNAUTHORIZED

settings = Settings()
subscription_key_header = APIKeyHeader(name="Ocp-Apim-Subscription-Key", auto_error=False)
subscription_key_dependency = Depends(subscription_key_header)


def verify_subscription_key(header: str | None = subscription_key_dependency) -> None:
    """Verifies the presence and value of this required header"""
    if header != settings.my_hm_subscription_token:
        logging.error(f"Invalid subscription key: {header}")
        print(header, settings.my_hm_subscription_token)
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, headers={"error-message": "Invalid subscription key"})
