import logging

from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.exc import OperationalError
from src.shared.settings import Settings
from tenacity import retry
from tenacity import stop_after_attempt
from tenacity import wait_fixed

settings = Settings()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

max_retries = 60 * 5  # 5 minutes
wait_seconds = 1


@retry(stop=stop_after_attempt(max_retries), wait=wait_fixed(wait_seconds))
def init() -> None:
    try:
        engine = create_engine(settings.sqlalchemy_database_uri)
        # Try to create a connection to check if <PERSON> is awake
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
    except OperationalError as e:
        logger.error(f"Database not ready: {e}")
        raise e
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise e


def main() -> None:
    logger.info("Initializing service")
    init()
    logger.info("Service finished initializing")


if __name__ == "__main__":
    main()
