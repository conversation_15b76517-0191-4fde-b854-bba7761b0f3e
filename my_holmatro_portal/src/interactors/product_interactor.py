import logging

from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.product import ProductAvailability
from src.models.schemas.product import ProductPrice
from src.repositories.product_repository import ProductRepository
from src.shared.mappers.product_availability_mapper import ProductAvailabilityMapper
from src.shared.mappers.product_price_mapper import ProductPriceMapper
from src.shared.mappers.product_sales_info_mapper import ProductSalesInfoMapper

_logging = logging.getLogger(__name__)


class ProductInteractor:
    def __init__(self, product_repository: ProductRepository):
        self.product_repository = product_repository

    def get_product_sales_info(self, current_user: JWTClaims, article_numbers: list[str]) -> ProductSalesInfoResponse:
        """
        Retrieves product sales information by user email and article numbers with proper error handling.
        """
        sales_info = self.product_repository.get_product_sales_info(current_user.email, article_numbers)
        return ProductSalesInfoMapper.to_product_sales_info_list(sales_info)

    def get_product_sales_price(self, current_user: JWTClaims, article_number: str) -> ProductPrice:
        """
        Retrieves product sales price by user email and article number.
        """
        price = self.product_repository.get_product_price(current_user.email, article_number)
        return ProductPriceMapper.to_product_price(price)

    def get_product_availability(self, current_user: JWTClaims, article_number: str) -> ProductAvailability:
        """
        Retrieves product availability by user email and article number with proper error handling.
        """
        availability = self.product_repository.get_product_availability(current_user.email, article_number)
        return ProductAvailabilityMapper.to_product_availability(availability)
