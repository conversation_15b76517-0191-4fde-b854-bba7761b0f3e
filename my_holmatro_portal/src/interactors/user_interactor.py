import logging

from src.models.response.user_response import UserResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.repositories.user_repository import UserRepository
from src.shared.mappers.user_mapper import UserMapper

_logging = logging.getLogger(__name__)


class UserInteractor:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository

    def get_user_by_email(self, current_user: JWTClaims) -> UserResponse:
        """
        Retrieves user information by email address with proper error handling
        """
        user_data = self.user_repository.get_by_email(current_user.email)
        return UserMapper.to_user(user_data)
