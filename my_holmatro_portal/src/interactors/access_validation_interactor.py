import logging

from src.models.schemas.user import User
from src.repositories.user_repository import UserRepository
from src.shared.exceptions import UserNotFoundError

_logging = logging.getLogger(__name__)


class AccessValidationInteractor:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository

    def validate_user_access(self, user_email: str) -> User:
        """
        Validates user access and returns User object if valid.
        Raises appropriate exceptions if user not found or access denied.
        """
        user_email = user_email.strip().lower()
        if not user_email:
            raise UserNotFoundError("Email is required")
        user = self._get_validated_user(user_email)
        _logging.info(f"User access validated successfully for '{user_email}'")
        return user

    def _get_validated_user(self, user_email: str) -> User:
        """Get user and validate basic portal access."""
        user = self.user_repository.get_user(user_email)
        if not user:
            raise UserNotFoundError(user_email)
        return user
