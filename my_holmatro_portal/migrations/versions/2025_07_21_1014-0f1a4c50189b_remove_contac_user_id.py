"""Remove contac.user_id

Revision ID: 0f1a4c50189b
Revises: 513101cc78ed
Create Date: 2025-07-21 10:14:15.625210

"""

from typing import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import mssql

# revision identifiers, used by Alembic.
revision: str = "0f1a4c50189b"
down_revision: Union[str, None] = "513101cc78ed"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contacts",
        "email",
        existing_type=sa.NVARCHAR(length=320, collation="SQL_Latin1_General_CP1_CI_AS"),
        nullable=True,
    )
    op.drop_constraint("fk_contacts_user_id_users", "contacts", type_="foreignkey")
    op.drop_column("contacts", "user_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    op.add_column("contacts", sa.Column("user_id", mssql.UNIQUEIDENTIFIER(), autoincrement=False, nullable=True))
    op.alter_column(
        "contacts",
        "email",
        existing_type=sa.NVARCHAR(length=320, collation="SQL_Latin1_General_CP1_CI_AS"),
        nullable=False,
    )
