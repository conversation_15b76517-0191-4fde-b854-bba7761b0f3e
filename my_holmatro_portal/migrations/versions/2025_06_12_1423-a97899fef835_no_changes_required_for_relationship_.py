"""No changes required for relationship overlaps

Revision ID: a97899fef835
Revises: aae855246d40
Create Date: 2025-06-12 14:23:48.561743

"""

from typing import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a97899fef835"
down_revision: Union[str, None] = "aae855246d40"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        op.f("fk_relations_country_code_countries"), "relations", "countries", ["country_code"], ["code"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("fk_relations_country_code_countries"), "relations", type_="foreignkey")
    # ### end Alembic commands ###
