"""Create a baseline migrations

Revision ID: aae855246d40
Revises: 
Create Date: 2025-06-10 11:54:19.190854

"""

from typing import Sequence
from typing import Union

import sqlalchemy as sa
import sqlalchemy_utils
from alembic import op
from sqlalchemy.dialects import mssql

# revision identifiers, used by Alembic.
revision: str = "aae855246d40"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "attributes",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_attributes")),
        sa.UniqueConstraint("code", name=op.f("uq_attributes_code")),
    )
    op.create_index("attributes_IX01", "attributes", ["code"], unique=False)
    op.create_table(
        "companies",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=15), nullable=False),
        sa.Column("desc", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_companies")),
        sa.UniqueConstraint("code", name=op.f("uq_companies_code")),
    )
    op.create_index("companies_IX01", "companies", ["code"], unique=False)
    op.create_table(
        "countries",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("code_iso", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_countries")),
        sa.UniqueConstraint("code", name=op.f("uq_countries_code")),
    )
    op.create_index("countries_IX01", "countries", ["code"], unique=False)
    op.create_table(
        "currencies",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("code_iso", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_currencies")),
        sa.UniqueConstraint("code", name=op.f("uq_currencies_code")),
    )
    op.create_index("currencies_IX01", "currencies", ["code"], unique=False)
    op.create_table(
        "employees",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("email", sa.Unicode(length=320), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_employees")),
        sa.UniqueConstraint("code", name=op.f("uq_employees_code")),
    )
    op.create_index("employees_IX01", "employees", ["code"], unique=False)
    op.create_table(
        "languages",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("code_iso", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_languages")),
        sa.UniqueConstraint("code", name=op.f("uq_languages_code")),
    )
    op.create_index("languages_IX01", "languages", ["code"], unique=False)
    op.create_table(
        "products",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=50), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_products")),
        sa.UniqueConstraint("code", name=op.f("uq_products_code")),
    )
    op.create_index("products_IX01", "products", ["code"], unique=False)
    op.create_table(
        "units",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("code_iso", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_units")),
        sa.UniqueConstraint("code", name=op.f("uq_units_code")),
    )
    op.create_index("units_IX01", "units", ["code"], unique=False)
    op.create_table(
        "users",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("email", sa.Unicode(length=320), nullable=False),
        sa.Column("portal_access", sa.Boolean(), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_users")),
    )
    op.create_index("ix_users_email", "users", ["email"], unique=False)
    op.create_index("users_IX01", "users", ["email"], unique=False)
    op.create_table(
        "contacts",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("language_code", sa.Unicode(length=15), nullable=False),
        sa.Column("country_code", sa.Unicode(length=15), nullable=False),
        sa.Column("email", sa.Unicode(length=320), nullable=False),
        sa.Column("user_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=True),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(["country_code"], ["countries.code"], name=op.f("fk_contacts_country_code_countries")),
        sa.ForeignKeyConstraint(
            ["language_code"], ["languages.code"], name=op.f("fk_contacts_language_code_languages")
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], name=op.f("fk_contacts_user_id_users")),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contacts")),
    )
    op.create_index("contacts_IX01", "contacts", ["code"], unique=False)
    op.create_table(
        "pricelists",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("currency_code", sa.Unicode(length=15), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["currency_code"], ["currencies.code"], name=op.f("fk_pricelists_currency_code_currencies")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_pricelists")),
        sa.UniqueConstraint("code", name=op.f("uq_pricelists_code")),
    )
    op.create_index("pricelists_IX01", "pricelists", ["code"], unique=False)
    op.create_table(
        "relations",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("language_code", sa.Unicode(length=15), nullable=False),
        sa.Column("currency_code", sa.Unicode(length=15), nullable=False),
        sa.Column("country_code", sa.Unicode(length=15), nullable=False),
        sa.Column("type", sa.Unicode(length=15), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["currency_code"], ["currencies.code"], name=op.f("fk_relations_currency_code_currencies")
        ),
        sa.ForeignKeyConstraint(
            ["language_code"], ["languages.code"], name=op.f("fk_relations_language_code_languages")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_relations")),
        sa.UniqueConstraint("code", name=op.f("uq_relations_code")),
    )
    op.create_index("relations_IX01", "relations", ["code"], unique=False)
    op.create_table(
        "warehouses",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("code", sa.Unicode(length=15), nullable=False),
        sa.Column("name", sa.Unicode(length=250), nullable=False),
        sa.Column("company_code", sa.Unicode(length=15), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["company_code"], ["companies.code"], name=op.f("fk_warehouses_company_code_companies")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_warehouses")),
        sa.UniqueConstraint("code", name=op.f("uq_warehouses_code")),
    )
    op.create_index("warehouses_IX01", "warehouses", ["code"], unique=False)
    op.create_table(
        "contact_attributes",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("contact_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("attribute_code", sa.Unicode(length=15), nullable=False),
        sa.Column("attribute_value", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_code"], ["attributes.code"], name=op.f("fk_contact_attributes_attribute_code_attributes")
        ),
        sa.ForeignKeyConstraint(
            ["contact_id"], ["contacts.id"], name=op.f("fk_contact_attributes_contact_id_contacts")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contact_attributes")),
        sa.UniqueConstraint("contact_id", "attribute_code", name="uix_contact_attributes_contact_attribute"),
    )
    op.create_index("contact_attributes_IX01", "contact_attributes", ["contact_id", "attribute_code"], unique=False)
    op.create_table(
        "product_availability",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("product_code", sa.Unicode(length=50), nullable=False),
        sa.Column("company_code", sa.Unicode(length=15), nullable=False),
        sa.Column("warehouse_code", sa.Unicode(length=15), nullable=False),
        sa.Column("unit_code", sa.Unicode(length=15), nullable=False),
        sa.Column("stock", sa.Float(), nullable=True),
        sa.Column("dalt", sa.BIGINT(), nullable=True),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["company_code"], ["companies.code"], name=op.f("fk_product_availability_company_code_companies")
        ),
        sa.ForeignKeyConstraint(
            ["product_code"], ["products.code"], name=op.f("fk_product_availability_product_code_products")
        ),
        sa.ForeignKeyConstraint(["unit_code"], ["units.code"], name=op.f("fk_product_availability_unit_code_units")),
        sa.ForeignKeyConstraint(
            ["warehouse_code"], ["warehouses.code"], name=op.f("fk_product_availability_warehouse_code_warehouses")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_product_availability")),
        sa.UniqueConstraint(
            "product_code", "company_code", "warehouse_code", "unit_code", name="uix_product_availability"
        ),
    )
    op.create_index(
        "product_availability_v2_IX01",
        "product_availability",
        ["product_code", "company_code", "warehouse_code", "unit_code"],
        unique=False,
    )
    op.create_table(
        "relation_attributes",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("relation_code", sa.Unicode(length=15), nullable=False),
        sa.Column("attribute_code", sa.Unicode(length=15), nullable=False),
        sa.Column("attribute_value", sa.Unicode(length=250), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["attribute_code"], ["attributes.code"], name=op.f("fk_relation_attributes_attribute_code_attributes")
        ),
        sa.ForeignKeyConstraint(
            ["relation_code"], ["relations.code"], name=op.f("fk_relation_attributes_relation_code_relations")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_relation_attributes")),
        sa.UniqueConstraint("relation_code", "attribute_code", name="uix_relation_attributes_relation_attribute"),
    )
    op.create_index(
        "relation_attributes_IX01", "relation_attributes", ["relation_code", "attribute_code"], unique=False
    )
    op.create_table(
        "relations_sell_to",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("relation_code", sa.Unicode(length=15), nullable=False),
        sa.Column("language_code", sa.Unicode(length=15), nullable=False),
        sa.Column("currency_code", sa.Unicode(length=15), nullable=False),
        sa.Column("country_code", sa.Unicode(length=15), nullable=False),
        sa.Column("int_sales_rep_employee_code", sa.Unicode(length=15), nullable=False),
        sa.Column("ext_sales_rep_employee_code", sa.Unicode(length=15), nullable=False),
        sa.Column("company_code", sa.Unicode(length=15), nullable=False),
        sa.Column("pricelist_code", sa.Unicode(length=15), nullable=False),
        sa.Column("relation_code_for_pricelist", sa.Unicode(length=15), nullable=False),
        sa.Column("status", sa.Unicode(length=50), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["company_code"], ["companies.code"], name=op.f("fk_relations_sell_to_company_code_companies")
        ),
        sa.ForeignKeyConstraint(
            ["country_code"], ["countries.code"], name=op.f("fk_relations_sell_to_country_code_countries")
        ),
        sa.ForeignKeyConstraint(
            ["currency_code"], ["currencies.code"], name=op.f("fk_relations_sell_to_currency_code_currencies")
        ),
        sa.ForeignKeyConstraint(
            ["ext_sales_rep_employee_code"],
            ["employees.code"],
            name=op.f("fk_relations_sell_to_ext_sales_rep_employee_code_employees"),
        ),
        sa.ForeignKeyConstraint(
            ["int_sales_rep_employee_code"],
            ["employees.code"],
            name=op.f("fk_relations_sell_to_int_sales_rep_employee_code_employees"),
        ),
        sa.ForeignKeyConstraint(
            ["language_code"], ["languages.code"], name=op.f("fk_relations_sell_to_language_code_languages")
        ),
        sa.ForeignKeyConstraint(
            ["pricelist_code"], ["pricelists.code"], name=op.f("fk_relations_sell_to_pricelist_code_pricelists")
        ),
        sa.ForeignKeyConstraint(
            ["relation_code"], ["relations.code"], name=op.f("fk_relations_sell_to_relation_code_relations")
        ),
        sa.ForeignKeyConstraint(
            ["relation_code_for_pricelist"],
            ["relations.code"],
            name=op.f("fk_relations_sell_to_relation_code_for_pricelist_relations"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_relations_sell_to")),
        sa.UniqueConstraint("relation_code", name=op.f("uq_relations_sell_to_relation_code")),
    )
    op.create_index("relations_sell_to_IX01", "relations_sell_to", ["relation_code"], unique=False)
    op.create_table(
        "contact_relation_sell_to",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("contact_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("relation_sell_to_code", sa.Unicode(length=15), nullable=False),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["contact_id"], ["contacts.id"], name=op.f("fk_contact_relation_sell_to_contact_id_contacts")
        ),
        sa.ForeignKeyConstraint(
            ["relation_sell_to_code"],
            ["relations_sell_to.relation_code"],
            name=op.f("fk_contact_relation_sell_to_relation_sell_to_code_relations_sell_to"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_contact_relation_sell_to")),
        sa.UniqueConstraint("contact_id", "relation_sell_to_code", name="uix_contact_relation_sell_to"),
    )
    op.create_index(
        "contact_relation_sell_to_IX01",
        "contact_relation_sell_to",
        ["contact_id", "relation_sell_to_code"],
        unique=False,
    )
    op.create_table(
        "product_salesprices",
        sa.Column(
            "id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), server_default=sa.text("newid()"), nullable=False
        ),
        sa.Column("relation_sell_to_code", sa.Unicode(length=15), nullable=False),
        sa.Column("pricelist_code", sa.Unicode(length=15), nullable=False),
        sa.Column("currency_code", sa.Unicode(length=15), nullable=False),
        sa.Column("product_code", sa.Unicode(length=50), nullable=False),
        sa.Column("unit_code", sa.Unicode(length=15), nullable=False),
        sa.Column("min_quantity", sa.Float(), nullable=True),
        sa.Column("gross_price", sa.Float(), nullable=True),
        sa.Column("discount", sa.Float(), nullable=True),
        sa.Column("nett_price", sa.Float(), nullable=True),
        sa.Column("valid_from", mssql.DATETIME2(), nullable=True),
        sa.Column("valid_until", mssql.DATETIME2(), nullable=True),
        sa.Column("created_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.Column("updated_at", mssql.DATETIME2(), server_default=sa.text("sysutcdatetime()"), nullable=False),
        sa.ForeignKeyConstraint(
            ["currency_code"], ["currencies.code"], name=op.f("fk_product_salesprices_currency_code_currencies")
        ),
        sa.ForeignKeyConstraint(
            ["pricelist_code"], ["pricelists.code"], name=op.f("fk_product_salesprices_pricelist_code_pricelists")
        ),
        sa.ForeignKeyConstraint(
            ["product_code"], ["products.code"], name=op.f("fk_product_salesprices_product_code_products")
        ),
        sa.ForeignKeyConstraint(
            ["relation_sell_to_code"],
            ["relations_sell_to.relation_code"],
            name=op.f("fk_product_salesprices_relation_sell_to_code_relations_sell_to"),
        ),
        sa.ForeignKeyConstraint(["unit_code"], ["units.code"], name=op.f("fk_product_salesprices_unit_code_units")),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_product_salesprices")),
        sa.UniqueConstraint(
            "relation_sell_to_code",
            "pricelist_code",
            "currency_code",
            "product_code",
            "unit_code",
            "min_quantity",
            "valid_from",
            name="uix_product_salesprices",
        ),
    )
    op.create_index(
        "product_salesprices_v2_IX01",
        "product_salesprices",
        [
            "relation_sell_to_code",
            "pricelist_code",
            "currency_code",
            "product_code",
            "unit_code",
            "min_quantity",
            "valid_from",
        ],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("product_salesprices_v2_IX01", table_name="product_salesprices")
    op.drop_table("product_salesprices")
    op.drop_index("contact_relation_sell_to_IX01", table_name="contact_relation_sell_to")
    op.drop_table("contact_relation_sell_to")
    op.drop_index("relations_sell_to_IX01", table_name="relations_sell_to")
    op.drop_table("relations_sell_to")
    op.drop_index("relation_attributes_IX01", table_name="relation_attributes")
    op.drop_table("relation_attributes")
    op.drop_index("product_availability_v2_IX01", table_name="product_availability")
    op.drop_table("product_availability")
    op.drop_index("contact_attributes_IX01", table_name="contact_attributes")
    op.drop_table("contact_attributes")
    op.drop_index("warehouses_IX01", table_name="warehouses")
    op.drop_table("warehouses")
    op.drop_index("relations_IX01", table_name="relations")
    op.drop_table("relations")
    op.drop_index("pricelists_IX01", table_name="pricelists")
    op.drop_table("pricelists")
    op.drop_index("contacts_IX01", table_name="contacts")
    op.drop_table("contacts")
    op.drop_index("users_IX01", table_name="users")
    op.drop_index("ix_users_email", table_name="users")
    op.drop_table("users")
    op.drop_index("units_IX01", table_name="units")
    op.drop_table("units")
    op.drop_index("products_IX01", table_name="products")
    op.drop_table("products")
    op.drop_index("languages_IX01", table_name="languages")
    op.drop_table("languages")
    op.drop_index("employees_IX01", table_name="employees")
    op.drop_table("employees")
    op.drop_index("currencies_IX01", table_name="currencies")
    op.drop_table("currencies")
    op.drop_index("countries_IX01", table_name="countries")
    op.drop_table("countries")
    op.drop_index("companies_IX01", table_name="companies")
    op.drop_table("companies")
    op.drop_index("attributes_IX01", table_name="attributes")
    op.drop_table("attributes")
    # ### end Alembic commands ###
