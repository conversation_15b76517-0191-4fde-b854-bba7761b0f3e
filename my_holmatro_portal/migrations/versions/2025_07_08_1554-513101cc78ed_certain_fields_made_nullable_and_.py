"""Certain fields made nullable and contact.code is now the primary key

Revision ID: 513101cc78ed
Revises: a97899fef835
Create Date: 2025-07-08 15:54:02.602871

"""

from typing import Sequence
from typing import Union

import sqlalchemy as sa
import sqlalchemy_utils
from alembic import op
from sqlalchemy.dialects import mssql

# revision identifiers, used by Alembic.
revision: str = "513101cc78ed"
down_revision: Union[str, None] = "a97899fef835"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    This migration switches the primary key of the 'contacts' table from 'id' to 'code'.
    It updates all dependent tables to reference 'contacts.code' instead of 'contacts.id'.
    """
    # ### commands auto generated by Alembic - please adjust! ###

    # --- Step 1: Add and populate the new 'contact_code' column in child tables ---
    op.add_column(
        "contact_attributes",
        sa.Column("contact_code", sa.Unicode(length=15), nullable=True),
    )
    op.add_column(
        "contact_relation_sell_to",
        sa.Column("contact_code", sa.Unicode(length=15), nullable=True),
    )

    op.execute(
        """
        UPDATE ca SET ca.contact_code = c.code
        FROM contact_attributes AS ca JOIN contacts AS c ON ca.contact_id = c.id
    """
    )
    op.execute(
        """
        UPDATE crst SET crst.contact_code = c.code
        FROM contact_relation_sell_to AS crst JOIN contacts AS c ON crst.contact_id = c.id
    """
    )

    op.alter_column(
        "contact_attributes",
        "contact_code",
        existing_type=sa.Unicode(length=15),
        nullable=False,
    )
    op.alter_column(
        "contact_relation_sell_to",
        "contact_code",
        existing_type=sa.Unicode(length=15),
        nullable=False,
    )

    # --- Step 2: Drop ALL old dependent constraints (FKs, Unique Constraints, and Indexes) ---
    op.drop_constraint(
        "fk_contact_attributes_contact_id_contacts",
        "contact_attributes",
        type_="foreignkey",
    )
    op.drop_constraint(
        "fk_contact_relation_sell_to_contact_id_contacts",
        "contact_relation_sell_to",
        type_="foreignkey",
    )

    op.drop_constraint("uix_contact_attributes_contact_attribute", "contact_attributes", type_="unique")
    op.drop_constraint("uix_contact_relation_sell_to", "contact_relation_sell_to", type_="unique")

    # **NEW**: Drop the old indexes that depend on the columns we are about to remove.
    op.drop_index("contact_attributes_IX01", table_name="contact_attributes")
    op.drop_index("contact_relation_sell_to_IX01", table_name="contact_relation_sell_to")

    # --- Step 3: Change the Primary Key on the 'contacts' table ---
    op.drop_constraint("pk_contacts", "contacts", type_="primary")
    op.alter_column("contacts", "id", server_default=None)
    op.create_primary_key(op.f("pk_contacts"), "contacts", ["code"])
    op.drop_column("contacts", "id")

    # --- Step 4: Create the new Foreign Keys, Unique constraints, and Indexes ---
    op.create_foreign_key(
        op.f("fk_contact_attributes_contact_code_contacts"),
        "contact_attributes",
        "contacts",
        ["contact_code"],
        ["code"],
    )
    op.create_foreign_key(
        op.f("fk_contact_relation_sell_to_contact_code_contacts"),
        "contact_relation_sell_to",
        "contacts",
        ["contact_code"],
        ["code"],
    )

    op.create_unique_constraint(
        "uix_contact_attributes_contact_attribute",
        "contact_attributes",
        ["contact_code", "attribute_code"],
    )
    op.create_unique_constraint(
        "uix_contact_relation_sell_to",
        "contact_relation_sell_to",
        ["contact_code", "relation_sell_to_code"],
    )

    # **NEW**: Recreate the indexes with the new columns.
    op.create_index(
        "contact_attributes_IX01",
        "contact_attributes",
        ["contact_code", "attribute_code"],
        unique=False,
    )
    op.create_index(
        "contact_relation_sell_to_IX01",
        "contact_relation_sell_to",
        ["contact_code", "relation_sell_to_code"],
        unique=False,
    )

    # --- Step 5: Drop the old columns and perform other alterations ---
    op.drop_column("contact_attributes", "contact_id")
    op.drop_column("contact_relation_sell_to", "contact_id")

    op.alter_column("contacts", "language_code", existing_type=sa.Unicode(length=15), nullable=True)
    op.alter_column("contacts", "country_code", existing_type=sa.Unicode(length=15), nullable=True)

    op.alter_column(
        "product_salesprices",
        "relation_sell_to_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )

    op.alter_column("relations", "language_code", existing_type=sa.Unicode(length=15), nullable=True)
    op.alter_column("relations", "currency_code", existing_type=sa.Unicode(length=15), nullable=True)
    op.alter_column("relations", "country_code", existing_type=sa.Unicode(length=15), nullable=True)
    op.drop_column("relations", "type")

    op.alter_column(
        "relations_sell_to",
        "language_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "currency_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "country_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "int_sales_rep_employee_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "ext_sales_rep_employee_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "company_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "pricelist_code",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    op.alter_column(
        "relations_sell_to",
        "relation_code_for_pricelist",
        existing_type=sa.Unicode(length=15),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """
    Reverses the primary key change, reverting 'contacts' to use 'id' as the PK
    and updating all child tables accordingly.
    """
    # ### commands auto generated by Alembic - please adjust! ###

    # --- Step 1: Reverse other schema alterations ---
    op.alter_column(
        "relations_sell_to",
        "relation_code_for_pricelist",
        existing_type=sa.Unicode(length=15),
        nullable=False,
    )
    # ... (rest of downgrade remains the same)

    # --- Step 2: Add back the old 'contact_id' columns and drop new ones ---
    op.add_column(
        "contact_attributes",
        sa.Column(
            "contact_id",
            sqlalchemy_utils.types.uuid.UUIDType(binary=False),
            nullable=True,
        ),
    )
    op.add_column(
        "contact_relation_sell_to",
        sa.Column(
            "contact_id",
            sqlalchemy_utils.types.uuid.UUIDType(binary=False),
            nullable=True,
        ),
    )

    # --- Step 3: Revert the Primary Key on 'contacts' table ---
    op.add_column(
        "contacts",
        sa.Column(
            "id",
            sqlalchemy_utils.types.uuid.UUIDType(binary=False),
            nullable=True,
            server_default=sa.text("newid()"),
        ),
    )
    op.execute("UPDATE contacts SET id = NEWID()")
    op.alter_column("contacts", "id", nullable=False)

    op.execute(
        """
        UPDATE ca SET ca.contact_id = c.id
        FROM contact_attributes AS ca JOIN contacts AS c ON ca.contact_code = c.code
    """
    )
    op.execute(
        """
        UPDATE crst SET crst.contact_id = c.id
        FROM contact_relation_sell_to AS crst JOIN contacts AS c ON crst.contact_code = c.code
    """
    )
    op.alter_column("contact_attributes", "contact_id", nullable=False)
    op.alter_column("contact_relation_sell_to", "contact_id", nullable=False)

    # --- Step 4: Drop new constraints and recreate old ones ---
    op.drop_constraint(
        op.f("fk_contact_attributes_contact_code_contacts"),
        "contact_attributes",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_contact_relation_sell_to_contact_code_contacts"),
        "contact_relation_sell_to",
        type_="foreignkey",
    )
    op.drop_constraint("uix_contact_attributes_contact_attribute", "contact_attributes", type_="unique")
    op.drop_constraint("uix_contact_relation_sell_to", "contact_relation_sell_to", type_="unique")

    op.drop_constraint(op.f("pk_contacts"), "contacts", type_="primary")
    op.create_primary_key(op.f("pk_contacts"), "contacts", ["id"])

    op.create_foreign_key(
        "fk_contact_attributes_contact_id_contacts",
        "contact_attributes",
        "contacts",
        ["contact_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_contact_relation_sell_to_contact_id_contacts",
        "contact_relation_sell_to",
        "contacts",
        ["contact_id"],
        ["id"],
    )
    op.create_unique_constraint(
        "uix_contact_attributes_contact_attribute",
        "contact_attributes",
        ["contact_id", "attribute_code"],
    )
    op.create_unique_constraint(
        "uix_contact_relation_sell_to",
        "contact_relation_sell_to",
        ["contact_id", "relation_sell_to_code"],
    )

    op.drop_column("contact_attributes", "contact_code")
    op.drop_column("contact_relation_sell_to", "contact_code")
    # ### end Alembic commands ###
