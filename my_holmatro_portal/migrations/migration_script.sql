-- Corrected Data Migration Script
-- Handles de-duplication of records from the source database to fit the new, stricter schema.

-- Declare variables for source and target database and schema names
DECLARE @SourceDatabase NVARCHAR(128) = 'myhmdb';
DECLARE @TargetDatabase NVARCHAR(128) = 'integrations';
DECLARE @SourceSchema NVARCHAR(128) = 'dbo';
DECLARE @TargetSchema NVARCHAR(128) = 'dbo';

BEGIN TRANSACTION; -- Start a transaction to allow rollback

BEGIN TRY
    -- Step 1: Migrate Reference Data (Countries, Languages, Currencies, etc.)
    -- This part of the script is generally safe as it uses GROUP BY on the codes.

    -- 1.1 Countries
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.countries (code, code_iso, name, created_at, updated_at)
        SELECT DISTINCT CountryId, CountryId, ''Country '' + CountryId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE CountryId IS NOT NULL AND CountryId <> ''''
        GROUP BY CountryId;
    ');

    -- 1.2 Languages
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.languages (code, code_iso, name, created_at, updated_at)
        SELECT DISTINCT LanguageId, LanguageId, ''Language '' + LanguageId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE LanguageId IS NOT NULL AND LanguageId <> ''''
        GROUP BY LanguageId;
    ');

    -- 1.3 Currencies
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.currencies (code, code_iso, name, created_at, updated_at)
        SELECT DISTINCT CurrencyId, CurrencyId, ''Currency '' + CurrencyId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM (
            SELECT CurrencyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE CurrencyId IS NOT NULL AND CurrencyId <> ''''
            UNION ALL
            SELECT CurrencyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductSalesPrices WHERE CurrencyId IS NOT NULL AND CurrencyId <> ''''
        ) AS CombinedCurrencies GROUP BY CurrencyId;
    ');

    -- 1.4 Units
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.units (code, code_iso, name, created_at, updated_at)
        SELECT DISTINCT UnitId, UnitId, Unit, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM (
            SELECT UnitId, Unit, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductSalesPrices WHERE UnitId IS NOT NULL AND UnitId <> ''''
            UNION ALL
            SELECT UnitId, Unit, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductAvailability WHERE UnitId IS NOT NULL AND UnitId <> ''''
        ) AS CombinedUnits GROUP BY UnitId, Unit;
    ');

    -- 1.5 Companies
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.companies (code, name, [desc], created_at, updated_at)
        SELECT DISTINCT CompanyId, ''Company '' + CompanyId, ''Company description for '' + CompanyId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM (
            SELECT CompanyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductAvailability WHERE CompanyId IS NOT NULL AND CompanyId <> ''''
            UNION ALL
            SELECT CompanyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE CompanyId IS NOT NULL AND CompanyId <> ''''
        ) AS CombinedCompanies GROUP BY CompanyId;
    ');

    -- 1.6 Products
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.products (code, name, created_at, updated_at)
        SELECT DISTINCT ProductId, ''Product '' + ProductId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM (
            SELECT ProductId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductSalesPrices WHERE ProductId IS NOT NULL AND ProductId <> ''''
            UNION ALL
            SELECT ProductId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductAvailability WHERE ProductId IS NOT NULL AND ProductId <> ''''
        ) AS CombinedProducts GROUP BY ProductId;
    ');

    -- 1.7 Employees
    EXEC('
        WITH AllEmployeeCodes AS (
            SELECT IntSalesRepId AS Code, IntSalesRepName AS Name, IntSalesRepEmail AS Email, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE IntSalesRepId IS NOT NULL AND IntSalesRepId <> ''''
            UNION SELECT ExtSalesRepId, ExtSalesRepName, ExtSalesRepEmail, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE ExtSalesRepId IS NOT NULL AND ExtSalesRepId <> ''''
        ),
        EmployeeDetails AS (
            SELECT Code, Name, Email, CreatedTsUtc, UpdatedTsUtc, ROW_NUMBER() OVER(PARTITION BY Code ORDER BY CreatedTsUtc ASC) as rn
            FROM AllEmployeeCodes
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.employees (code, name, email, created_at, updated_at)
        SELECT Code, ISNULL(NULLIF(Name, ''''), ''Employee '' + Code), ISNULL(NULLIF(Email, ''''), ''<EMAIL>''), MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM EmployeeDetails WHERE rn = 1 GROUP BY Code, Name, Email;
    ');

    -- 1.8 Warehouses
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.warehouses (code, name, company_code, created_at, updated_at)
        SELECT DISTINCT WarehouseId, ''Warehouse '' + WarehouseId, CompanyId, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductAvailability WHERE WarehouseId IS NOT NULL AND WarehouseId <> '''' AND CompanyId IS NOT NULL AND CompanyId <> ''''
        GROUP BY WarehouseId, CompanyId;
    ');

    -- 1.9 Attributes
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.[attributes] (code, name, created_at, updated_at)
        SELECT DISTINCT AttributeCode, AttributeDesc, MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.User_Attributes WHERE AttributeCode IS NOT NULL AND AttributeCode <> ''''
        GROUP BY AttributeCode, AttributeDesc;
    ');

    -- Step 2: Migrate primary entities with de-duplication logic

    -- 2.1 Users
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.users (email, portal_access, created_at, updated_at)
        SELECT ContactEmail, MAX(CASE WHEN PortalAccess = 1 THEN 1 ELSE 0 END), MIN(CreatedTsUtc), MAX(UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE ContactEmail IS NOT NULL AND ContactEmail <> ''''
        GROUP BY ContactEmail;
    ');

    -- 2.2 Pricelists
    EXEC('
        WITH PriceListDetails AS (
            SELECT PriceListId, CurrencyId, CreatedTsUtc, UpdatedTsUtc, ROW_NUMBER() OVER(PARTITION BY PriceListId ORDER BY CreatedTsUtc ASC) as rn
            FROM (
                SELECT PriceListId, CurrencyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductSalesPrices WHERE PriceListId IS NOT NULL AND PriceListId <> ''''
                UNION SELECT PriceListId, CurrencyId, CreatedTsUtc, UpdatedTsUtc FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE PriceListId IS NOT NULL AND PriceListId <> ''''
            ) AS AllPriceLists
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.pricelists (code, name, currency_code, created_at, updated_at)
        SELECT PriceListId, ''Pricelist '' + PriceListId, CurrencyId, CreatedTsUtc, UpdatedTsUtc
        FROM PriceListDetails WHERE rn = 1;
    ');

    -- 2.3 Relations (CORRECTED to include all relation codes from both RelationId and PriceRelationId)
    EXEC('
        WITH AllRelationCodes AS (
            SELECT RelationId AS Code FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE RelationId IS NOT NULL AND RelationId <> ''''
            UNION
            SELECT PriceRelationId AS Code FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users WHERE PriceRelationId IS NOT NULL AND PriceRelationId <> ''''
        ),
        RelationDetails AS (
            SELECT
                c.Code,
                u.RelationName,
                u.LanguageId,
                u.CurrencyId,
                u.CountryId,
                u.CreatedTsUtc,
                u.UpdatedTsUtc,
                ROW_NUMBER() OVER(PARTITION BY c.Code ORDER BY u.CreatedTsUtc ASC) as rn
            FROM AllRelationCodes c
            LEFT JOIN ' + @SourceDatabase + '.' + @SourceSchema + '.Users u ON c.Code = u.RelationId
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.relations (code, name, language_code, currency_code, country_code, created_at, updated_at)
        SELECT
            Code,
            ISNULL(RelationName, ''Relation '' + Code),
            LanguageId,
            CurrencyId,
            CountryId,
            ISNULL(CreatedTsUtc, GETUTCDATE()),
            ISNULL(UpdatedTsUtc, GETUTCDATE())
        FROM RelationDetails WHERE rn = 1;
    ');

    -- 2.4 Contacts (CORRECTED with de-duplication)
    EXEC('
        WITH RankedContacts AS (
            SELECT
                ContactId, ContactName, LanguageId, CountryId, ContactEmail, CreatedTsUtc, UpdatedTsUtc,
                ROW_NUMBER() OVER(PARTITION BY ContactId ORDER BY CreatedTsUtc ASC, ContactEmail ASC) as rn
            FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users
            WHERE ContactId IS NOT NULL AND ContactId <> ''''
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.contacts (code, name, language_code, country_code, email, created_at, updated_at)
        SELECT ContactId, ContactName, LanguageId, CountryId, ContactEmail, CreatedTsUtc, UpdatedTsUtc
        FROM RankedContacts
        WHERE rn = 1;
    ');

    -- Step 3: Migrate relationship and association entities

    -- 3.1 Relations_sell_to (CORRECTED with stricter WHERE clauses)
    EXEC('
        WITH SellToDetails AS (
            SELECT u.RelationId, u.LanguageId, u.CurrencyId, u.CountryId, u.IntSalesRepId, u.ExtSalesRepId, u.CompanyId, u.PriceListId, u.PriceRelationId, u.CreatedTsUtc, u.UpdatedTsUtc,
                   ROW_NUMBER() OVER (PARTITION BY u.RelationId ORDER BY u.CreatedTsUtc ASC) as rn
            FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users u
            WHERE u.RelationId IS NOT NULL AND u.RelationId <> ''''
              AND u.CompanyId IS NOT NULL AND u.CompanyId <> ''''
              AND u.LanguageId IS NOT NULL AND u.LanguageId <> ''''
              AND u.CurrencyId IS NOT NULL AND u.CurrencyId <> ''''
              AND u.CountryId IS NOT NULL AND u.CountryId <> ''''
              AND u.IntSalesRepId IS NOT NULL AND u.IntSalesRepId <> ''''
              AND u.ExtSalesRepId IS NOT NULL AND u.ExtSalesRepId <> ''''
              AND u.PriceListId IS NOT NULL AND u.PriceListId <> ''''
              AND u.PriceRelationId IS NOT NULL AND u.PriceRelationId <> ''''
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.relations_sell_to (relation_code, language_code, currency_code, country_code, int_sales_rep_employee_code, ext_sales_rep_employee_code, company_code, pricelist_code, relation_code_for_pricelist, status, created_at, updated_at)
        SELECT RelationId, LanguageId, CurrencyId, CountryId, IntSalesRepId, ExtSalesRepId, CompanyId, PriceListId, PriceRelationId, ''Active'', CreatedTsUtc, UpdatedTsUtc
        FROM SellToDetails WHERE rn = 1;
    ');

    -- 3.2 Contact_relation_sell_to
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.contact_relation_sell_to (contact_code, relation_sell_to_code, created_at, updated_at)
        SELECT DISTINCT u.ContactId, u.RelationId, MIN(u.CreatedTsUtc), MAX(u.UpdatedTsUtc)
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.Users u
        WHERE u.ContactId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.contacts)
          AND u.RelationId IN (SELECT relation_code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.relations_sell_to)
        GROUP BY u.ContactId, u.RelationId;
    ');

    -- 3.3 Contact_attributes (CORRECTED with de-duplication)
    EXEC('
        WITH RankedAttributes AS (
            SELECT ua.ContactId, ua.AttributeCode, ua.AttributeValue, ua.CreatedTsUtc, ua.UpdatedTsUtc,
                   ROW_NUMBER() OVER(PARTITION BY ua.ContactId, ua.AttributeCode ORDER BY ua.CreatedTsUtc ASC) as rn
            FROM ' + @SourceDatabase + '.' + @SourceSchema + '.User_Attributes ua
            WHERE ua.ContactId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.contacts)
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.contact_attributes (contact_code, attribute_code, attribute_value, created_at, updated_at)
        SELECT ContactId, AttributeCode, AttributeValue, CreatedTsUtc, UpdatedTsUtc
        FROM RankedAttributes WHERE rn = 1;
    ');

    -- 3.4 Relation_attributes (CORRECTED with de-duplication)
    EXEC('
        WITH RankedRelationAttributes AS (
            SELECT ua.RelationId, ua.AttributeCode, ua.AttributeValue, ua.CreatedTsUtc, ua.UpdatedTsUtc,
                   ROW_NUMBER() OVER(PARTITION BY ua.RelationId, ua.AttributeCode ORDER BY ua.CreatedTsUtc ASC) as rn
            FROM ' + @SourceDatabase + '.' + @SourceSchema + '.User_Attributes ua
            WHERE ua.RelationId IS NOT NULL AND ua.RelationId <> ''''
              AND ua.RelationId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.relations)
        )
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.relation_attributes (relation_code, attribute_code, attribute_value, created_at, updated_at)
        SELECT RelationId, AttributeCode, AttributeValue, CreatedTsUtc, UpdatedTsUtc
        FROM RankedRelationAttributes WHERE rn = 1;
    ');

    -- 3.5 Product_availability (CORRECTED with stricter WHERE clause)
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.product_availability (product_code, company_code, warehouse_code, unit_code, stock, dalt, created_at, updated_at)
        SELECT pa.ProductId, pa.CompanyId, pa.WarehouseId, pa.UnitId, pa.Stock, pa.DALT, pa.CreatedTsUtc, pa.UpdatedTsUtc
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductAvailability pa
        WHERE pa.UnitId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.units)
          AND pa.ProductId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.products)
          AND pa.CompanyId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.companies)
          AND pa.WarehouseId IN (SELECT code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.warehouses);
    ');

    -- 3.6 Product_salesprices
    EXEC('
        INSERT INTO ' + @TargetDatabase + '.' + @TargetSchema + '.product_salesprices (relation_sell_to_code, pricelist_code, currency_code, product_code, unit_code, min_quantity, gross_price, discount, nett_price, valid_from, valid_until, created_at, updated_at)
        SELECT psp.RelationId, psp.PriceListId, psp.CurrencyId, psp.ProductId, psp.UnitId, psp.MinQuantity, psp.GrossPrice, psp.Discount, psp.NettPrice, psp.ValidFrom, psp.ValidUntil, psp.CreatedTsUtc, psp.UpdatedTsUtc
        FROM ' + @SourceDatabase + '.' + @SourceSchema + '.ProductSalesPrices psp
        WHERE psp.RelationId IN (SELECT relation_code FROM ' + @TargetDatabase + '.' + @TargetSchema + '.relations_sell_to);
    ');

    -- Final step: Link users to contacts
    EXEC('
        UPDATE c SET c.user_id = u.id
        FROM ' + @TargetDatabase + '.' + @TargetSchema + '.contacts c
        JOIN ' + @TargetDatabase + '.' + @TargetSchema + '.users u ON c.email = u.email;
    ');

    COMMIT TRANSACTION;
    PRINT 'Migration completed successfully.';
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT 'Migration failed. Rolling back changes.';
    PRINT ERROR_MESSAGE();
    THROW;
END CATCH;
