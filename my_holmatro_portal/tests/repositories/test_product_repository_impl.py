import datetime

import pytest
from src.models.response.product_response import ProductSalesInfoResponse
from src.shared.exceptions import ProductAvailabilityNotFoundError
from src.shared.exceptions import ProductNotFoundError
from src.shared.exceptions import ProductPriceNotFoundError
from src.shared.mappers.product_sales_info_mapper import ProductSalesInfoMapper
from tests.repositories.conftest import seed_base_data
from tests.repositories.conftest import seed_products
from tests.repositories.conftest import seed_valid_user


def test_get_product_sales_info_with_valid_user_and_products(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_numbers = [
        seed_products["products"][0].code,
        seed_products["products"][1].code,
    ]
    # Act
    sales_info_results = product_repository.get_product_sales_info(user_email, article_numbers)
    result = ProductSalesInfoMapper.to_product_sales_info_list(sales_info_results)

    # Assert
    assert isinstance(result, ProductSalesInfoResponse)
    assert isinstance(result.sales_info, list)
    assert len(result.sales_info) == 2

    product_codes = [p.article_no for p in result.sales_info]
    assert seed_products["products"][0].code in product_codes
    assert seed_products["products"][1].code in product_codes

    product1 = next(p for p in result.sales_info if p.article_no == seed_products["products"][0].code)
    assert product1.gross_price == 1000.0
    assert product1.nett_price == 900.0
    assert product1.stock == 50.0

    product2 = next(p for p in result.sales_info if p.article_no == seed_products["products"][1].code)
    assert product2.gross_price == 1500.0
    assert product2.nett_price == 1275.0
    assert product2.stock == 30.0


def test_get_product_sales_info_with_invalid_user(product_repository, seed_products):
    # Arrange
    user_email = "<EMAIL>"
    article_numbers = [seed_products["products"][0].code]

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, article_numbers)


def test_get_product_sales_info_with_empty_article_numbers(product_repository, seed_products):
    # Arrange
    user_email = seed_products["contact"].email

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, [])


def test_get_product_sales_info_with_nonexistent_product(product_repository, seed_products):
    # Arrange
    user_email = seed_products["contact"].email
    article_numbers = ["NONEXISTENT"]

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, article_numbers)


def test_get_product_sales_info_with_mixed_products(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_numbers = [seed_products["products"][0].code, "NONEXISTENT"]

    # Act
    sales_info_results = product_repository.get_product_sales_info(user_email, article_numbers)
    result = ProductSalesInfoMapper.to_product_sales_info_list(sales_info_results)

    # Assert
    assert result is not None
    assert len(result.sales_info) == 1
    assert result.sales_info[0].article_no == seed_products["products"][0].code


def test_user_with_no_contact_relation(product_repository, seed_user_no_contacts, seed_products):
    # Arrange
    user_email = seed_user_no_contacts.email
    article_numbers = [seed_products["products"][0].code]

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, article_numbers)


def test_clean_article_numbers(product_repository):
    # Arrange
    article_numbers = ["  PROD001  ", "", "  ", None, "PROD002"]

    # Act
    cleaned = product_repository._clean_article_numbers(article_numbers)

    # Assert
    assert cleaned == ["PROD001", "PROD002"]
    assert len(cleaned) == 2


def test_whitespace_and_none_article_numbers(product_repository, seed_products):
    # Arrange
    user_email = seed_products["contact"].email
    article_numbers = ["  ", "", None]

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, article_numbers)


def test_get_product_price_with_valid_user_and_product(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    # Act
    result = product_repository.get_product_price(user_email, article_number)

    # Assert
    assert result is not None
    assert result.article_no == article_number
    assert result.gross_price == 1000.0
    assert result.nett_price == 900.0
    assert result.discount_percentage == 10.0
    assert result.currency == "USD"
    assert result.unit is not None


def test_get_product_price_with_invalid_user(product_repository, seed_products):
    # Arrange
    user_email = "<EMAIL>"
    article_number = seed_products["products"][0].code

    # Act & Assert
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, article_number)


def test_get_product_price_with_nonexistent_product(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = "NONEXISTENT"

    # Act & Assert
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, article_number)


def test_get_product_price_with_user_no_contact_relation(product_repository, seed_user_no_contacts, seed_products):
    # Arrange
    user_email = seed_user_no_contacts.email
    article_number = seed_products["products"][0].code

    # Act & Assert
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, article_number)


def test_get_product_price_handles_exception(monkeypatch, product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    def raise_exc(*args, **kwargs):
        raise Exception("Simulated DB error")

    monkeypatch.setattr(product_repository.session, "execute", raise_exc)

    # Act & Assert
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, article_number)


def test_get_product_sales_info_handles_exception(monkeypatch, product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_numbers = [seed_products["products"][0].code]

    def raise_exc(*args, **kwargs):
        raise Exception("Simulated DB error")

    monkeypatch.setattr(product_repository.session, "execute", raise_exc)

    # Act & Assert
    with pytest.raises(ProductNotFoundError):
        product_repository.get_product_sales_info(user_email, article_numbers)


def test_format_results_rounds_and_returns_objects(product_repository):
    # Arrange
    class FakeRow:
        def _asdict(self):
            return {
                "article_no": "P1",
                "gross_price": 1000.1234,
                "nett_price": 900.5678,
                "discount_percentage": 10.9876,
                "currency": "USD",
                "unit": "PCS",
                "stock": 50.555,
                "restock_days": 5,
                "created_ts_utc": datetime.datetime.now(datetime.timezone.utc),
                "updated_ts_utc": datetime.datetime.now(datetime.timezone.utc),
            }

    fake_rows = [FakeRow()]

    # Act - Use the mapper directly instead of format_results method

    results = ProductSalesInfoMapper.to_product_sales_info_list(fake_rows)
    assert isinstance(results.sales_info, list)
    assert len(results.sales_info) == 1

    # Assert
    result = results.sales_info[0]
    assert result.gross_price == 1000.12
    assert result.nett_price == 900.57
    assert result.discount_percentage == 10.99
    assert result.stock == 50.55
    assert result.article_no == "P1"
    assert result.currency == "USD"
    assert result.unit == "PCS"


def test_get_availability_query_returns_correct_query(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    # Act
    query = product_repository._get_availability_query(user_email, article_number)

    # Assert
    # The query should be a SQLAlchemy selectable
    assert hasattr(query, "compile")
    # The query should select the correct label for article_no
    columns = [c.key for c in query.selected_columns]
    assert "article_no" in columns
    assert "unit" in columns
    assert "stock" in columns
    assert "restock_days" in columns
    assert "created_ts_utc" in columns
    assert "updated_ts_utc" in columns


def test_get_product_availability_with_valid_user_and_product(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    # Act
    result = product_repository.get_product_availability(user_email, article_number)

    # Assert
    assert result is not None
    assert result.article_no == article_number
    assert hasattr(result, "stock")
    assert hasattr(result, "restock_days")
    assert hasattr(result, "created_ts_utc")
    assert hasattr(result, "updated_ts_utc")


def test_get_product_availability_with_invalid_user(product_repository, seed_products):
    # Arrange
    user_email = "<EMAIL>"
    article_number = seed_products["products"][0].code

    # Act & Assert
    with pytest.raises(ProductAvailabilityNotFoundError):
        product_repository.get_product_availability(user_email, article_number)


def test_get_product_availability_with_nonexistent_product(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = "NONEXISTENT"

    # Act & Assert
    with pytest.raises(ProductAvailabilityNotFoundError):
        product_repository.get_product_availability(user_email, article_number)


def test_get_product_availability_handles_exception(monkeypatch, product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    def raise_exc(*args, **kwargs):
        raise Exception("Simulated DB error")

    monkeypatch.setattr(product_repository.session, "execute", raise_exc)

    # Act & Assert
    with pytest.raises(ProductAvailabilityNotFoundError):
        product_repository.get_product_availability(user_email, article_number)


def test_get_product_availability_no_article_number(product_repository, seed_products):
    # Arrange
    user_email = seed_products["user"].email
    article_number = "   "  # whitespace only

    # Act & Assert
    with pytest.raises(ProductAvailabilityNotFoundError):
        product_repository.get_product_availability(user_email, article_number)


def test_get_availability_query_with_seeded_data(product_repository, seed_products):
    """
    This integration test specifically calls the private method _get_availability_query
    to ensure its code is covered, using data from the seed_products fixture.
    """
    # Arrange: Use seeded data to get a valid user and product
    user_email = seed_products["user"].email
    article_number = seed_products["products"][0].code

    # Act: Call the private method
    query = product_repository._get_availability_query(user_email, article_number)

    # Assert: Execute the query and validate the result against the seeded data
    assert query is not None

    result = product_repository.session.execute(query).first()
    assert result is not None

    # Check that the columns from the method body are present and correct
    assert hasattr(result, "article_no")
    assert hasattr(result, "unit")
    assert hasattr(result, "stock")
    assert hasattr(result, "restock_days")

    # Verify the values from the database
    product_availability = next(p for p in seed_products["product_availability"] if p.product_code == article_number)
    assert result.article_no == article_number
    assert result.stock == product_availability.stock
    assert result.restock_days == product_availability.dalt


def test_complex_pricing_scenarios(product_repository, seed_products):
    """Test that the repository correctly handles complex pricing scenarios."""
    # Arrange
    user_email = seed_products["user"].email

    product3_code = seed_products["products"][2].code  # PROD003
    price3 = product_repository.get_product_price(user_email, product3_code)
    assert price3 is not None
    assert price3.unit == "Pieces"
    assert price3.nett_price == 80.0
    assert price3.gross_price == 100.0
    assert price3.discount_percentage == 20.0

    # Test product with stock but no price (PROD005)
    product_no_price_code = seed_products["products"][4].code  # PROD005
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, product_no_price_code)

    # Test product with price but no stock (PROD006)
    product_no_stock_code = seed_products["products"][5].code  # PROD006
    no_stock_price = product_repository.get_product_price(user_email, product_no_stock_code)

    assert no_stock_price is not None
    assert no_stock_price.nett_price == 2700.0
    assert no_stock_price.gross_price == 3000.0
    assert no_stock_price.discount_percentage == 10.0

    # Get availability for product with no stock
    no_stock_availability = product_repository.get_product_availability(user_email, product_no_stock_code)
    assert no_stock_availability is not None
    assert no_stock_availability.stock == 0.0
    assert no_stock_availability.restock_days == 15

    # Test multi-relation user with access to products in different currencies
    multi_user_email = seed_products["multi_relation_user"].email

    # PROD004 is only available in EUR for REL002
    product4_code = seed_products["products"][3].code  # PROD004
    euro_price = product_repository.get_product_price(multi_user_email, product4_code)

    assert euro_price is not None
    assert euro_price.currency == "EUR"
    assert euro_price.nett_price == 2250.0
    assert euro_price.gross_price == 2500.0
    assert euro_price.discount_percentage == 10.0

    # Test expired pricing (PROD007)
    expired_product_code = seed_products["products"][6].code  # PROD007
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, expired_product_code)

    # Test future pricing (PROD008)
    future_product_code = seed_products["products"][7].code  # PROD008
    with pytest.raises(ProductPriceNotFoundError):
        product_repository.get_product_price(user_email, future_product_code)

    # Test bulk pricing scenario with PROD003
    bulk_product_sales_info_results = product_repository.get_product_sales_info(user_email, [product3_code])
    bulk_product_sales_info = ProductSalesInfoMapper.to_product_sales_info_list(bulk_product_sales_info_results)

    assert bulk_product_sales_info is not None
    assert len(bulk_product_sales_info.sales_info) == 2
    bulk_product = bulk_product_sales_info.sales_info[0]
    assert bulk_product.nett_price == 80.0
    assert bulk_product.unit == "Pieces"
    assert bulk_product.gross_price == 100.0
    assert bulk_product.discount_percentage == 20.0
