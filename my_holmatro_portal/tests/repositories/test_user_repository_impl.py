import uuid

import pytest
from src.models.db.contacts import Contacts
from src.models.db.users import Users
from src.models.schemas.user import UserAggregate
from src.shared.exceptions import UserNotFoundError
from tests.repositories.conftest import seed_valid_user


@pytest.mark.parametrize("email, expected", [("<EMAIL>", True), ("<EMAIL>", False)])
def test_get_by_email(user_repository, seed_valid_user, email, expected):
    """Test retrieval of aggregated user data by email."""
    if expected:
        # Act: The method now returns a UserAggregate object
        user_aggregate: UserAggregate = user_repository.get_by_email(email)

        # Assert: Check the attributes of the UserAggregate object
        assert user_aggregate is not None

        # Assertions against the 'user_db' attribute
        assert user_aggregate.user_db.email == "<EMAIL>"

        # Assertions against the 'primary_contact' attribute
        assert user_aggregate.primary_contact.code == "CONTACT123"
        assert user_aggregate.primary_contact.name == "Test Contact"

        # Assertions against other top-level attributes
        assert user_aggregate.primary_relation_name == "Test Relation"
        assert user_aggregate.country.code == "NL"
        assert user_aggregate.language.code == "EN"
        assert user_aggregate.primary_currency_id == "EUR"
        assert len(user_aggregate.all_relations) == 1

        # Check relation details
        relation = user_aggregate.all_relations[0]
        assert relation.company_id == "Holmatro"
        assert relation.relation_id == "REL1"
        assert relation.relation_name == "Test Relation"
        assert relation.int_sales_rep_id == "EMP1"
        assert relation.int_sales_rep_name == "Internal Rep"
        assert relation.int_sales_rep_email == "<EMAIL>"
        assert relation.ext_sales_rep_id == "EMP2"
        assert relation.ext_sales_rep_name == "External Rep"
        assert relation.ext_sales_rep_email == "<EMAIL>"
    else:
        # Act & Assert
        with pytest.raises(UserNotFoundError) as exc_info:
            user_repository.get_by_email(email)
        assert str(exc_info.value) == f"User with email '{email}' not found"


@pytest.fixture
def seed_user_no_contacts(db_session):
    """Create a user without any contacts for testing."""
    user = Users(
        id=uuid.uuid4(),
        email="<EMAIL>",
        portal_access=True,
    )

    db_session.add(user)
    db_session.commit()

    return user


def test_get_relations_with_data(user_repository, seed_valid_user):
    """Test relation data retrieval for a user with relations."""
    # Act
    relations, primary_name, primary_currency = user_repository._get_relations(seed_valid_user["contact"].code)

    # Assert
    assert len(relations) == 1
    assert primary_name == "Test Relation"
    assert primary_currency == "EUR"
    assert relations[0].company_id == "Holmatro"
    assert relations[0].relation_id == "REL1"
    assert relations[0].relation_name == "Test Relation"


def test_get_relations_empty(user_repository, db_session):
    """Test relation data retrieval for a user with no relations."""
    # Create contact with no relations
    contact = Contacts(
        code="NO_RELATIONS",
        name="Contact Without Relations",
        email="<EMAIL>",
        country_code="NL",
        language_code="EN",
    )

    db_session.add(contact)
    db_session.commit()

    # Act
    relations, primary_name, primary_currency = user_repository._get_relations(contact.code)

    # Assert
    assert relations == []
    assert primary_name is None
    assert primary_currency is None


def test_get_relation_sell_to_attributes_with_data(user_repository, seed_valid_user):
    """Test relation sell to attributes retrieval when attributes exist."""
    # Act
    relation_code = seed_valid_user["relation_sell_to"].relation_code
    attributes = user_repository._get_relation_sell_to_attributes([relation_code])

    # Assert
    assert len(attributes) == 1

    # Check if we have the expected SF attributes
    attribute = attributes[0]
    assert hasattr(attribute, "sf_category")
    assert hasattr(attribute, "sf_sub_category")

    # Check attribute values
    assert attribute.sf_category == "Test Category"
    assert attribute.sf_sub_category == "Test Subcategory"


def test_get_relation_sell_to_attributes_empty(user_repository, db_session):
    """Test relation sell to attributes retrieval when no attributes exist."""
    # Create contact with no attributes
    contact = Contacts(
        code="NO_ATTRIBUTES",
        name="Contact Without Attributes",
        email="<EMAIL>",
        country_code="NL",
        language_code="EN",
    )

    db_session.add(contact)
    db_session.commit()

    # Act
    attributes = user_repository._get_relation_sell_to_attributes([contact.code])

    # Assert
    assert attributes == []
