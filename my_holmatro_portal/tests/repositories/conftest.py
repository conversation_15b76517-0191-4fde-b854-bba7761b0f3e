import uuid
from datetime import datetime
from datetime import timedelta
from datetime import timezone

import pytest
from sqlalchemy import DateTime
from sqlalchemy import TypeDecorator
from sqlalchemy import create_engine
from sqlalchemy import event
from sqlalchemy.dialects.sqlite.base import SQLiteTypeCompiler
from sqlalchemy.orm import sessionmaker
from src.models.db.attributes import Attributes
from src.models.db.base import Base
from src.models.db.companies import Companies
from src.models.db.contact_attributes import ContactAttributes
from src.models.db.contact_relation_sell_to import ContactRelationSellTo
from src.models.db.contacts import Contacts
from src.models.db.countries import Countries
from src.models.db.currencies import Currencies
from src.models.db.employees import Employees
from src.models.db.languages import Languages
from src.models.db.pricelists import Pricelists
from src.models.db.product_availability import ProductAvailability
from src.models.db.product_salesprices import ProductSalesPrices
from src.models.db.products import Products
from src.models.db.relation_attributes import RelationAttributes
from src.models.db.relations import Relations
from src.models.db.relations_sell_to import RelationsSellTo
from src.models.db.units import Units
from src.models.db.users import Users
from src.models.db.warehouses import Warehouses
from src.services.product_repository_impl import ProductRepositoryImpl
from src.services.user_repository_impl import UserRepositoryImpl


class DATETIME2(TypeDecorator):
    """Custom DATETIME type for SQLite compatibility with SQL Server."""

    impl = DateTime
    cache_ok = True


SQLiteTypeCompiler.visit_DATETIME2 = SQLiteTypeCompiler.visit_DATETIME  # type: ignore[attr-defined]


def sysutcdatetime():
    """Mimics SQL Server's sysutcdatetime() function."""
    return datetime.now(timezone.utc)


@pytest.fixture(scope="function")
def db_session():
    """Setup and teardown an in-memory SQLite database for testing."""
    engine = create_engine("sqlite:///:memory:", echo=False)

    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, _connection_record):
        dbapi_connection.create_function("sysutcdatetime", 0, lambda: sysutcdatetime().isoformat())

    TestingSessionLocal = sessionmaker(bind=engine, expire_on_commit=False)
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    yield session
    session.close()
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def user_repository(db_session):
    """Return a UserRepositoryImpl instance connected to the test database."""
    return UserRepositoryImpl(db_session)


@pytest.fixture
def product_repository(db_session):
    """Return a ProductRepositoryImpl instance connected to the test database."""
    return ProductRepositoryImpl(db_session)


@pytest.fixture
def seed_base_data(db_session):
    """Create common base data used across tests."""
    country = Countries(id=uuid.uuid4(), code="NL", code_iso="NLD", name="Netherlands")
    language = Languages(id=uuid.uuid4(), code="EN", code_iso="ENG", name="English")
    currency = Currencies(id=uuid.uuid4(), code="EUR", code_iso="EUR", name="Euro")

    # Create company
    company = Companies(id=uuid.uuid4(), code="COMP1", name="Holmatro", desc="Holmatro Company")

    # Create sales reps
    int_sales_rep = Employees(id=uuid.uuid4(), code="EMP1", name="Internal Rep", email="<EMAIL>")
    ext_sales_rep = Employees(id=uuid.uuid4(), code="EMP2", name="External Rep", email="<EMAIL>")

    # Create pricelist
    pricelist = Pricelists(id=uuid.uuid4(), code="PL1", name="Standard Pricelist", currency_code="EUR")

    db_session.add_all([country, language, currency, company, int_sales_rep, ext_sales_rep, pricelist])
    db_session.commit()

    return {
        "country": country,
        "language": language,
        "currency": currency,
        "company": company,
        "int_sales_rep": int_sales_rep,
        "ext_sales_rep": ext_sales_rep,
        "pricelist": pricelist,
    }


@pytest.fixture
def seed_valid_user(db_session, seed_base_data):
    """Create a test user with contacts, relations, and attributes."""
    # Create user
    user = Users(
        id=uuid.uuid4(),
        email="<EMAIL>",
        portal_access=True,
    )

    # Create contact
    contact = Contacts(
        code="CONTACT123",
        name="Test Contact",
        country_code=seed_base_data["country"].code,
        language_code=seed_base_data["language"].code,
        email="<EMAIL>",
    )

    # Create relation
    relation = Relations(
        id=uuid.uuid4(),
        code="REL1",
        name="Test Relation",
        language_code=seed_base_data["language"].code,
        currency_code=seed_base_data["currency"].code,
        country_code=seed_base_data["country"].code,
    )

    # Create relation sell to
    relation_sell_to = RelationsSellTo(
        id=uuid.uuid4(),
        relation_code="REL1",
        language_code=seed_base_data["language"].code,
        currency_code=seed_base_data["currency"].code,
        country_code=seed_base_data["country"].code,
        int_sales_rep_employee_code=seed_base_data["int_sales_rep"].code,
        ext_sales_rep_employee_code=seed_base_data["ext_sales_rep"].code,
        company_code=seed_base_data["company"].code,
        pricelist_code=seed_base_data["pricelist"].code,
        relation_code_for_pricelist="REL1",
        status="ACTIVE",
    )

    # Link contact to relation
    contact_relation = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact.code,
        relation_sell_to_code="REL1",
    )

    # Create attributes with proper code format
    category_attr = Attributes(id=uuid.uuid4(), code="C0004C001", name="Test Category")
    subcategory_attr = Attributes(id=uuid.uuid4(), code="C0004S001", name="Test Subcategory")
    proposition_attr = Attributes(id=uuid.uuid4(), code="C0004", name="Holmatro Partner Portal")

    db_session.add_all([category_attr, subcategory_attr, proposition_attr])
    db_session.commit()

    # Create contact attributes
    contact_attributes = [
        ContactAttributes(
            id=uuid.uuid4(),
            contact_code=contact.code,
            attribute_code=proposition_attr.code,
            attribute_value="ja",
        ),
        ContactAttributes(
            id=uuid.uuid4(),
            contact_code=contact.code,
            attribute_code=category_attr.code,
            attribute_value="ja",
        ),
        ContactAttributes(
            id=uuid.uuid4(),
            contact_code=contact.code,
            attribute_code=subcategory_attr.code,
            attribute_value="ja",
        ),
    ]

    relation_attributes = [
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code=relation.code,
            attribute_code=category_attr.code,
            attribute_value="ja",
        ),
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code=relation.code,
            attribute_code=subcategory_attr.code,
            attribute_value="ja",
        ),
    ]

    db_session.add_all(
        [
            user,
            contact,
            relation,
            relation_sell_to,
            contact_relation,
        ]
        + contact_attributes
        + relation_attributes
    )
    db_session.commit()

    return {
        "user": user,
        "contact": contact,
        "relation_sell_to": relation_sell_to,
        "category_attr": category_attr,
        "subcategory_attr": subcategory_attr,
        "proposition_attr": proposition_attr,
    }


@pytest.fixture
def seed_user_no_contacts(db_session):
    """Create a user without any contacts for testing edge cases."""
    user = Users(
        id=uuid.uuid4(),
        email="<EMAIL>",
        portal_access=True,
    )
    db_session.add(user)
    db_session.commit()
    return user


@pytest.fixture
def seed_products(db_session):
    """Seed the database with comprehensive test data."""
    # Create multiple languages
    language_en = Languages(id=uuid.uuid4(), code="EN", code_iso="en-US", name="English")
    language_es = Languages(id=uuid.uuid4(), code="ES", code_iso="es-ES", name="Spanish")
    db_session.add_all([language_en, language_es])

    # Create multiple countries
    country_us = Countries(id=uuid.uuid4(), code="US", code_iso="USA", name="United States")
    country_de = Countries(id=uuid.uuid4(), code="DE", code_iso="DEU", name="Germany")
    country_gb = Countries(id=uuid.uuid4(), code="GB", code_iso="GBR", name="United Kingdom")
    db_session.add_all([country_us, country_de, country_gb])

    # Create multiple currencies
    currency_usd = Currencies(id=uuid.uuid4(), code="USD", code_iso="USD", name="US Dollar")
    currency_eur = Currencies(id=uuid.uuid4(), code="EUR", code_iso="EUR", name="Euro")
    currency_gbp = Currencies(id=uuid.uuid4(), code="GBP", code_iso="GBP", name="British Pound")
    db_session.add_all([currency_usd, currency_eur, currency_gbp])

    # Create companies and warehouses
    company = Companies(id=uuid.uuid4(), code="HOLMATRO", name="Holmatro", desc="Holmatro Company")
    warehouse1 = Warehouses(id=uuid.uuid4(), code="WH001", name="Main Warehouse", company_code=company.code)
    warehouse2 = Warehouses(
        id=uuid.uuid4(),
        code="WH002",
        name="Secondary Warehouse",
        company_code=company.code,
    )
    db_session.add_all([company, warehouse1, warehouse2])

    # Create sales representatives
    int_sales_rep = Employees(
        id=uuid.uuid4(),
        code="EMP001",
        name="Internal Sales Rep",
        email="<EMAIL>",
    )
    ext_sales_rep = Employees(
        id=uuid.uuid4(),
        code="EMP002",
        name="External Sales Rep",
        email="<EMAIL>",
    )
    db_session.add_all([int_sales_rep, ext_sales_rep])

    # Create multiple relations and pricelists
    relation1 = Relations(
        id=uuid.uuid4(),
        code="REL001",
        name="US Customer",
        language_code="EN",
        currency_code="USD",
        country_code="US",
    )
    relation2 = Relations(
        id=uuid.uuid4(),
        code="REL002",
        name="EU Customer",
        language_code="EN",
        currency_code="EUR",
        country_code="DE",
    )
    relation3 = Relations(
        id=uuid.uuid4(),
        code="REL003",
        name="UK Customer",
        language_code="EN",
        currency_code="GBP",
        country_code="GB",
    )

    pricelist_usd = Pricelists(id=uuid.uuid4(), code="PL001", name="USD Pricelist", currency_code="USD")
    pricelist_eur = Pricelists(id=uuid.uuid4(), code="PL002", name="EUR Pricelist", currency_code="EUR")
    pricelist_gbp = Pricelists(id=uuid.uuid4(), code="PL003", name="GBP Pricelist", currency_code="GBP")

    db_session.add_all([relation1, relation2, relation3, pricelist_usd, pricelist_eur, pricelist_gbp])

    # Add attributes for the relations to make them valid
    category_attr = Attributes(id=uuid.uuid4(), code="C0004C001", name="Test Category")
    subcategory_attr = Attributes(id=uuid.uuid4(), code="C0004S001", name="Test Subcategory")
    db_session.add_all([category_attr, subcategory_attr])
    db_session.commit()

    relation_attributes = [
        # REL001 has both attributes
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code="REL001",
            attribute_code=category_attr.code,
            attribute_value="ja",
        ),
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code="REL001",
            attribute_code=subcategory_attr.code,
            attribute_value="ja",
        ),
        # REL002 has both attributes for the multi-relation user test
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code="REL002",
            attribute_code=category_attr.code,
            attribute_value="ja",
        ),
        RelationAttributes(
            id=uuid.uuid4(),
            relation_code="REL002",
            attribute_code=subcategory_attr.code,
            attribute_value="ja",
        ),
    ]
    db_session.add_all(relation_attributes)

    # Create relation_sell_to records
    relation_sell_to1 = RelationsSellTo(
        id=uuid.uuid4(),
        relation_code="REL001",
        language_code="EN",
        currency_code="USD",
        country_code="US",
        int_sales_rep_employee_code="EMP001",
        ext_sales_rep_employee_code="EMP002",
        company_code="HOLMATRO",
        pricelist_code="PL001",
        relation_code_for_pricelist="REL001",
        status="ACTIVE",
    )
    relation_sell_to2 = RelationsSellTo(
        id=uuid.uuid4(),
        relation_code="REL002",
        language_code="EN",
        currency_code="EUR",
        country_code="DE",
        int_sales_rep_employee_code="EMP001",
        ext_sales_rep_employee_code="EMP002",
        company_code="HOLMATRO",
        pricelist_code="PL002",
        relation_code_for_pricelist="REL002",
        status="ACTIVE",
    )
    relation_sell_to3 = RelationsSellTo(
        id=uuid.uuid4(),
        relation_code="REL003",
        language_code="EN",
        currency_code="GBP",
        country_code="GB",
        int_sales_rep_employee_code="EMP001",
        ext_sales_rep_employee_code="EMP002",
        company_code="HOLMATRO",
        pricelist_code="PL003",
        relation_code_for_pricelist="REL003",
        status="INACTIVE",
    )
    db_session.add_all([relation_sell_to1, relation_sell_to2, relation_sell_to3])

    # Create multiple units
    unit_pcs = Units(id=uuid.uuid4(), code="PCS", code_iso="PCS", name="Pieces")
    unit_box = Units(id=uuid.uuid4(), code="BOX", code_iso="BOX", name="Box")
    unit_kg = Units(id=uuid.uuid4(), code="KG", code_iso="KG", name="Kilograms")
    db_session.add_all([unit_pcs, unit_box, unit_kg])

    # Create multiple users and contacts
    user1 = Users(id=uuid.uuid4(), email="<EMAIL>", portal_access=True)
    contact1 = Contacts(
        code="CONT001",
        name="John Doe",
        language_code="EN",
        country_code="US",
        email="<EMAIL>",
    )
    contact_relation1 = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact1.code,
        relation_sell_to_code="REL001",
    )

    user2 = Users(id=uuid.uuid4(), email="<EMAIL>", portal_access=True)
    contact2 = Contacts(
        code="CONT002",
        name="Jane Smith",
        language_code="EN",
        country_code="DE",
        email="<EMAIL>",
    )
    contact_relation2a = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact2.code,
        relation_sell_to_code="REL001",
    )
    contact_relation2b = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact2.code,
        relation_sell_to_code="REL002",
    )

    user3 = Users(id=uuid.uuid4(), email="<EMAIL>", portal_access=False)
    contact3 = Contacts(
        code="CONT003",
        name="No Portal User",
        language_code="EN",
        country_code="GB",
        email="<EMAIL>",
    )
    contact_relation3 = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact3.code,
        relation_sell_to_code="REL003",
    )

    db_session.add_all(
        [
            user1,
            user2,
            user3,
            contact1,
            contact2,
            contact3,
            contact_relation1,
            contact_relation2a,
            contact_relation2b,
            contact_relation3,
        ]
    )

    # Create diverse products
    products = [
        Products(id=uuid.uuid4(), code="PROD001", name="Hydraulic Cutter"),
        Products(id=uuid.uuid4(), code="PROD002", name="Spreader Tool"),
        Products(id=uuid.uuid4(), code="PROD003", name="Multi-Unit Product"),
        Products(id=uuid.uuid4(), code="PROD004", name="EUR Only Product"),
        Products(id=uuid.uuid4(), code="PROD005", name="No Price Product"),
        Products(id=uuid.uuid4(), code="PROD006", name="No Stock Product"),
        Products(id=uuid.uuid4(), code="PROD007", name="Expired Price Product"),
        Products(id=uuid.uuid4(), code="PROD008", name="Future Price Product"),
    ]
    db_session.add_all(products)

    # Create complex pricing scenarios
    now = datetime.now(timezone.utc)
    past_date = now - timedelta(days=30)
    future_date = now + timedelta(days=365)
    expired_date = now - timedelta(days=1)

    product_prices = [
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD001",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=1000.0,
            discount=10.0,
            nett_price=900.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD002",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=1500.0,
            discount=15.0,
            nett_price=1275.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL002",
            pricelist_code="PL002",
            currency_code="EUR",
            product_code="PROD002",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=1300.0,
            discount=12.0,
            nett_price=1144.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD003",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=100.0,
            discount=0.0,
            nett_price=100.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD003",
            unit_code="BOX",
            min_quantity=1.0,
            gross_price=1000.0,
            discount=5.0,
            nett_price=950.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD003",
            unit_code="PCS",
            min_quantity=10.0,
            gross_price=100.0,
            discount=20.0,
            nett_price=80.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL002",
            pricelist_code="PL002",
            currency_code="EUR",
            product_code="PROD004",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=2500.0,
            discount=10.0,
            nett_price=2250.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD006",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=3000.0,
            discount=10.0,
            nett_price=2700.0,
            valid_from=past_date,
            valid_until=future_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD007",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=500.0,
            discount=0.0,
            nett_price=500.0,
            valid_from=past_date,
            valid_until=expired_date,
        ),
        ProductSalesPrices(
            id=uuid.uuid4(),
            relation_sell_to_code="REL001",
            pricelist_code="PL001",
            currency_code="USD",
            product_code="PROD008",
            unit_code="PCS",
            min_quantity=1.0,
            gross_price=800.0,
            discount=0.0,
            nett_price=800.0,
            valid_from=future_date,
            valid_until=future_date + timedelta(days=365),
        ),
    ]
    db_session.add_all(product_prices)

    # Create product availability
    product_availability = [
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD001",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=50.0,
            dalt=5,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD002",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=30.0,
            dalt=3,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD003",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=100.0,
            dalt=2,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD003",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="BOX",
            stock=10.0,
            dalt=7,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD004",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=25.0,
            dalt=4,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD005",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=0.0,
            dalt=10,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD006",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=0.0,
            dalt=15,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD007",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=20.0,
            dalt=6,
        ),
        ProductAvailability(
            id=uuid.uuid4(),
            product_code="PROD008",
            company_code="HOLMATRO",
            warehouse_code="WH001",
            unit_code="PCS",
            stock=15.0,
            dalt=8,
        ),
    ]
    db_session.add_all(product_availability)
    db_session.commit()

    return {
        "user": user1,
        "multi_relation_user": user2,
        "no_portal_user": user3,
        "contact": contact1,
        "multi_contact": contact2,
        "company": company,
        "warehouses": [warehouse1, warehouse2],
        "int_sales_rep": int_sales_rep,
        "ext_sales_rep": ext_sales_rep,
        "relations": [relation1, relation2, relation3],
        "relation_sell_to": relation_sell_to1,
        "relation_sell_to_eur": relation_sell_to2,
        "relation_sell_to_inactive": relation_sell_to3,
        "products": products,
        "product_prices": product_prices,
        "product_availability": product_availability,
        "currencies": [currency_usd, currency_eur, currency_gbp],
        "countries": [country_us, country_de, country_gb],
        "languages": [language_en, language_es],
        "units": [unit_pcs, unit_box, unit_kg],
    }


@pytest.fixture
def seed_contact_data(db_session, seed_base_data):
    """Create test contact data."""
    contact = Contacts(
        code="CONTACT123",
        name="Test Contact",
        country_code=seed_base_data["country"].code,
        language_code=seed_base_data["language"].code,
        email="<EMAIL>",
    )

    contact_attribute = ContactAttributes(
        id=uuid.uuid4(),
        contact_code=contact.code,
        attribute_code="DISCOUNT",
        attribute_value="10",
    )

    relation = Relations(
        id=uuid.uuid4(),
        code="REL123",
        name="Relation for Contact",
        language_code="EN",
        currency_code="EUR",
        country_code="NL",
    )
    relation_sell_to = RelationsSellTo(id=uuid.uuid4(), relation_code="REL123", status="ACTIVE")

    contact_relation = ContactRelationSellTo(
        id=uuid.uuid4(),
        contact_code=contact.code,
        relation_sell_to_code=relation_sell_to.relation_code,
    )

    relation_attribute = RelationAttributes(
        id=uuid.uuid4(),
        relation_code="REL123",
        attribute_code="DISCOUNT",
        attribute_value="15",
    )

    db_session.add_all(
        [
            contact,
            contact_attribute,
            relation,
            relation_sell_to,
            contact_relation,
            relation_attribute,
        ]
    )
    db_session.commit()

    return {
        "contact": contact,
        "contact_attribute": contact_attribute,
        "contact_relation": contact_relation,
        "relation_attribute": relation_attribute,
    }
