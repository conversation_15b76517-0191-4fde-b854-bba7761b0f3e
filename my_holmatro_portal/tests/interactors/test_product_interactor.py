import logging
from unittest.mock import Mock

import pytest
from src.interactors.product_interactor import ProductInteractor
from src.models.response.product_response import ProductSalesInfoResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.product import ProductAvailability
from src.models.schemas.product import ProductPrice
from src.repositories.product_repository import ProductRepository
from src.shared.exceptions import ProductNotFoundError

# Configure logger
logger = logging.getLogger("test_product_interactor")


@pytest.fixture
def mock_product_repository():
    """Creates a mock ProductRepository."""
    return Mock(spec=ProductRepository)


@pytest.fixture
def product_interactor(mock_product_repository):
    """Creates an instance of ProductInteractor with a mocked repository."""
    return ProductInteractor(product_repository=mock_product_repository)


def test_get_product_sales_info_success(product_interactor, mock_product_repository):
    """Tests successful retrieval of product sales information."""
    mock_results_data = [
        {
            "article_no": "12345",
            "gross_price": 100.0,
            "nett_price": 80.0,
            "discount_percentage": 20.0,
            "currency": "USD",
            "unit": "pcs",
            "stock": 50,
            "restock_days": 7,
            "created_ts_utc": "2023-01-01T00:00:00Z",
            "updated_ts_utc": "2023-01-01T00:00:00Z",
        },
        {
            "article_no": "67890",
            "gross_price": 200.0,
            "nett_price": 150.0,
            "discount_percentage": 25.0,
            "currency": "USD",
            "unit": "pcs",
            "stock": 30,
            "restock_days": 10,
            "created_ts_utc": "2023-01-01T00:00:00Z",
            "updated_ts_utc": "2023-01-01T00:00:00Z",
        },
    ]
    mock_rows = [Mock(_asdict=Mock(return_value=item)) for item in mock_results_data]
    mock_product_repository.get_product_sales_info = Mock(return_value=mock_rows)

    current_user = JWTClaims(emails=["<EMAIL>"], aud="your_audience", iss="your_issuer", sub="mock_sub")
    article_numbers = ["12345", "67890"]

    product_sales_info_response = product_interactor.get_product_sales_info(current_user, article_numbers)

    assert isinstance(product_sales_info_response, ProductSalesInfoResponse)
    assert len(product_sales_info_response.sales_info) == 2
    assert product_sales_info_response.sales_info[0].article_no == "12345"
    assert product_sales_info_response.sales_info[1].article_no == "67890"

    mock_product_repository.get_product_sales_info.assert_called_once_with("<EMAIL>", article_numbers)


def test_get_product_sales_info_no_email(product_interactor):
    """Tests scenario where the current user has no email-address."""
    current_user = JWTClaims(aud="your_audience", iss="your_issuer", sub="mock_sub", emails=[])

    article_numbers = ["12345", "67890"]

    with pytest.raises(ValueError) as exc_info:
        product_interactor.get_product_sales_info(current_user, article_numbers)
    assert str(exc_info.value) == "No email found in JWT claims"


def test_get_product_sales_info_not_found(product_interactor, mock_product_repository):
    """Tests scenario where no product sales information is found."""
    mock_product_repository.get_product_sales_info = Mock(side_effect=ProductNotFoundError("12345"))

    current_user = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )
    article_numbers = ["12345"]

    with pytest.raises(ProductNotFoundError) as exc_info:
        product_interactor.get_product_sales_info(current_user, article_numbers)

    assert "Product with article number '12345' not found" in str(exc_info.value)

    mock_product_repository.get_product_sales_info.assert_called_once_with("<EMAIL>", article_numbers)


def test_get_product_sales_price_success(product_interactor, mock_product_repository):
    """Tests successful retrieval of product sales price."""
    mock_price_data = {
        "article_no": "12345",
        "gross_price": 100.0,
        "nett_price": 80.0,
        "discount_percentage": 20.0,
        "currency": "USD",
        "unit": "pcs",
        "created_ts_utc": "2023-01-01T00:00:00Z",
        "updated_ts_utc": "2023-01-01T00:00:00Z",
    }
    mock_row = Mock()
    mock_row._asdict.return_value = mock_price_data
    mock_product_repository.get_product_price = Mock(return_value=mock_row)

    current_user = JWTClaims(aud="aud", iss="iss", sub="sub", emails=["<EMAIL>"])
    result = product_interactor.get_product_sales_price(current_user, "12345")

    assert isinstance(result, ProductPrice)
    assert result.article_no == "12345"
    assert result.nett_price == 80.0
    assert result.currency == "USD"

    mock_product_repository.get_product_price.assert_called_once_with("<EMAIL>", "12345")


def test_get_product_availability_success(product_interactor, mock_product_repository):
    """Tests successful retrieval of product availability."""
    mock_availability_data = {
        "article_no": "12345",
        "stock": 50,
        "restock_days": 7,
        "unit": "piece",
        "created_ts_utc": "2023-01-01T00:00:00Z",
        "updated_ts_utc": "2023-01-01T00:00:00Z",
    }
    mock_row = Mock()
    mock_row._asdict.return_value = mock_availability_data
    mock_product_repository.get_product_availability = Mock(return_value=mock_row)

    current_user = JWTClaims(aud="aud", iss="iss", sub="sub", emails=["<EMAIL>"])
    result = product_interactor.get_product_availability(current_user, "12345")

    assert isinstance(result, ProductAvailability)
    assert result.article_no == "12345"
    assert result.stock == 50
    assert result.restock_days == 7

    mock_product_repository.get_product_availability.assert_called_once_with("<EMAIL>", "12345")
