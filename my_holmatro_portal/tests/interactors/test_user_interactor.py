import logging
from types import SimpleNamespace
from unittest.mock import Mock

import pytest
from src.interactors.user_interactor import UserInteractor
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.user import Relation
from src.models.schemas.user import UserAggregate
from src.repositories.user_repository import UserRepository
from src.shared.exceptions import UserAccessDeniedError
from src.shared.exceptions import UserNotFoundError

# Configure logger
logger = logging.getLogger("test_user_interactor")


@pytest.fixture
def mock_user_repository():
    """Creates a mock UserRepository."""
    return Mock(spec=UserRepository)


@pytest.fixture
def user_interactor(mock_user_repository):
    """Creates an instance of UserInteractor with a mocked repository."""
    return UserInteractor(user_repository=mock_user_repository)


def test_get_user_by_email_success(user_interactor, mock_user_repository):
    """Tests successful retrieval of user by email."""
    mock_user_db = SimpleNamespace(
        email="<EMAIL>", created_at="2023-01-01T00:00:00Z", updated_at="2023-01-01T00:00:00Z"
    )
    mock_primary_contact = SimpleNamespace(code="123", name="Test User")
    mock_country = SimpleNamespace(code="US")
    mock_language = SimpleNamespace(code="EN")
    mock_relations = [
        Relation(
            company_id="company_1",
            relation_id="relation_1",
            relation_name="Relation 1",
            int_sales_rep_id="int_rep_1",
            int_sales_rep_name="Internal Rep 1",
            int_sales_rep_email="<EMAIL>",
            ext_sales_rep_id="ext_rep_1",
            ext_sales_rep_name="External Rep 1",
            ext_sales_rep_email="<EMAIL>",
        )
    ]
    mock_user_aggregate = UserAggregate(
        user_db=mock_user_db,
        primary_contact=mock_primary_contact,
        all_relations=mock_relations,
        primary_relation_name="Test Relation",
        primary_currency_id="USD",
        country=mock_country,
        language=mock_language,
        attributes=[],
        external_links=[],
    )

    mock_user_repository.get_by_email = Mock(return_value=mock_user_aggregate)

    current_user = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )
    user = user_interactor.get_user_by_email(current_user)

    assert user.user.email_address == "<EMAIL>"
    assert user.user.contact_id == "123"
    mock_user_repository.get_by_email.assert_called_once_with("<EMAIL>")


def test_get_user_by_email_not_found(user_interactor, mock_user_repository):
    """Tests scenario where user is not found."""
    mock_user_repository.get_by_email = Mock(side_effect=UserNotFoundError("<EMAIL>"))

    current_user = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )

    with pytest.raises(UserNotFoundError) as exc_info:
        user_interactor.get_user_by_email(current_user)

    assert str(exc_info.value) == "User with email '<EMAIL>' not found"
    mock_user_repository.get_by_email.assert_called_once_with("<EMAIL>")


def test_get_user_by_email_access_denied(user_interactor, mock_user_repository):
    """Tests scenario where user access is denied."""
    mock_user_repository.get_by_email = Mock(side_effect=UserAccessDeniedError("<EMAIL>"))

    current_user = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )

    with pytest.raises(UserAccessDeniedError) as exc_info:
        user_interactor.get_user_by_email(current_user)

    assert str(exc_info.value) == "User '<EMAIL>' does not have portal access"
    mock_user_repository.get_by_email.assert_called_once_with("<EMAIL>")


def test_get_user_by_email_unexpected_error(user_interactor, mock_user_repository):
    """Tests scenario where an unexpected error occurs."""
    mock_user_repository.get_by_email = Mock(side_effect=Exception("Unexpected error"))

    current_user = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )

    with pytest.raises(Exception) as exc_info:
        user_interactor.get_user_by_email(current_user)

    assert str(exc_info.value) == "Unexpected error"
    mock_user_repository.get_by_email.assert_called_once_with("<EMAIL>")
