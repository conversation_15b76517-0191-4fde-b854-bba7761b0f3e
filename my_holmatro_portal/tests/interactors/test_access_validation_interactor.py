from unittest.mock import MagicMock

import pytest
from src.interactors.access_validation_interactor import AccessValidationInteractor
from src.models.schemas.user import User
from src.shared.exceptions import UserNotFoundError


@pytest.fixture
def user_repository_mock():
    return MagicMock()


@pytest.fixture
def contact_repository_mock():
    return MagicMock()


@pytest.fixture
def interactor(
    user_repository_mock,
):
    return AccessValidationInteractor(user_repository_mock)


@pytest.fixture
def mock_user():
    """Create a mock user with all required fields."""
    return User(
        email_address="<EMAIL>",
        contact_id="123",
        contact_name="Test User",
        relation_name="Test Relation",
        country_id="US",
        language_id="EN",
        currency_id="USD",
        created_ts_utc="2023-01-01T00:00:00Z",
        updated_ts_utc="2023-01-01T00:00:00Z",
        relations=[],
        attributes=[],
        external_links=[],
    )


def test_validate_user_access_success(interactor, user_repository_mock, mock_user):
    user_email = "<EMAIL>"

    user_repository_mock.get_user.return_value = mock_user

    result = interactor.validate_user_access(user_email)

    assert result == mock_user
    user_repository_mock.get_user.assert_called_once_with(user_email)


def test_validate_user_access_user_not_found(interactor, user_repository_mock):
    user_email = "<EMAIL>"
    user_repository_mock.get_user.return_value = None

    with pytest.raises(UserNotFoundError):
        interactor.validate_user_access(user_email)

    user_repository_mock.get_user.assert_called_once_with(user_email)
