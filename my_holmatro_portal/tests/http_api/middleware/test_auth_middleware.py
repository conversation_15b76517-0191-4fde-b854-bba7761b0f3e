from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

import pytest
from fastapi import HTT<PERSON>Exception
from src.http_api.middleware.auth_middleware import fetch_jwks
from src.http_api.middleware.auth_middleware import fetch_openid_config
from src.http_api.middleware.auth_middleware import get_current_user
from src.shared.exceptions import HolmatroBadRequestError
from src.shared.exceptions import HolmatroTokenError


@pytest.mark.asyncio
async def test_fetch_openid_config_with_cache():
    """Test that fetch_openid_config uses cache when available."""
    mock_client = AsyncMock()
    mock_url = "https://test.com/.well-known/openid-configuration"

    mock_cache_value = {"jwks_uri": "https://test.com/keys"}
    with patch("src.http_api.middleware.auth_middleware.jwks_cache") as mock_cache:
        mock_cache.get = AsyncMock(return_value=mock_cache_value)

        result = await fetch_openid_config(mock_client, mock_url)

        mock_cache.get.assert_called_once_with(mock_url)
        mock_client.get.assert_not_called()
        assert result == mock_cache_value


@pytest.mark.asyncio
async def test_fetch_jwks_with_cache():
    """Test that fetch_jwks uses cache when available."""
    mock_client = AsyncMock()
    mock_url = "https://test.com/keys"

    mock_cache_value = {"keys": [{"kty": "RSA", "n": "test"}]}
    with patch("src.http_api.middleware.auth_middleware.jwks_cache") as mock_cache:
        mock_cache.get = AsyncMock(return_value=mock_cache_value)

        result = await fetch_jwks(mock_client, mock_url)

        mock_cache.get.assert_called_once_with(mock_url)
        mock_client.get.assert_not_called()
        assert result == mock_cache_value


@pytest.mark.asyncio
async def test_get_current_user_valid_request():
    """Test get_current_user returns claims from request.state."""
    mock_claims = MagicMock()
    mock_request = MagicMock()
    mock_request.state.jwt_claims = mock_claims

    result = await get_current_user(mock_request)
    assert result == mock_claims


@pytest.mark.asyncio
async def test_get_current_user_missing_claims():
    """Test get_current_user raises if claims missing."""
    mock_request = MagicMock()
    del mock_request.state.jwt_claims  # Ensure attribute is missing

    with pytest.raises(HolmatroBadRequestError) as exc_info:
        await get_current_user(mock_request)
    assert exc_info.value.message == "JWT claims not found"


@pytest.mark.asyncio
async def test_fetch_openid_config_without_cache():
    """Test that fetch_openid_config fetches from URL when cache is empty."""
    mock_client = AsyncMock()
    mock_url = "https://test.com/.well-known/openid-configuration"
    mock_response_data = {"jwks_uri": "https://test.com/keys"}

    mock_response = MagicMock()
    mock_response.json.return_value = mock_response_data
    mock_client.get.return_value = mock_response

    with patch("src.http_api.middleware.auth_middleware.jwks_cache") as mock_cache:
        mock_cache.get = AsyncMock(return_value=None)
        mock_cache.set = AsyncMock()

        result = await fetch_openid_config(mock_client, mock_url)

        mock_cache.get.assert_called_once_with(mock_url)
        mock_client.get.assert_called_once_with(mock_url)
        mock_cache.set.assert_called_once_with(mock_url, mock_response_data, ttl=3600)
        assert result == mock_response_data


@pytest.mark.asyncio
async def test_fetch_jwks_without_cache():
    """Test that fetch_jwks fetches from URL when cache is empty."""
    mock_client = AsyncMock()
    mock_url = "https://test.com/keys"
    mock_response_data = {"keys": [{"kty": "RSA", "n": "test"}]}

    mock_response = MagicMock()
    mock_response.json.return_value = mock_response_data
    mock_client.get.return_value = mock_response

    with patch("src.http_api.middleware.auth_middleware.jwks_cache") as mock_cache:
        mock_cache.get = AsyncMock(return_value=None)
        mock_cache.set = AsyncMock()

        result = await fetch_jwks(mock_client, mock_url)

        mock_cache.get.assert_called_once_with(mock_url)
        mock_client.get.assert_called_once_with(mock_url)
        mock_cache.set.assert_called_once_with(mock_url, mock_response_data, ttl=3600)
        assert result == mock_response_data
