import json
from unittest.mock import AsyncMock
from unittest.mock import <PERSON><PERSON><PERSON>
from unittest.mock import patch

import pytest
from fastapi import Request
from src.http_api.middleware.user_validation import UserValidationMiddleware
from src.shared.exceptions import UserAccessDeniedError
from src.shared.exceptions import UserNotFoundError
from starlette.responses import Response


@pytest.fixture
def mock_app():
    """Create a mock ASGI app."""
    return MagicMock()


@pytest.fixture
def middleware(mock_app):
    """Create UserValidationMiddleware instance."""
    return UserValidationMiddleware(mock_app, ["/health", "/docs", "/openapi.json", "/redoc"])


@pytest.fixture
def mock_request():
    """Create a mock request."""
    request = MagicMock(spec=Request)
    request.url.path = "/api/test"
    request.state = MagicMock()
    return request


@pytest.fixture
def mock_call_next():
    """Create a mock call_next function."""
    mock_response = Response()
    return AsyncMock(return_value=mock_response)


@pytest.mark.asyncio
async def test_dispatch_skips_validation_for_health_endpoint(middleware, mock_call_next):
    """Test that dispatch skips validation for /health endpoint."""
    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/health"

    result = await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_called_once_with(mock_request)
    assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_skips_validation_for_docs_endpoint(middleware, mock_call_next):
    """Test that dispatch skips validation for /docs endpoint."""
    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/docs"

    result = await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_called_once_with(mock_request)
    assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_skips_validation_for_openapi_endpoint(middleware, mock_call_next):
    """Test that dispatch skips validation for /openapi.json endpoint."""
    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/openapi.json"

    result = await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_called_once_with(mock_request)
    assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_skips_validation_for_redoc_endpoint(middleware, mock_call_next):
    """Test that dispatch skips validation for /redoc endpoint."""
    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/redoc"

    result = await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_called_once_with(mock_request)
    assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_proceeds_without_validation_when_no_user_email(mock_request, mock_call_next):
    """Test that dispatch raises UserNotFoundError when user_email is not in request state."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    # Remove user_email from request.state
    delattr(mock_request.state, "user_email")

    with pytest.raises(UserNotFoundError):
        await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_not_called()


@pytest.mark.asyncio
async def test_dispatch_skips_validation_for_custom_skip_paths():
    """Test that dispatch skips validation for custom skip paths."""
    mock_app = MagicMock()
    middleware = UserValidationMiddleware(
        mock_app,
        skip_paths=["/health", "/docs", "/openapi.json", "/redoc", "/custom"],
    )
    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/custom/endpoint"
    mock_call_next = AsyncMock()

    result = await middleware.dispatch(mock_request, mock_call_next)

    mock_call_next.assert_called_once_with(mock_request)
    assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_raises_user_not_found_when_no_user_email(mock_request, mock_call_next):
    """Test that dispatch raises UserNotFoundError when user_email is not in request state."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    # Remove user_email from request.state
    delattr(mock_request.state, "user_email")

    with pytest.raises(UserNotFoundError) as exc_info:
        await middleware.dispatch(mock_request, mock_call_next)

    assert "User email is required for validation" in str(exc_info.value)
    mock_call_next.assert_not_called()


@pytest.mark.asyncio
async def test_dispatch_successful_user_validation(mock_request, mock_call_next):
    """Test successful user validation flow."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    mock_request.state.user_email = "<EMAIL>"

    mock_user = MagicMock()
    mock_session = MagicMock()
    mock_user_repository = MagicMock()
    mock_interactor = MagicMock()
    mock_interactor.validate_user_access.return_value = mock_user

    with (
        patch(
            "src.http_api.middleware.user_validation.get_db_session",
            return_value=iter([mock_session]),
        ),
        patch(
            "src.http_api.middleware.user_validation.UserRepositoryImpl",
            return_value=mock_user_repository,
        ),
        patch(
            "src.http_api.middleware.user_validation.AccessValidationInteractor",
            return_value=mock_interactor,
        ),
    ):
        result = await middleware.dispatch(mock_request, mock_call_next)

        mock_interactor.validate_user_access.assert_called_once_with("<EMAIL>")
        assert mock_request.state.validated_user == mock_user
        mock_call_next.assert_called_once_with(mock_request)
        assert result == mock_call_next.return_value


@pytest.mark.asyncio
async def test_dispatch_handles_user_not_found_error(mock_request, mock_call_next):
    """Test that dispatch properly handles UserNotFoundError."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    mock_request.state.user_email = "<EMAIL>"

    mock_session = MagicMock()
    mock_user_repository = MagicMock()
    mock_interactor = MagicMock()
    mock_interactor.validate_user_access.side_effect = UserNotFoundError("User not found")

    with (
        patch(
            "src.http_api.middleware.user_validation.get_db_session",
            return_value=iter([mock_session]),
        ),
        patch(
            "src.http_api.middleware.user_validation.UserRepositoryImpl",
            return_value=mock_user_repository,
        ),
        patch(
            "src.http_api.middleware.user_validation.AccessValidationInteractor",
            return_value=mock_interactor,
        ),
    ):
        response = await middleware.dispatch(mock_request, mock_call_next)
        assert response.status_code == 404
        assert json.loads(response.body) == {"detail": "User with email 'User not found' not found"}

        mock_interactor.validate_user_access.assert_called_once_with("<EMAIL>")
        mock_call_next.assert_not_called()


@pytest.mark.asyncio
async def test_dispatch_handles_user_access_denied_error(mock_request, mock_call_next):
    """Test that dispatch properly handles UserAccessDeniedError."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    mock_request.state.user_email = "<EMAIL>"

    mock_session = MagicMock()
    mock_user_repository = MagicMock()
    mock_interactor = MagicMock()
    mock_interactor.validate_user_access.side_effect = UserAccessDeniedError("Access denied")

    with (
        patch(
            "src.http_api.middleware.user_validation.get_db_session",
            return_value=iter([mock_session]),
        ),
        patch(
            "src.http_api.middleware.user_validation.UserRepositoryImpl",
            return_value=mock_user_repository,
        ),
        patch(
            "src.http_api.middleware.user_validation.AccessValidationInteractor",
            return_value=mock_interactor,
        ),
    ):
        response = await middleware.dispatch(mock_request, mock_call_next)
        assert response.status_code == 403
        assert json.loads(response.body) == {"detail": "User 'Access denied' does not have portal access"}

        mock_interactor.validate_user_access.assert_called_once_with("<EMAIL>")
        mock_call_next.assert_not_called()


@pytest.mark.asyncio
async def test_dispatch_handles_unexpected_exception(mock_request, mock_call_next):
    """Test that dispatch properly handles unexpected exceptions."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    mock_request.state.user_email = "<EMAIL>"

    mock_session = MagicMock()
    mock_user_repository = MagicMock()
    mock_interactor = MagicMock()
    mock_interactor.validate_user_access.side_effect = Exception("Unexpected error")

    with (
        patch(
            "src.http_api.middleware.user_validation.get_db_session",
            return_value=iter([mock_session]),
        ),
        patch(
            "src.http_api.middleware.user_validation.UserRepositoryImpl",
            return_value=mock_user_repository,
        ),
        patch(
            "src.http_api.middleware.user_validation.AccessValidationInteractor",
            return_value=mock_interactor,
        ),
        patch("src.http_api.middleware.user_validation._logging") as mock_logging,
    ):
        with pytest.raises(Exception) as exc_info:
            await middleware.dispatch(mock_request, mock_call_next)

        assert str(exc_info.value) == "Unexpected error"
        mock_interactor.validate_user_access.assert_called_once_with("<EMAIL>")
        mock_logging.error.assert_called_once()
        mock_call_next.assert_not_called()


@pytest.mark.asyncio
async def test_dispatch_creates_correct_repository_instances(mock_request, mock_call_next):
    """Test that dispatch creates repository instances with correct session."""
    middleware = UserValidationMiddleware(MagicMock(), skip_paths=["/health", "/docs", "/openapi.json", "/redoc"])
    mock_request.state.user_email = "<EMAIL>"

    mock_user = MagicMock()
    mock_session = MagicMock()

    with (
        patch(
            "src.http_api.middleware.user_validation.get_db_session",
            return_value=iter([mock_session]),
        ) as mock_get_session,
        patch("src.http_api.middleware.user_validation.UserRepositoryImpl") as mock_user_repo_class,
        patch("src.http_api.middleware.user_validation.AccessValidationInteractor") as mock_interactor_class,
    ):
        mock_interactor = MagicMock()
        mock_interactor.validate_user_access.return_value = mock_user
        mock_interactor_class.return_value = mock_interactor

        await middleware.dispatch(mock_request, mock_call_next)

        mock_get_session.assert_called_once()
        mock_user_repo_class.assert_called_once_with(mock_session)
        mock_interactor_class.assert_called_once_with(mock_user_repo_class.return_value)


def test_should_skip_validation_with_custom_skip_paths():
    """Test should_skip_validation works with custom skip paths."""
    middleware = UserValidationMiddleware(
        MagicMock(),
        skip_paths=["/health", "/docs", "/openapi.json", "/redoc", "/custom"],
    )

    mock_request = MagicMock(spec=Request)
    mock_request.url.path = "/custom/endpoint"

    result = middleware.should_skip_validation(mock_request)
    assert result is True
