from unittest.mock import patch

import pytest
from fastapi import HTTPException
from src.http_api.dependencies.required_header import verify_subscription_key
from starlette.status import HTTP_401_UNAUTHORIZED


def test_verify_subscription_key_valid(settings):
    header = settings.my_hm_subscription_token
    try:
        with patch("src.http_api.dependencies.required_header.settings", new=settings):
            verify_subscription_key(header)
    except HTTPException:
        pytest.fail("verify_subscription_key raised HTTPException unexpectedly!")


def test_verify_subscription_key_invalid(settings):
    header = "invalid_token"
    with pytest.raises(HTTPException) as exc_info:
        verify_subscription_key(header)
    assert exc_info.value.status_code == HTTP_401_UNAUTHORIZED
    assert exc_info.value.headers["error-message"] == "Invalid subscription key"


def test_verify_subscription_key_missing(settings):
    header = None
    with pytest.raises(HTTPException) as exc_info:
        verify_subscription_key(header)
    assert exc_info.value.status_code == HTTP_401_UNAUTHORIZED
    assert exc_info.value.headers["error-message"] == "Invalid subscription key"
