import datetime
from unittest.mock import Mock

import pytest
from fastapi import Request
from jose import jwt
from src.http_api.dependencies.required_header import verify_subscription_key
from src.http_api.middleware import auth_middleware
from src.http_api.middleware import user_validation
from src.http_api.routers import general
from src.http_api.routers import products
from src.main import create_app
from src.models.response.product_response import ProductSalesInfoResponse
from src.models.response.user_response import UserResponse
from src.models.schemas.jwt_claims import JWTClaims
from src.models.schemas.product import ProductAvailability
from src.models.schemas.product import ProductPrice
from src.models.schemas.product import ProductSalesInfo
from src.models.schemas.user import Attribute
from src.models.schemas.user import ExternalLink
from src.models.schemas.user import Relation
from src.models.schemas.user import User
from src.shared.settings import Settings
from starlette.testclient import TestClient


@pytest.fixture
def settings():
    """Fixture to provide test settings."""
    return Settings(
        jwt_audience="your_audience",
        b2c_issuer="your_issuer",
        jwt_algorithm="HS256",
        my_hm_subscription_token="test_key",
        openid_b2c_config_url="https://mock-oidc/.well-known/openid-configuration",
    )


@pytest.fixture
def unauthenticated_client():
    """
    Provides a test client without any authentication mocking.
    This is useful for testing that middleware correctly blocks unauthenticated requests.
    """
    app = create_app()
    with TestClient(app) as client:
        yield client


@pytest.fixture
def authenticated_client(monkeypatch):
    """
    Provides a test client with mocked authentication and authorization.
    This fixture is now safe for parallel execution.
    """
    mock_claims = JWTClaims(
        aud="your_audience",
        iss="your_issuer",
        sub="mock_sub",
        emails=["<EMAIL>"],
    )

    async def mock_jwt_dispatch(self, request: Request, call_next):
        request.state.jwt_claims = mock_claims
        request.state.user_email = mock_claims.emails[0] if mock_claims.emails else None
        response = await call_next(request)
        return response

    async def mock_user_dispatch(self, request: Request, call_next):
        response = await call_next(request)
        return response

    monkeypatch.setattr(auth_middleware.JWTValidationMiddleware, "dispatch", mock_jwt_dispatch)
    monkeypatch.setattr(user_validation.UserValidationMiddleware, "dispatch", mock_user_dispatch)

    app = create_app()

    mock_user_interactor = create_mock_user_interactor()
    mock_product_interactor = create_mock_product_interactor()
    app.dependency_overrides[general.get_user_interactor] = lambda: mock_user_interactor
    app.dependency_overrides[products.get_product_interactor] = lambda: mock_product_interactor
    app.dependency_overrides[verify_subscription_key] = lambda: None

    with TestClient(app) as client:
        yield client


def generate_valid_jwt(settings: Settings):
    """Generates a valid JWT token for testing."""
    claims = {
        "aud": settings.jwt_audience,
        "iss": settings.b2c_issuer,
        "sub": "mock_sub",
        "emails": ["<EMAIL>"],
        "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=1),
        "iat": datetime.datetime.utcnow(),
    }
    return jwt.encode(claims, "", algorithm=settings.jwt_algorithm)


def create_mock_user_interactor():
    """Creates a mocked UserInteractor for authentication tests."""
    mock_user_interactor = Mock()
    mock_user_interactor.get_user_by_email = Mock(
        return_value=UserResponse(
            user=User(
                email_address="<EMAIL>",
                contact_id="123",
                contact_name="Test User",
                relation_name="Test Relation",
                country_id="US",
                language_id="EN",
                currency_id="USD",
                created_ts_utc=datetime.datetime(2023, 1, 1, 0, 0, 0),
                updated_ts_utc=datetime.datetime(2023, 1, 2, 0, 0, 0),
            ),
            relations=[
                Relation(
                    company_id="company_1",
                    relation_id="relation_1",
                    relation_name="Relation Name",
                    int_sales_rep_id="int_rep_1",
                    int_sales_rep_name="Internal Rep",
                    int_sales_rep_email="<EMAIL>",
                    ext_sales_rep_id="ext_rep_1",
                    ext_sales_rep_name="External Rep",
                    ext_sales_rep_email="<EMAIL>",
                )
            ],
            attributes=[Attribute(sf_category="Category", sf_sub_category="SubCategory")],
            external_links=[ExternalLink(link="example", url="https://example.com")],
        )
    )
    return mock_user_interactor


def create_mock_product_interactor():
    mock_interactor = Mock()
    mock_interactor.get_product_sales_info = Mock(
        return_value=ProductSalesInfoResponse(
            sales_info=[
                ProductSalesInfo(
                    article_no="1234",
                    gross_price=100.0,
                    nett_price=90.0,
                    discount_percentage=10.0,
                    currency="USD",
                    unit="piece",
                    stock=50,
                    restock_days=5,
                    created_ts_utc=datetime.datetime(2023, 1, 1, 0, 0, 0),
                    updated_ts_utc=datetime.datetime(2023, 1, 2, 0, 0, 0),
                ),
                ProductSalesInfo(
                    article_no="5678",
                    gross_price=200.0,
                    nett_price=180.0,
                    discount_percentage=10.0,
                    currency="USD",
                    unit="piece",
                    stock=30,
                    restock_days=7,
                    created_ts_utc=datetime.datetime(2023, 1, 1, 0, 0, 0),
                    updated_ts_utc=datetime.datetime(2023, 1, 2, 0, 0, 0),
                ),
            ]
        )
    )
    mock_interactor.get_product_sales_price = Mock(
        return_value=ProductPrice(
            article_no="1234",
            gross_price=100.0,
            nett_price=90.0,
            discount_percentage=10.0,
            currency="USD",
            unit="piece",
            created_ts_utc=datetime.datetime(2023, 1, 1, 0, 0, 0),
            updated_ts_utc=datetime.datetime(2023, 1, 2, 0, 0, 0),
        )
    )
    mock_interactor.get_product_availability = Mock(
        return_value=ProductAvailability(
            article_no="1234",
            stock=50,
            restock_days=5,
            unit="piece",
            created_ts_utc=datetime.datetime(2023, 1, 1, 0, 0, 0),
            updated_ts_utc=datetime.datetime(2023, 1, 2, 0, 0, 0),
        )
    )
    return mock_interactor
