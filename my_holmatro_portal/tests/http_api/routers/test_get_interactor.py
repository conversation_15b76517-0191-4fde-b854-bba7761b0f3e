from unittest.mock import MagicMock

import pytest
from src.http_api.routers.general import get_user_interactor
from src.http_api.routers.products import get_product_interactor
from src.interactors.product_interactor import ProductInteractor
from src.interactors.user_interactor import UserInteractor
from src.services.product_repository_impl import ProductRepositoryImpl
from src.services.user_repository_impl import UserRepositoryImpl


def test_get_user_interactor():
    # Arrange
    mock_session = MagicMock()

    # Act
    interactor = get_user_interactor(mock_session)

    # Assert
    assert isinstance(interactor, UserInteractor)
    assert isinstance(interactor.user_repository, UserRepositoryImpl)
    assert interactor.user_repository.session == mock_session


def test_get_product_interactor():
    # Arrange
    mock_session = MagicMock()

    # Act
    interactor = get_product_interactor(mock_session)

    # Assert
    assert isinstance(interactor, ProductInteractor)
    assert isinstance(interactor.product_repository, ProductRepositoryImpl)
    assert interactor.product_repository.session == mock_session
