import pytest
from authlib.jose import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient
from src.http_api.middleware.auth_middleware import validate_jwt_with_authlib
from tests.http_api.conftest import generate_valid_jwt


def test_unauthenticated_protected_endpoint(unauthenticated_client: TestClient):
    """Ensures unauthenticated users cannot access protected routes."""
    with pytest.raises(Exception) as exc_info:
        response = unauthenticated_client.get("/general/user")
        assert str(exc_info.value) == "Missing or invalid headers"

        assert response.status_code == 401


def test_authenticated_protected_endpoint(authenticated_client: TestClient):
    """Ensures authenticated users can access protected routes."""
    response = authenticated_client.get("/general/user")
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_jwt_validation_with_mocked_oidc(settings, httpx_mock):
    """Tests JWT validation using mocked OpenID Connect (OIDC)."""
    # Mock the OpenID Connect configuration response
    openid_config = {
        "jwks_uri": "https://mock-oidc/jwks",
        "issuer": settings.b2c_issuer,
    }
    httpx_mock.add_response(url=settings.openid_b2c_config_url, json=openid_config)

    # Mock the JWKS response
    jwk = JsonWebKey.import_key("", {"kty": "oct"})
    httpx_mock.add_response(url="https://mock-oidc/jwks", json={"keys": [jwk.as_dict()]})

    # Generate a valid JWT token
    valid_token = generate_valid_jwt(settings)

    # Use an actual AsyncClient, but httpx_mock will intercept its requests
    async with AsyncClient() as client:
        # Call the function under test
        claims = await validate_jwt_with_authlib(valid_token, settings, client)

    # Assertions
    assert claims.sub == "mock_sub"
    assert claims.emails[0] == "<EMAIL>"


def test_get_product_sales_inf(unauthenticated_client: TestClient):
    with pytest.raises(Exception) as exc_info:
        response = unauthenticated_client.get("/products/salesinfo")
        assert response.status_code == 400
        assert str(exc_info.value) == "Missing or invalid headers"


def test_get_product_sales_info(authenticated_client: TestClient):
    response = authenticated_client.get("/products/salesinfo")
    assert response.status_code == 422


def test_get_product_sales_info_with_articlenos(authenticated_client: TestClient):
    response = authenticated_client.get("/products/salesinfo", params=[("articleno", "1234"), ("articleno", "5678")])
    assert response.status_code == 200


def test_get_product_sales_info_with_articleno(authenticated_client: TestClient):
    response = authenticated_client.get("/products/salesinfo", params=[("articleno", "1234")])
    assert response.status_code == 200


def test_get_product_sales_price(unauthenticated_client: TestClient):
    with pytest.raises(Exception) as exc_info:
        response = unauthenticated_client.get("/products/1234/salesprices")
        assert response.status_code == 400
        assert str(exc_info.value) == "Missing or invalid headers"


def test_get_product_sales_price_with_articleno(authenticated_client: TestClient):
    response = authenticated_client.get("/products/1234/salesprices")
    assert response.status_code == 200


def test_get_product_availability(unauthenticated_client: TestClient):
    with pytest.raises(Exception) as exc_info:
        response = unauthenticated_client.get("/products/1234/availability")
        assert str(exc_info.value) == "Missing or invalid headers"
        assert response.status_code == 400


def test_get_product_availability_with_articleno(authenticated_client: TestClient):
    response = authenticated_client.get("/products/1234/availability")
    assert response.status_code == 200
