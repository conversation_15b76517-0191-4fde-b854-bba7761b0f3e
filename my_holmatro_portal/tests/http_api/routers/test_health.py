import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from src.http_api.routers.health import router

# Create a FastAPI app and include the router for testing
app = FastAPI()
app.include_router(router, prefix="/health")

client = TestClient(app)


def test_health_check():
    # Act
    response = client.get("/health")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
