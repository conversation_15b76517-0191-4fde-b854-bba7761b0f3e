from unittest import mock

import pytest
from sqlalchemy.orm import Session
from src.shared.db import create_tables
from src.shared.db import get_db_session

# src/shared/test_db.py


def test_get_db_session_yields_session(monkeypatch):
    # Arrange
    fake_session = mock.MagicMock(spec=Session)
    monkeypatch.setattr("src.shared.db.SessionLocal", lambda: fake_session)

    # Act
    gen = get_db_session()
    session = next(gen)

    # Assert
    assert session is fake_session

    # Cleanup
    with pytest.raises(StopIteration):
        next(gen)
    fake_session.close.assert_called_once()


def test_get_db_session_closes_on_exception(monkeypatch):
    # Arrange
    fake_session = mock.MagicMock(spec=Session)
    monkeypatch.setattr("src.shared.db.SessionLocal", lambda: fake_session)

    # Act
    gen = get_db_session()
    next(gen)
    # Simulate exception in context
    try:
        gen.throw(Exception("fail"))
    except Exception:
        pass

    # Assert
    fake_session.close.assert_called_once()


def test_create_tables_calls_metadata(monkeypatch):
    # Arrange
    fake_metadata = mock.MagicMock()
    fake_engine = object()
    monkeypatch.setattr("src.shared.db.Base.metadata", fake_metadata)
    monkeypatch.setattr("src.shared.db.engine", fake_engine)

    # Act
    create_tables()

    # Assert
    fake_metadata.create_all.assert_called_once_with(bind=fake_engine)
