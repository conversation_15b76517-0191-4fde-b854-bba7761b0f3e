services:
  woocommerce-openapi-generator:
    image: openapitools/openapi-generator-cli:v7.13.0
    volumes:
      - .:/local
    working_dir: /local
    command:
      [
        "generate",
        "-i", "/local/api-specs/woocommerce.json",
        "-g", "python",
        "-o", "/local/http_api_clients/woocommerce",
        "--additional-properties", "packageName=woocommerce"
      ]
  centix-v1-openapi-generator:
    image: openapitools/openapi-generator-cli:v7.13.0
    volumes:
      - .:/local
    working_dir: /local
    command:
      [
        "generate",
        "-i", "/local/api-specs/centix_api_client.json",
        "-g", "python",
        "-o", "/local/http_api_clients/centix_api_client",
        "--additional-properties", "packageName=centix_api_client",
        "--additional-properties", "library=urllib3",
        "--additional-properties", "dateFormat=%Y-%m-%d",
        "--additional-properties", "datetimeFormat=%Y-%m-%dT%H:%M:%S%z",
        "--additional-properties", "disallowAdditionalPropertiesIfNotPresent=false"
      ]
  centix-v2-openapi-generator:
    image: openapitools/openapi-generator-cli:v7.13.0
    volumes:
      - .:/local
    working_dir: /local
    command:
      [
        "generate",
        "-i", "/local/api-specs/centix_api_client_v2.json",
        "-g", "python",
        "-o", "/local/http_api_clients/centix_api_client_v2",
        "--additional-properties", "packageName=centix_api_client_v2",
        "--additional-properties", "library=urllib3",
        "--additional-properties", "dateFormat=%Y-%m-%d",
        "--additional-properties", "datetimeFormat=%Y-%m-%dT%H:%M:%S%z",
        "--additional-properties", "disallowAdditionalPropertiesIfNotPresent=false"
      ]