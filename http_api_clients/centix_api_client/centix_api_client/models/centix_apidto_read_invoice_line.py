# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadInvoiceLine(BaseModel):
    """
    CentixAPIDTOReadInvoiceLine
    """ # noqa: E501
    invoice_id: Optional[StrictInt] = Field(default=None, alias="InvoiceID")
    invoice_line_type: Optional[StrictInt] = Field(default=None, description="1 = Sales, 2 = ObjectRental, 4 = Comment", alias="InvoiceLineType")
    position_no: Optional[StrictInt] = Field(default=None, alias="PositionNo")
    product_id: Optional[StrictInt] = Field(default=None, alias="ProductID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    product_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductPrice")
    discount_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="DiscountPCT")
    price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Price")
    vat_id: Optional[StrictInt] = Field(default=None, alias="VatID")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    lock_price: Optional[StrictBool] = Field(default=None, alias="LockPrice")
    is_composed_product: Optional[StrictBool] = Field(default=None, alias="IsComposedProduct")
    invoice_composed_product: Optional[StrictBool] = Field(default=None, alias="InvoiceComposedProduct")
    invoice_parts: Optional[StrictBool] = Field(default=None, alias="InvoiceParts")
    composed_product_parent_id: Optional[StrictInt] = Field(default=None, alias="ComposedProductParentID")
    product_composition_quantity: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductCompositionQuantity")
    product_composition_round_off_method: Optional[StrictInt] = Field(default=None, alias="ProductCompositionRoundOffMethod")
    alternate_product_id: Optional[StrictInt] = Field(default=None, alias="AlternateProductID")
    cross_link_product_id: Optional[StrictInt] = Field(default=None, alias="CrossLinkProductID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    unit_quantity: Optional[StrictInt] = Field(default=None, alias="UnitQuantity")
    quantity: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Quantity")
    rented_from: Optional[datetime] = Field(default=None, alias="RentedFrom")
    rented_till: Optional[datetime] = Field(default=None, alias="RentedTill")
    sub_total: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotal")
    order_line_id: Optional[StrictInt] = Field(default=None, alias="OrderLineID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    pack_list_id: Optional[StrictInt] = Field(default=None, alias="PackListID")
    product_price_vat_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="ProductPriceVatPct")
    product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductCalcPrice")
    price_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceIncl")
    price_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceVatAmount")
    sub_total_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalIncl")
    sub_total_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalVatAmount")
    product_price_currency_id: Optional[StrictInt] = Field(default=None, alias="ProductPriceCurrencyID")
    product_price_exchange_rate: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="ProductPriceExchangeRate")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    sub_total_product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalProductCalcPrice")
    is_product_realization: Optional[StrictBool] = Field(default=None, alias="IsProductRealization")
    is_hour_registration: Optional[StrictBool] = Field(default=None, alias="IsHourRegistration")
    is_discount_product: Optional[StrictBool] = Field(default=None, alias="IsDiscountProduct")
    is_order_costs_product: Optional[StrictBool] = Field(default=None, alias="IsOrderCostsProduct")
    is_transport_costs_product: Optional[StrictBool] = Field(default=None, alias="IsTransportCostsProduct")
    free_gift_quantity: Optional[StrictInt] = Field(default=None, alias="FreeGiftQuantity")
    free_gift_type: Optional[StrictInt] = Field(default=None, alias="FreeGiftType")
    lock_product_price: Optional[StrictBool] = Field(default=None, alias="LockProductPrice")
    rental_object_id: Optional[StrictInt] = Field(default=None, alias="RentalObjectID")
    rental_rate_code_id: Optional[StrictInt] = Field(default=None, alias="RentalRateCodeID")
    rental_rate_day1: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay1")
    rental_rate_day2: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay2")
    rental_rate_day3: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay3")
    rental_rate_day4: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay4")
    rental_rate_day5: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay5")
    rental_rate_day6: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay6")
    rental_rate_week: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateWeek")
    rental_rate_month: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateMonth")
    rental_rate_quarter: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateQuarter")
    rental_rate_year: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateYear")
    rental_period_day1: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay1")
    rental_period_day2: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay2")
    rental_period_day3: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay3")
    rental_period_day4: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay4")
    rental_period_day5: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay5")
    rental_period_day6: Optional[StrictInt] = Field(default=None, alias="RentalPeriodDay6")
    rental_period_week: Optional[StrictInt] = Field(default=None, alias="RentalPeriodWeek")
    rental_period_month: Optional[StrictInt] = Field(default=None, alias="RentalPeriodMonth")
    rental_period_quarter: Optional[StrictInt] = Field(default=None, alias="RentalPeriodQuarter")
    rental_period_year: Optional[StrictInt] = Field(default=None, alias="RentalPeriodYear")
    rental_period_invoiced_till_date: Optional[datetime] = Field(default=None, alias="RentalPeriodInvoicedTillDate")
    rental_period_invoice_till_date: Optional[datetime] = Field(default=None, alias="RentalPeriodInvoiceTillDate")
    meter_reading_unit_rate: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingUnitRate")
    meter_reading_initial_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingInitialValue")
    meter_reading_final_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingFinalValue")
    meter_reading_extra_consumption: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingExtraConsumption")
    suppress_zero: Optional[StrictBool] = Field(default=None, alias="SuppressZero")
    deliver_contact_person_id: Optional[StrictInt] = Field(default=None, alias="DeliverContactPersonID")
    allow_repurchase: Optional[StrictBool] = Field(default=None, alias="AllowRepurchase")
    repurchase_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="RepurchasePCT")
    return_list_id: Optional[StrictInt] = Field(default=None, alias="ReturnListID")
    credit: Optional[StrictBool] = Field(default=None, alias="Credit")
    budget_code_id: Optional[StrictInt] = Field(default=None, alias="BudgetCodeID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["InvoiceID", "InvoiceLineType", "PositionNo", "ProductID", "Descr", "ProductPrice", "DiscountPCT", "Price", "VatID", "CurrencyID", "LockPrice", "IsComposedProduct", "InvoiceComposedProduct", "InvoiceParts", "ComposedProductParentID", "ProductCompositionQuantity", "ProductCompositionRoundOffMethod", "AlternateProductID", "CrossLinkProductID", "ProjectID", "CostCodeID", "UnitQuantity", "Quantity", "RentedFrom", "RentedTill", "SubTotal", "OrderLineID", "InternalNote", "ExternalNote", "Reference", "PackListID", "ProductPriceVatPct", "ProductCalcPrice", "PriceIncl", "PriceVatAmount", "SubTotalIncl", "SubTotalVatAmount", "ProductPriceCurrencyID", "ProductPriceExchangeRate", "CostCategoryID", "SubTotalProductCalcPrice", "IsProductRealization", "IsHourRegistration", "IsDiscountProduct", "IsOrderCostsProduct", "IsTransportCostsProduct", "FreeGiftQuantity", "FreeGiftType", "LockProductPrice", "RentalObjectID", "RentalRateCodeID", "RentalRateDay1", "RentalRateDay2", "RentalRateDay3", "RentalRateDay4", "RentalRateDay5", "RentalRateDay6", "RentalRateWeek", "RentalRateMonth", "RentalRateQuarter", "RentalRateYear", "RentalPeriodDay1", "RentalPeriodDay2", "RentalPeriodDay3", "RentalPeriodDay4", "RentalPeriodDay5", "RentalPeriodDay6", "RentalPeriodWeek", "RentalPeriodMonth", "RentalPeriodQuarter", "RentalPeriodYear", "RentalPeriodInvoicedTillDate", "RentalPeriodInvoiceTillDate", "MeterReadingUnitRate", "MeterReadingInitialValue", "MeterReadingFinalValue", "MeterReadingExtraConsumption", "SuppressZero", "DeliverContactPersonID", "AllowRepurchase", "RepurchasePCT", "ReturnListID", "Credit", "BudgetCodeID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('invoice_line_type')
    def invoice_line_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 4]):
            raise ValueError("must be one of enum values (1, 2, 4)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadInvoiceLine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if position_no (nullable) is None
        # and model_fields_set contains the field
        if self.position_no is None and "position_no" in self.model_fields_set:
            _dict['PositionNo'] = None

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if product_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_price is None and "product_price" in self.model_fields_set:
            _dict['ProductPrice'] = None

        # set to None if discount_pct (nullable) is None
        # and model_fields_set contains the field
        if self.discount_pct is None and "discount_pct" in self.model_fields_set:
            _dict['DiscountPCT'] = None

        # set to None if price (nullable) is None
        # and model_fields_set contains the field
        if self.price is None and "price" in self.model_fields_set:
            _dict['Price'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if is_composed_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_composed_product is None and "is_composed_product" in self.model_fields_set:
            _dict['IsComposedProduct'] = None

        # set to None if invoice_composed_product (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_composed_product is None and "invoice_composed_product" in self.model_fields_set:
            _dict['InvoiceComposedProduct'] = None

        # set to None if invoice_parts (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_parts is None and "invoice_parts" in self.model_fields_set:
            _dict['InvoiceParts'] = None

        # set to None if composed_product_parent_id (nullable) is None
        # and model_fields_set contains the field
        if self.composed_product_parent_id is None and "composed_product_parent_id" in self.model_fields_set:
            _dict['ComposedProductParentID'] = None

        # set to None if product_composition_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.product_composition_quantity is None and "product_composition_quantity" in self.model_fields_set:
            _dict['ProductCompositionQuantity'] = None

        # set to None if product_composition_round_off_method (nullable) is None
        # and model_fields_set contains the field
        if self.product_composition_round_off_method is None and "product_composition_round_off_method" in self.model_fields_set:
            _dict['ProductCompositionRoundOffMethod'] = None

        # set to None if alternate_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.alternate_product_id is None and "alternate_product_id" in self.model_fields_set:
            _dict['AlternateProductID'] = None

        # set to None if cross_link_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.cross_link_product_id is None and "cross_link_product_id" in self.model_fields_set:
            _dict['CrossLinkProductID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if unit_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.unit_quantity is None and "unit_quantity" in self.model_fields_set:
            _dict['UnitQuantity'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if rented_from (nullable) is None
        # and model_fields_set contains the field
        if self.rented_from is None and "rented_from" in self.model_fields_set:
            _dict['RentedFrom'] = None

        # set to None if rented_till (nullable) is None
        # and model_fields_set contains the field
        if self.rented_till is None and "rented_till" in self.model_fields_set:
            _dict['RentedTill'] = None

        # set to None if sub_total (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total is None and "sub_total" in self.model_fields_set:
            _dict['SubTotal'] = None

        # set to None if order_line_id (nullable) is None
        # and model_fields_set contains the field
        if self.order_line_id is None and "order_line_id" in self.model_fields_set:
            _dict['OrderLineID'] = None

        # set to None if pack_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.pack_list_id is None and "pack_list_id" in self.model_fields_set:
            _dict['PackListID'] = None

        # set to None if product_price_vat_pct (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_vat_pct is None and "product_price_vat_pct" in self.model_fields_set:
            _dict['ProductPriceVatPct'] = None

        # set to None if product_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_calc_price is None and "product_calc_price" in self.model_fields_set:
            _dict['ProductCalcPrice'] = None

        # set to None if price_incl (nullable) is None
        # and model_fields_set contains the field
        if self.price_incl is None and "price_incl" in self.model_fields_set:
            _dict['PriceIncl'] = None

        # set to None if price_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.price_vat_amount is None and "price_vat_amount" in self.model_fields_set:
            _dict['PriceVatAmount'] = None

        # set to None if sub_total_incl (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_incl is None and "sub_total_incl" in self.model_fields_set:
            _dict['SubTotalIncl'] = None

        # set to None if sub_total_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_vat_amount is None and "sub_total_vat_amount" in self.model_fields_set:
            _dict['SubTotalVatAmount'] = None

        # set to None if product_price_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_currency_id is None and "product_price_currency_id" in self.model_fields_set:
            _dict['ProductPriceCurrencyID'] = None

        # set to None if product_price_exchange_rate (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_exchange_rate is None and "product_price_exchange_rate" in self.model_fields_set:
            _dict['ProductPriceExchangeRate'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if sub_total_product_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_product_calc_price is None and "sub_total_product_calc_price" in self.model_fields_set:
            _dict['SubTotalProductCalcPrice'] = None

        # set to None if is_product_realization (nullable) is None
        # and model_fields_set contains the field
        if self.is_product_realization is None and "is_product_realization" in self.model_fields_set:
            _dict['IsProductRealization'] = None

        # set to None if is_hour_registration (nullable) is None
        # and model_fields_set contains the field
        if self.is_hour_registration is None and "is_hour_registration" in self.model_fields_set:
            _dict['IsHourRegistration'] = None

        # set to None if is_discount_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_discount_product is None and "is_discount_product" in self.model_fields_set:
            _dict['IsDiscountProduct'] = None

        # set to None if is_order_costs_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_order_costs_product is None and "is_order_costs_product" in self.model_fields_set:
            _dict['IsOrderCostsProduct'] = None

        # set to None if is_transport_costs_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_transport_costs_product is None and "is_transport_costs_product" in self.model_fields_set:
            _dict['IsTransportCostsProduct'] = None

        # set to None if free_gift_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.free_gift_quantity is None and "free_gift_quantity" in self.model_fields_set:
            _dict['FreeGiftQuantity'] = None

        # set to None if free_gift_type (nullable) is None
        # and model_fields_set contains the field
        if self.free_gift_type is None and "free_gift_type" in self.model_fields_set:
            _dict['FreeGiftType'] = None

        # set to None if lock_product_price (nullable) is None
        # and model_fields_set contains the field
        if self.lock_product_price is None and "lock_product_price" in self.model_fields_set:
            _dict['LockProductPrice'] = None

        # set to None if rental_object_id (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_id is None and "rental_object_id" in self.model_fields_set:
            _dict['RentalObjectID'] = None

        # set to None if rental_rate_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_code_id is None and "rental_rate_code_id" in self.model_fields_set:
            _dict['RentalRateCodeID'] = None

        # set to None if rental_rate_day1 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day1 is None and "rental_rate_day1" in self.model_fields_set:
            _dict['RentalRateDay1'] = None

        # set to None if rental_rate_day2 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day2 is None and "rental_rate_day2" in self.model_fields_set:
            _dict['RentalRateDay2'] = None

        # set to None if rental_rate_day3 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day3 is None and "rental_rate_day3" in self.model_fields_set:
            _dict['RentalRateDay3'] = None

        # set to None if rental_rate_day4 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day4 is None and "rental_rate_day4" in self.model_fields_set:
            _dict['RentalRateDay4'] = None

        # set to None if rental_rate_day5 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day5 is None and "rental_rate_day5" in self.model_fields_set:
            _dict['RentalRateDay5'] = None

        # set to None if rental_rate_day6 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_day6 is None and "rental_rate_day6" in self.model_fields_set:
            _dict['RentalRateDay6'] = None

        # set to None if rental_rate_week (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_week is None and "rental_rate_week" in self.model_fields_set:
            _dict['RentalRateWeek'] = None

        # set to None if rental_rate_month (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_month is None and "rental_rate_month" in self.model_fields_set:
            _dict['RentalRateMonth'] = None

        # set to None if rental_rate_quarter (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_quarter is None and "rental_rate_quarter" in self.model_fields_set:
            _dict['RentalRateQuarter'] = None

        # set to None if rental_rate_year (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_year is None and "rental_rate_year" in self.model_fields_set:
            _dict['RentalRateYear'] = None

        # set to None if rental_period_day1 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day1 is None and "rental_period_day1" in self.model_fields_set:
            _dict['RentalPeriodDay1'] = None

        # set to None if rental_period_day2 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day2 is None and "rental_period_day2" in self.model_fields_set:
            _dict['RentalPeriodDay2'] = None

        # set to None if rental_period_day3 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day3 is None and "rental_period_day3" in self.model_fields_set:
            _dict['RentalPeriodDay3'] = None

        # set to None if rental_period_day4 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day4 is None and "rental_period_day4" in self.model_fields_set:
            _dict['RentalPeriodDay4'] = None

        # set to None if rental_period_day5 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day5 is None and "rental_period_day5" in self.model_fields_set:
            _dict['RentalPeriodDay5'] = None

        # set to None if rental_period_day6 (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_day6 is None and "rental_period_day6" in self.model_fields_set:
            _dict['RentalPeriodDay6'] = None

        # set to None if rental_period_week (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_week is None and "rental_period_week" in self.model_fields_set:
            _dict['RentalPeriodWeek'] = None

        # set to None if rental_period_month (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_month is None and "rental_period_month" in self.model_fields_set:
            _dict['RentalPeriodMonth'] = None

        # set to None if rental_period_quarter (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_quarter is None and "rental_period_quarter" in self.model_fields_set:
            _dict['RentalPeriodQuarter'] = None

        # set to None if rental_period_year (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_year is None and "rental_period_year" in self.model_fields_set:
            _dict['RentalPeriodYear'] = None

        # set to None if rental_period_invoiced_till_date (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_invoiced_till_date is None and "rental_period_invoiced_till_date" in self.model_fields_set:
            _dict['RentalPeriodInvoicedTillDate'] = None

        # set to None if rental_period_invoice_till_date (nullable) is None
        # and model_fields_set contains the field
        if self.rental_period_invoice_till_date is None and "rental_period_invoice_till_date" in self.model_fields_set:
            _dict['RentalPeriodInvoiceTillDate'] = None

        # set to None if meter_reading_unit_rate (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_unit_rate is None and "meter_reading_unit_rate" in self.model_fields_set:
            _dict['MeterReadingUnitRate'] = None

        # set to None if meter_reading_initial_value (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_initial_value is None and "meter_reading_initial_value" in self.model_fields_set:
            _dict['MeterReadingInitialValue'] = None

        # set to None if meter_reading_final_value (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_final_value is None and "meter_reading_final_value" in self.model_fields_set:
            _dict['MeterReadingFinalValue'] = None

        # set to None if meter_reading_extra_consumption (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_extra_consumption is None and "meter_reading_extra_consumption" in self.model_fields_set:
            _dict['MeterReadingExtraConsumption'] = None

        # set to None if suppress_zero (nullable) is None
        # and model_fields_set contains the field
        if self.suppress_zero is None and "suppress_zero" in self.model_fields_set:
            _dict['SuppressZero'] = None

        # set to None if deliver_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_contact_person_id is None and "deliver_contact_person_id" in self.model_fields_set:
            _dict['DeliverContactPersonID'] = None

        # set to None if repurchase_pct (nullable) is None
        # and model_fields_set contains the field
        if self.repurchase_pct is None and "repurchase_pct" in self.model_fields_set:
            _dict['RepurchasePCT'] = None

        # set to None if return_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.return_list_id is None and "return_list_id" in self.model_fields_set:
            _dict['ReturnListID'] = None

        # set to None if credit (nullable) is None
        # and model_fields_set contains the field
        if self.credit is None and "credit" in self.model_fields_set:
            _dict['Credit'] = None

        # set to None if budget_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.budget_code_id is None and "budget_code_id" in self.model_fields_set:
            _dict['BudgetCodeID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadInvoiceLine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "InvoiceID": obj.get("InvoiceID"),
            "InvoiceLineType": obj.get("InvoiceLineType"),
            "PositionNo": obj.get("PositionNo"),
            "ProductID": obj.get("ProductID"),
            "Descr": obj.get("Descr"),
            "ProductPrice": obj.get("ProductPrice"),
            "DiscountPCT": obj.get("DiscountPCT"),
            "Price": obj.get("Price"),
            "VatID": obj.get("VatID"),
            "CurrencyID": obj.get("CurrencyID"),
            "LockPrice": obj.get("LockPrice"),
            "IsComposedProduct": obj.get("IsComposedProduct"),
            "InvoiceComposedProduct": obj.get("InvoiceComposedProduct"),
            "InvoiceParts": obj.get("InvoiceParts"),
            "ComposedProductParentID": obj.get("ComposedProductParentID"),
            "ProductCompositionQuantity": obj.get("ProductCompositionQuantity"),
            "ProductCompositionRoundOffMethod": obj.get("ProductCompositionRoundOffMethod"),
            "AlternateProductID": obj.get("AlternateProductID"),
            "CrossLinkProductID": obj.get("CrossLinkProductID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "UnitQuantity": obj.get("UnitQuantity"),
            "Quantity": obj.get("Quantity"),
            "RentedFrom": obj.get("RentedFrom"),
            "RentedTill": obj.get("RentedTill"),
            "SubTotal": obj.get("SubTotal"),
            "OrderLineID": obj.get("OrderLineID"),
            "InternalNote": obj.get("InternalNote"),
            "ExternalNote": obj.get("ExternalNote"),
            "Reference": obj.get("Reference"),
            "PackListID": obj.get("PackListID"),
            "ProductPriceVatPct": obj.get("ProductPriceVatPct"),
            "ProductCalcPrice": obj.get("ProductCalcPrice"),
            "PriceIncl": obj.get("PriceIncl"),
            "PriceVatAmount": obj.get("PriceVatAmount"),
            "SubTotalIncl": obj.get("SubTotalIncl"),
            "SubTotalVatAmount": obj.get("SubTotalVatAmount"),
            "ProductPriceCurrencyID": obj.get("ProductPriceCurrencyID"),
            "ProductPriceExchangeRate": obj.get("ProductPriceExchangeRate"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "SubTotalProductCalcPrice": obj.get("SubTotalProductCalcPrice"),
            "IsProductRealization": obj.get("IsProductRealization"),
            "IsHourRegistration": obj.get("IsHourRegistration"),
            "IsDiscountProduct": obj.get("IsDiscountProduct"),
            "IsOrderCostsProduct": obj.get("IsOrderCostsProduct"),
            "IsTransportCostsProduct": obj.get("IsTransportCostsProduct"),
            "FreeGiftQuantity": obj.get("FreeGiftQuantity"),
            "FreeGiftType": obj.get("FreeGiftType"),
            "LockProductPrice": obj.get("LockProductPrice"),
            "RentalObjectID": obj.get("RentalObjectID"),
            "RentalRateCodeID": obj.get("RentalRateCodeID"),
            "RentalRateDay1": obj.get("RentalRateDay1"),
            "RentalRateDay2": obj.get("RentalRateDay2"),
            "RentalRateDay3": obj.get("RentalRateDay3"),
            "RentalRateDay4": obj.get("RentalRateDay4"),
            "RentalRateDay5": obj.get("RentalRateDay5"),
            "RentalRateDay6": obj.get("RentalRateDay6"),
            "RentalRateWeek": obj.get("RentalRateWeek"),
            "RentalRateMonth": obj.get("RentalRateMonth"),
            "RentalRateQuarter": obj.get("RentalRateQuarter"),
            "RentalRateYear": obj.get("RentalRateYear"),
            "RentalPeriodDay1": obj.get("RentalPeriodDay1"),
            "RentalPeriodDay2": obj.get("RentalPeriodDay2"),
            "RentalPeriodDay3": obj.get("RentalPeriodDay3"),
            "RentalPeriodDay4": obj.get("RentalPeriodDay4"),
            "RentalPeriodDay5": obj.get("RentalPeriodDay5"),
            "RentalPeriodDay6": obj.get("RentalPeriodDay6"),
            "RentalPeriodWeek": obj.get("RentalPeriodWeek"),
            "RentalPeriodMonth": obj.get("RentalPeriodMonth"),
            "RentalPeriodQuarter": obj.get("RentalPeriodQuarter"),
            "RentalPeriodYear": obj.get("RentalPeriodYear"),
            "RentalPeriodInvoicedTillDate": obj.get("RentalPeriodInvoicedTillDate"),
            "RentalPeriodInvoiceTillDate": obj.get("RentalPeriodInvoiceTillDate"),
            "MeterReadingUnitRate": obj.get("MeterReadingUnitRate"),
            "MeterReadingInitialValue": obj.get("MeterReadingInitialValue"),
            "MeterReadingFinalValue": obj.get("MeterReadingFinalValue"),
            "MeterReadingExtraConsumption": obj.get("MeterReadingExtraConsumption"),
            "SuppressZero": obj.get("SuppressZero"),
            "DeliverContactPersonID": obj.get("DeliverContactPersonID"),
            "AllowRepurchase": obj.get("AllowRepurchase"),
            "RepurchasePCT": obj.get("RepurchasePCT"),
            "ReturnListID": obj.get("ReturnListID"),
            "Credit": obj.get("Credit"),
            "BudgetCodeID": obj.get("BudgetCodeID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


