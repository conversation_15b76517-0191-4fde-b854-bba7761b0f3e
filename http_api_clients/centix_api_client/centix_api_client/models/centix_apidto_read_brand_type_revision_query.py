# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadBrandTypeRevisionQuery(BaseModel):
    """
    CentixAPIDTOReadBrandTypeRevisionQuery
    """ # noqa: E501
    brand_id: Optional[StrictInt] = Field(default=None, alias="BrandID")
    brand_descr: Optional[StrictStr] = Field(default=None, alias="Brand_Descr")
    brand_archive: Optional[StrictBool] = Field(default=None, alias="Brand_Archive")
    brand_created_by: Optional[StrictStr] = Field(default=None, alias="Brand_CreatedBy")
    brand_modified_by: Optional[StrictStr] = Field(default=None, alias="Brand_ModifiedBy")
    brand_create_date: Optional[datetime] = Field(default=None, alias="Brand_CreateDate")
    brand_time_stamp: Optional[datetime] = Field(default=None, alias="Brand_TimeStamp")
    type_id: Optional[StrictInt] = Field(default=None, alias="TypeID")
    type_descr: Optional[StrictStr] = Field(default=None, alias="Type_Descr")
    type_archive: Optional[StrictBool] = Field(default=None, alias="Type_Archive")
    type_default_object_type_id: Optional[StrictInt] = Field(default=None, alias="Type_DefaultObjectTypeID")
    type_default_product_id: Optional[StrictInt] = Field(default=None, alias="Type_DefaultProductID")
    type_created_by: Optional[StrictStr] = Field(default=None, alias="Type_CreatedBy")
    type_modified_by: Optional[StrictStr] = Field(default=None, alias="Type_ModifiedBy")
    type_create_date: Optional[datetime] = Field(default=None, alias="Type_CreateDate")
    type_time_stamp: Optional[datetime] = Field(default=None, alias="Type_TimeStamp")
    revision_id: Optional[StrictInt] = Field(default=None, alias="RevisionID")
    revision_descr: Optional[StrictStr] = Field(default=None, alias="Revision_Descr")
    revision_archive: Optional[StrictBool] = Field(default=None, alias="Revision_Archive")
    revision_created_by: Optional[StrictStr] = Field(default=None, alias="Revision_CreatedBy")
    revision_modified_by: Optional[StrictStr] = Field(default=None, alias="Revision_ModifiedBy")
    revision_create_date: Optional[datetime] = Field(default=None, alias="Revision_CreateDate")
    revision_time_stamp: Optional[datetime] = Field(default=None, alias="Revision_TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["BrandID", "Brand_Descr", "Brand_Archive", "Brand_CreatedBy", "Brand_ModifiedBy", "Brand_CreateDate", "Brand_TimeStamp", "TypeID", "Type_Descr", "Type_Archive", "Type_DefaultObjectTypeID", "Type_DefaultProductID", "Type_CreatedBy", "Type_ModifiedBy", "Type_CreateDate", "Type_TimeStamp", "RevisionID", "Revision_Descr", "Revision_Archive", "Revision_CreatedBy", "Revision_ModifiedBy", "Revision_CreateDate", "Revision_TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadBrandTypeRevisionQuery from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if brand_create_date (nullable) is None
        # and model_fields_set contains the field
        if self.brand_create_date is None and "brand_create_date" in self.model_fields_set:
            _dict['Brand_CreateDate'] = None

        # set to None if brand_time_stamp (nullable) is None
        # and model_fields_set contains the field
        if self.brand_time_stamp is None and "brand_time_stamp" in self.model_fields_set:
            _dict['Brand_TimeStamp'] = None

        # set to None if type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_id is None and "type_id" in self.model_fields_set:
            _dict['TypeID'] = None

        # set to None if type_default_object_type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_default_object_type_id is None and "type_default_object_type_id" in self.model_fields_set:
            _dict['Type_DefaultObjectTypeID'] = None

        # set to None if type_default_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_default_product_id is None and "type_default_product_id" in self.model_fields_set:
            _dict['Type_DefaultProductID'] = None

        # set to None if type_create_date (nullable) is None
        # and model_fields_set contains the field
        if self.type_create_date is None and "type_create_date" in self.model_fields_set:
            _dict['Type_CreateDate'] = None

        # set to None if type_time_stamp (nullable) is None
        # and model_fields_set contains the field
        if self.type_time_stamp is None and "type_time_stamp" in self.model_fields_set:
            _dict['Type_TimeStamp'] = None

        # set to None if revision_id (nullable) is None
        # and model_fields_set contains the field
        if self.revision_id is None and "revision_id" in self.model_fields_set:
            _dict['RevisionID'] = None

        # set to None if revision_create_date (nullable) is None
        # and model_fields_set contains the field
        if self.revision_create_date is None and "revision_create_date" in self.model_fields_set:
            _dict['Revision_CreateDate'] = None

        # set to None if revision_time_stamp (nullable) is None
        # and model_fields_set contains the field
        if self.revision_time_stamp is None and "revision_time_stamp" in self.model_fields_set:
            _dict['Revision_TimeStamp'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadBrandTypeRevisionQuery from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "BrandID": obj.get("BrandID"),
            "Brand_Descr": obj.get("Brand_Descr"),
            "Brand_Archive": obj.get("Brand_Archive"),
            "Brand_CreatedBy": obj.get("Brand_CreatedBy"),
            "Brand_ModifiedBy": obj.get("Brand_ModifiedBy"),
            "Brand_CreateDate": obj.get("Brand_CreateDate"),
            "Brand_TimeStamp": obj.get("Brand_TimeStamp"),
            "TypeID": obj.get("TypeID"),
            "Type_Descr": obj.get("Type_Descr"),
            "Type_Archive": obj.get("Type_Archive"),
            "Type_DefaultObjectTypeID": obj.get("Type_DefaultObjectTypeID"),
            "Type_DefaultProductID": obj.get("Type_DefaultProductID"),
            "Type_CreatedBy": obj.get("Type_CreatedBy"),
            "Type_ModifiedBy": obj.get("Type_ModifiedBy"),
            "Type_CreateDate": obj.get("Type_CreateDate"),
            "Type_TimeStamp": obj.get("Type_TimeStamp"),
            "RevisionID": obj.get("RevisionID"),
            "Revision_Descr": obj.get("Revision_Descr"),
            "Revision_Archive": obj.get("Revision_Archive"),
            "Revision_CreatedBy": obj.get("Revision_CreatedBy"),
            "Revision_ModifiedBy": obj.get("Revision_ModifiedBy"),
            "Revision_CreateDate": obj.get("Revision_CreateDate"),
            "Revision_TimeStamp": obj.get("Revision_TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


