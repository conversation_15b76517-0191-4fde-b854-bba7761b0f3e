# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadFieldDictionary(BaseModel):
    """
    CentixAPIDTOReadFieldDictionary
    """ # noqa: E501
    field_name: Optional[StrictStr] = Field(default=None, alias="FieldName")
    column_title: Optional[StrictStr] = Field(default=None, alias="ColumnTitle")
    property_group_id: Optional[StrictInt] = Field(default=None, alias="PropertyGroupID")
    property_group_descr: Optional[StrictStr] = Field(default=None, alias="PropertyGroup_Descr")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    enabled: Optional[StrictBool] = Field(default=None, alias="Enabled")
    field_type: Optional[StrictInt] = Field(default=None, description="0 = BigInt, 1 = Binary, 2 = Bit, 3 = Char, 4 = DateTime, 5 = Decimal, 6 = Float, 7 = Image, 8 = Int, 9 = Money, 10 = NChar, 11 = NText, 12 = NVarChar, 13 = Real, 14 = UniqueIdentifier, 15 = SmallDateTime, 16 = SmallInt, 17 = SmallMoney, 18 = Text, 19 = Timestamp, 20 = TinyInt, 21 = VarBinary, 22 = VarChar, 23 = Variant, 25 = Xml, 29 = Udt, 30 = Structured, 31 = Date, 32 = Time, 33 = DateTime2, 34 = DateTimeOffset", alias="FieldType")
    field_length: Optional[StrictInt] = Field(default=None, alias="FieldLength")
    field_integer: Optional[StrictInt] = Field(default=None, alias="FieldInteger")
    field_decimal: Optional[StrictInt] = Field(default=None, alias="FieldDecimal")
    min_value: Optional[StrictStr] = Field(default=None, alias="MinValue")
    max_value: Optional[StrictStr] = Field(default=None, alias="MaxValue")
    list_id: Optional[StrictInt] = Field(default=None, alias="ListID")
    list_table_dictionary: Optional[StrictStr] = Field(default=None, alias="ListTableDictionary")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["FieldName", "ColumnTitle", "PropertyGroupID", "PropertyGroup_Descr", "Descr", "Enabled", "FieldType", "FieldLength", "FieldInteger", "FieldDecimal", "MinValue", "MaxValue", "ListID", "ListTableDictionary", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('field_type')
    def field_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 29, 30, 31, 32, 33, 34]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 29, 30, 31, 32, 33, 34)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadFieldDictionary from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if property_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.property_group_id is None and "property_group_id" in self.model_fields_set:
            _dict['PropertyGroupID'] = None

        # set to None if field_length (nullable) is None
        # and model_fields_set contains the field
        if self.field_length is None and "field_length" in self.model_fields_set:
            _dict['FieldLength'] = None

        # set to None if field_integer (nullable) is None
        # and model_fields_set contains the field
        if self.field_integer is None and "field_integer" in self.model_fields_set:
            _dict['FieldInteger'] = None

        # set to None if field_decimal (nullable) is None
        # and model_fields_set contains the field
        if self.field_decimal is None and "field_decimal" in self.model_fields_set:
            _dict['FieldDecimal'] = None

        # set to None if list_id (nullable) is None
        # and model_fields_set contains the field
        if self.list_id is None and "list_id" in self.model_fields_set:
            _dict['ListID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadFieldDictionary from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "FieldName": obj.get("FieldName"),
            "ColumnTitle": obj.get("ColumnTitle"),
            "PropertyGroupID": obj.get("PropertyGroupID"),
            "PropertyGroup_Descr": obj.get("PropertyGroup_Descr"),
            "Descr": obj.get("Descr"),
            "Enabled": obj.get("Enabled"),
            "FieldType": obj.get("FieldType"),
            "FieldLength": obj.get("FieldLength"),
            "FieldInteger": obj.get("FieldInteger"),
            "FieldDecimal": obj.get("FieldDecimal"),
            "MinValue": obj.get("MinValue"),
            "MaxValue": obj.get("MaxValue"),
            "ListID": obj.get("ListID"),
            "ListTableDictionary": obj.get("ListTableDictionary"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


