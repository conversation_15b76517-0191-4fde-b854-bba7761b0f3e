# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateSalesProduct(BaseModel):
    """
    CentixAPIDTOUpdateSalesProduct
    """ # noqa: E501
    allow_repurchase: Optional[StrictBool] = Field(default=None, alias="AllowRepurchase")
    is_administrative_product: Optional[StrictBool] = Field(default=None, alias="IsAdministrativeProduct")
    price: Optional[Union[StrictFloat, StrictInt]] = Field(alias="Price")
    recommended_retail_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RecommendedRetailPrice")
    repurchase_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="RepurchasePCT")
    stock_product: Optional[StrictBool] = Field(default=None, alias="StockProduct")
    entry_inspection: Optional[StrictInt] = Field(default=None, description="If a value other than 'NULL', is entered, the system default will be overridden by the entered value.0 = Never, 1 = Optional, 2 = Required", alias="EntryInspection")
    entry_inspection_mi_plan_id: Optional[StrictInt] = Field(default=None, description="If a value other than 'NULL', is entered, the system default will be overridden by the entered value.", alias="EntryInspectionMIPlanID")
    id: Annotated[str, Field(min_length=0, strict=True, max_length=30)] = Field(alias="ID")
    descr: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Descr")
    descr2: Optional[StrictStr] = Field(default=None, alias="Descr2")
    aidc: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="AIDC")
    product_category_id: Optional[StrictInt] = Field(alias="ProductCategoryID")
    product_group_id: Optional[StrictInt] = Field(alias="ProductGroupID")
    product_line_id: Optional[StrictInt] = Field(default=None, alias="ProductLineID")
    available_from: Optional[datetime] = Field(default=None, alias="AvailableFrom")
    blocked_for_delivery: Optional[StrictBool] = Field(default=None, alias="BlockedForDelivery")
    blocked_for_order: Optional[StrictBool] = Field(default=None, alias="BlockedForOrder")
    brand_id: Optional[StrictInt] = Field(default=None, alias="BrandID")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    currency_id: Optional[StrictInt] = Field(alias="CurrencyID")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    height: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Height")
    hs_code_id: Optional[StrictInt] = Field(default=None, alias="HSCodeID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    invoice_order_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceOrderCosts")
    invoice_transport_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceTransportCosts")
    is_composed_product: Optional[StrictBool] = Field(default=None, alias="IsComposedProduct")
    length: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Length")
    object_type_id: Optional[StrictInt] = Field(default=None, alias="ObjectTypeID")
    price_on_request: Optional[StrictBool] = Field(default=None, alias="PriceOnRequest")
    purchase_currency_id: Optional[StrictInt] = Field(alias="PurchaseCurrencyID")
    purchase_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PurchasePrice")
    purchase_vat_id: Optional[StrictInt] = Field(alias="PurchaseVatID")
    search_tag: Optional[StrictStr] = Field(default=None, alias="SearchTag")
    status_id: StrictInt = Field(alias="StatusID")
    stock_indication_id: Optional[StrictInt] = Field(default=None, alias="StockIndicationID")
    stock_type: Optional[StrictInt] = Field(description="0 = Bulk, 1 = Unique", alias="StockType")
    type_id: Optional[StrictInt] = Field(default=None, alias="TypeID")
    unit_id: Optional[StrictInt] = Field(default=None, alias="UnitID")
    vat_id: Optional[StrictInt] = Field(alias="VatID")
    weight: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Weight")
    width: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Width")
    html_content: Optional[StrictStr] = Field(default=None, alias="HTMLContent")
    html_content2: Optional[StrictStr] = Field(default=None, alias="HTMLContent2")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AllowRepurchase", "IsAdministrativeProduct", "Price", "RecommendedRetailPrice", "RepurchasePCT", "StockProduct", "EntryInspection", "EntryInspectionMIPlanID", "ID", "Descr", "Descr2", "AIDC", "ProductCategoryID", "ProductGroupID", "ProductLineID", "AvailableFrom", "BlockedForDelivery", "BlockedForOrder", "BrandID", "CostCategoryID", "CostCodeID", "CurrencyID", "ExternalNote", "Height", "HSCodeID", "InternalNote", "InvoiceOrderCosts", "InvoiceTransportCosts", "IsComposedProduct", "Length", "ObjectTypeID", "PriceOnRequest", "PurchaseCurrencyID", "PurchasePrice", "PurchaseVatID", "SearchTag", "StatusID", "StockIndicationID", "StockType", "TypeID", "UnitID", "VatID", "Weight", "Width", "HTMLContent", "HTMLContent2"]

    @field_validator('entry_inspection')
    def entry_inspection_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2]):
            raise ValueError("must be one of enum values (0, 1, 2)")
        return value

    @field_validator('stock_type')
    def stock_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateSalesProduct from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if allow_repurchase (nullable) is None
        # and model_fields_set contains the field
        if self.allow_repurchase is None and "allow_repurchase" in self.model_fields_set:
            _dict['AllowRepurchase'] = None

        # set to None if is_administrative_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_administrative_product is None and "is_administrative_product" in self.model_fields_set:
            _dict['IsAdministrativeProduct'] = None

        # set to None if price (nullable) is None
        # and model_fields_set contains the field
        if self.price is None and "price" in self.model_fields_set:
            _dict['Price'] = None

        # set to None if recommended_retail_price (nullable) is None
        # and model_fields_set contains the field
        if self.recommended_retail_price is None and "recommended_retail_price" in self.model_fields_set:
            _dict['RecommendedRetailPrice'] = None

        # set to None if repurchase_pct (nullable) is None
        # and model_fields_set contains the field
        if self.repurchase_pct is None and "repurchase_pct" in self.model_fields_set:
            _dict['RepurchasePCT'] = None

        # set to None if entry_inspection (nullable) is None
        # and model_fields_set contains the field
        if self.entry_inspection is None and "entry_inspection" in self.model_fields_set:
            _dict['EntryInspection'] = None

        # set to None if entry_inspection_mi_plan_id (nullable) is None
        # and model_fields_set contains the field
        if self.entry_inspection_mi_plan_id is None and "entry_inspection_mi_plan_id" in self.model_fields_set:
            _dict['EntryInspectionMIPlanID'] = None

        # set to None if product_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_category_id is None and "product_category_id" in self.model_fields_set:
            _dict['ProductCategoryID'] = None

        # set to None if product_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_group_id is None and "product_group_id" in self.model_fields_set:
            _dict['ProductGroupID'] = None

        # set to None if product_line_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_line_id is None and "product_line_id" in self.model_fields_set:
            _dict['ProductLineID'] = None

        # set to None if available_from (nullable) is None
        # and model_fields_set contains the field
        if self.available_from is None and "available_from" in self.model_fields_set:
            _dict['AvailableFrom'] = None

        # set to None if brand_id (nullable) is None
        # and model_fields_set contains the field
        if self.brand_id is None and "brand_id" in self.model_fields_set:
            _dict['BrandID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if hs_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.hs_code_id is None and "hs_code_id" in self.model_fields_set:
            _dict['HSCodeID'] = None

        # set to None if object_type_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_type_id is None and "object_type_id" in self.model_fields_set:
            _dict['ObjectTypeID'] = None

        # set to None if purchase_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_currency_id is None and "purchase_currency_id" in self.model_fields_set:
            _dict['PurchaseCurrencyID'] = None

        # set to None if purchase_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_vat_id is None and "purchase_vat_id" in self.model_fields_set:
            _dict['PurchaseVatID'] = None

        # set to None if stock_indication_id (nullable) is None
        # and model_fields_set contains the field
        if self.stock_indication_id is None and "stock_indication_id" in self.model_fields_set:
            _dict['StockIndicationID'] = None

        # set to None if stock_type (nullable) is None
        # and model_fields_set contains the field
        if self.stock_type is None and "stock_type" in self.model_fields_set:
            _dict['StockType'] = None

        # set to None if type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_id is None and "type_id" in self.model_fields_set:
            _dict['TypeID'] = None

        # set to None if unit_id (nullable) is None
        # and model_fields_set contains the field
        if self.unit_id is None and "unit_id" in self.model_fields_set:
            _dict['UnitID'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateSalesProduct from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AllowRepurchase": obj.get("AllowRepurchase"),
            "IsAdministrativeProduct": obj.get("IsAdministrativeProduct"),
            "Price": obj.get("Price"),
            "RecommendedRetailPrice": obj.get("RecommendedRetailPrice"),
            "RepurchasePCT": obj.get("RepurchasePCT"),
            "StockProduct": obj.get("StockProduct"),
            "EntryInspection": obj.get("EntryInspection"),
            "EntryInspectionMIPlanID": obj.get("EntryInspectionMIPlanID"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "Descr2": obj.get("Descr2"),
            "AIDC": obj.get("AIDC"),
            "ProductCategoryID": obj.get("ProductCategoryID"),
            "ProductGroupID": obj.get("ProductGroupID"),
            "ProductLineID": obj.get("ProductLineID"),
            "AvailableFrom": obj.get("AvailableFrom"),
            "BlockedForDelivery": obj.get("BlockedForDelivery"),
            "BlockedForOrder": obj.get("BlockedForOrder"),
            "BrandID": obj.get("BrandID"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "CurrencyID": obj.get("CurrencyID"),
            "ExternalNote": obj.get("ExternalNote"),
            "Height": obj.get("Height"),
            "HSCodeID": obj.get("HSCodeID"),
            "InternalNote": obj.get("InternalNote"),
            "InvoiceOrderCosts": obj.get("InvoiceOrderCosts"),
            "InvoiceTransportCosts": obj.get("InvoiceTransportCosts"),
            "IsComposedProduct": obj.get("IsComposedProduct"),
            "Length": obj.get("Length"),
            "ObjectTypeID": obj.get("ObjectTypeID"),
            "PriceOnRequest": obj.get("PriceOnRequest"),
            "PurchaseCurrencyID": obj.get("PurchaseCurrencyID"),
            "PurchasePrice": obj.get("PurchasePrice"),
            "PurchaseVatID": obj.get("PurchaseVatID"),
            "SearchTag": obj.get("SearchTag"),
            "StatusID": obj.get("StatusID"),
            "StockIndicationID": obj.get("StockIndicationID"),
            "StockType": obj.get("StockType"),
            "TypeID": obj.get("TypeID"),
            "UnitID": obj.get("UnitID"),
            "VatID": obj.get("VatID"),
            "Weight": obj.get("Weight"),
            "Width": obj.get("Width"),
            "HTMLContent": obj.get("HTMLContent"),
            "HTMLContent2": obj.get("HTMLContent2")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


