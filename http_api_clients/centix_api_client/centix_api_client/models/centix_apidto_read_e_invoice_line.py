# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadEInvoiceLine(BaseModel):
    """
    CentixAPIDTOReadEInvoiceLine
    """ # noqa: E501
    e_invoice_id: Optional[StrictInt] = Field(default=None, alias="EInvoiceID")
    invoice_line_type: Optional[StrictInt] = Field(default=None, description="0 = Default, 1 = FixedPrice, 2 = Discount, 3 = Comment, 4 = Rental, 5 = HourRegistration, 6 = ArticleRealization", alias="InvoiceLineType")
    item_code: Optional[StrictStr] = Field(default=None, alias="ItemCode")
    description: Optional[StrictStr] = Field(default=None, alias="Description")
    quantity: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Quantity")
    unit_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="UnitPrice")
    discount_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="DiscountPct")
    project_code: Optional[StrictStr] = Field(default=None, alias="ProjectCode")
    currency_code: Optional[StrictStr] = Field(default=None, alias="CurrencyCode")
    warehouse_code: Optional[StrictStr] = Field(default=None, alias="WarehouseCode")
    resource_code: Optional[StrictStr] = Field(default=None, alias="ResourceCode")
    pricelist_code: Optional[StrictStr] = Field(default=None, alias="PricelistCode")
    contract_id: Optional[StrictStr] = Field(default=None, alias="Contract_ID")
    order_id: Optional[StrictStr] = Field(default=None, alias="Order_ID")
    working_ticket_id: Optional[StrictStr] = Field(default=None, alias="WorkingTicket_ID")
    work_flow_id: Optional[StrictStr] = Field(default=None, alias="WorkFlow_ID")
    invoice_id: Optional[StrictStr] = Field(default=None, alias="Invoice_ID")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    cost_code: Optional[StrictStr] = Field(default=None, alias="CostCode")
    cost_category: Optional[StrictStr] = Field(default=None, alias="CostCategory")
    budget_code: Optional[StrictStr] = Field(default=None, alias="BudgetCode")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["EInvoiceID", "InvoiceLineType", "ItemCode", "Description", "Quantity", "UnitPrice", "DiscountPct", "ProjectCode", "CurrencyCode", "WarehouseCode", "ResourceCode", "PricelistCode", "Contract_ID", "Order_ID", "WorkingTicket_ID", "WorkFlow_ID", "Invoice_ID", "Reference", "CostCode", "CostCategory", "BudgetCode", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('invoice_line_type')
    def invoice_line_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 5, 6]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 5, 6)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadEInvoiceLine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if e_invoice_id (nullable) is None
        # and model_fields_set contains the field
        if self.e_invoice_id is None and "e_invoice_id" in self.model_fields_set:
            _dict['EInvoiceID'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if unit_price (nullable) is None
        # and model_fields_set contains the field
        if self.unit_price is None and "unit_price" in self.model_fields_set:
            _dict['UnitPrice'] = None

        # set to None if discount_pct (nullable) is None
        # and model_fields_set contains the field
        if self.discount_pct is None and "discount_pct" in self.model_fields_set:
            _dict['DiscountPct'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadEInvoiceLine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "EInvoiceID": obj.get("EInvoiceID"),
            "InvoiceLineType": obj.get("InvoiceLineType"),
            "ItemCode": obj.get("ItemCode"),
            "Description": obj.get("Description"),
            "Quantity": obj.get("Quantity"),
            "UnitPrice": obj.get("UnitPrice"),
            "DiscountPct": obj.get("DiscountPct"),
            "ProjectCode": obj.get("ProjectCode"),
            "CurrencyCode": obj.get("CurrencyCode"),
            "WarehouseCode": obj.get("WarehouseCode"),
            "ResourceCode": obj.get("ResourceCode"),
            "PricelistCode": obj.get("PricelistCode"),
            "Contract_ID": obj.get("Contract_ID"),
            "Order_ID": obj.get("Order_ID"),
            "WorkingTicket_ID": obj.get("WorkingTicket_ID"),
            "WorkFlow_ID": obj.get("WorkFlow_ID"),
            "Invoice_ID": obj.get("Invoice_ID"),
            "Reference": obj.get("Reference"),
            "CostCode": obj.get("CostCode"),
            "CostCategory": obj.get("CostCategory"),
            "BudgetCode": obj.get("BudgetCode"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


