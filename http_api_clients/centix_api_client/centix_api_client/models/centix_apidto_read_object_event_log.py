# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectEventLog(BaseModel):
    """
    CentixAPIDTOReadObjectEventLog
    """ # noqa: E501
    status_id: Optional[StrictInt] = Field(default=None, alias="StatusID")
    entity_auto_id: Optional[StrictInt] = Field(default=None, alias="EntityAutoID")
    event: Optional[StrictInt] = Field(default=None, description="0 = StatusChanged, 1 = AssignedToPersonChanged, 2 = EntityCreated, 3 = EntityUpdated, 4 = EntityDeleted, 100 = Expired, 200 = DeliverDateChanged, 201 = SignatureChanged, 300 = WorkflowGroupChanged, 301 = WorkflowSubgroupChanged, 302 = WorkflowPriorityChanged, 400 = AccountCreated, 401 = AccountCreatedWithActivationlink, 402 = AccountLocked, 403 = AccountMaxLoginExceeded, 404 = AccountExpired, 405 = AccountResetPassword, 406 = AccountPasswordChanged, 407 = AccountUnlocked, 408 = AccountActivated, 500 = WebhookMessageFailed", alias="Event")
    triggered_by_person_id: Optional[StrictInt] = Field(default=None, alias="TriggeredByPersonID")
    event_time: Optional[datetime] = Field(default=None, alias="EventTime")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["StatusID", "EntityAutoID", "Event", "TriggeredByPersonID", "EventTime", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('event')
    def event_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 100, 200, 201, 300, 301, 302, 400, 401, 402, 403, 404, 405, 406, 407, 408, 500]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 100, 200, 201, 300, 301, 302, 400, 401, 402, 403, 404, 405, 406, 407, 408, 500)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectEventLog from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if triggered_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.triggered_by_person_id is None and "triggered_by_person_id" in self.model_fields_set:
            _dict['TriggeredByPersonID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectEventLog from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "StatusID": obj.get("StatusID"),
            "EntityAutoID": obj.get("EntityAutoID"),
            "Event": obj.get("Event"),
            "TriggeredByPersonID": obj.get("TriggeredByPersonID"),
            "EventTime": obj.get("EventTime"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


