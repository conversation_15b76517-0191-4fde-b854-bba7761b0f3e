# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadPackListItemObject(BaseModel):
    """
    CentixAPIDTOReadPackListItemObject
    """ # noqa: E501
    pack_list_item_id: Optional[StrictInt] = Field(default=None, alias="PackListItemID")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    object00_location_id: Optional[StrictInt] = Field(default=None, alias="Object00LocationID")
    rental_object_entry_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectEntryQuantity")
    rental_object_return_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectReturnQuantity")
    object_owner_id: Optional[StrictStr] = Field(default=None, alias="ObjectOwner_ID")
    object_owner_name: Optional[StrictStr] = Field(default=None, alias="ObjectOwner_Name")
    object_owner_name2: Optional[StrictStr] = Field(default=None, alias="ObjectOwner_Name2")
    object_descr: Optional[StrictStr] = Field(default=None, alias="Object_Descr")
    object_descr2: Optional[StrictStr] = Field(default=None, alias="Object_Descr2")
    object_aidc: Optional[StrictStr] = Field(default=None, alias="Object_AIDC")
    object_brand_id: Optional[StrictStr] = Field(default=None, alias="Object_Brand_ID")
    object_brand_descr: Optional[StrictStr] = Field(default=None, alias="Object_Brand_Descr")
    object_type_id: Optional[StrictStr] = Field(default=None, alias="Object_Type_ID")
    object_type_descr: Optional[StrictStr] = Field(default=None, alias="Object_Type_Descr")
    object_serial_no: Optional[StrictStr] = Field(default=None, alias="Object_SerialNo")
    object_is_unique_item: Optional[StrictBool] = Field(default=None, alias="Object_IsUniqueItem")
    object_object_type_id: Optional[StrictStr] = Field(default=None, alias="Object_ObjectType_ID")
    object_object_type_descr: Optional[StrictStr] = Field(default=None, alias="Object_ObjectType_Descr")
    object_cost_code_id: Optional[StrictStr] = Field(default=None, alias="Object_CostCode_ID")
    object_cost_code_descr: Optional[StrictStr] = Field(default=None, alias="Object_CostCode_Descr")
    object_cost_category_id: Optional[StrictStr] = Field(default=None, alias="Object_CostCategory_ID")
    object_cost_category_descr: Optional[StrictStr] = Field(default=None, alias="Object_CostCategory_Descr")
    object_object_status_id: Optional[StrictStr] = Field(default=None, alias="Object_ObjectStatus_ID")
    object_object_status_descr: Optional[StrictStr] = Field(default=None, alias="Object_ObjectStatus_Descr")
    object_condition_id: Optional[StrictStr] = Field(default=None, alias="Object_Condition_ID")
    object_condition_descr: Optional[StrictStr] = Field(default=None, alias="Object_Condition_Descr")
    object_owner_reference: Optional[StrictStr] = Field(default=None, alias="Object_OwnerReference")
    object_revision_id: Optional[StrictStr] = Field(default=None, alias="Object_Revision_ID")
    object_revision_descr: Optional[StrictStr] = Field(default=None, alias="Object_Revision_Descr")
    object_construction_year: Optional[StrictStr] = Field(default=None, alias="Object_ConstructionYear")
    meter_reading_date: Optional[datetime] = Field(default=None, alias="MeterReadingDate")
    meter_reading_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingValue")
    from_warehouse_id: Optional[StrictInt] = Field(default=None, alias="FromWarehouseID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["PackListItemID", "ObjectID", "Quantity", "Object00LocationID", "RentalObjectEntryQuantity", "RentalObjectReturnQuantity", "ObjectOwner_ID", "ObjectOwner_Name", "ObjectOwner_Name2", "Object_Descr", "Object_Descr2", "Object_AIDC", "Object_Brand_ID", "Object_Brand_Descr", "Object_Type_ID", "Object_Type_Descr", "Object_SerialNo", "Object_IsUniqueItem", "Object_ObjectType_ID", "Object_ObjectType_Descr", "Object_CostCode_ID", "Object_CostCode_Descr", "Object_CostCategory_ID", "Object_CostCategory_Descr", "Object_ObjectStatus_ID", "Object_ObjectStatus_Descr", "Object_Condition_ID", "Object_Condition_Descr", "Object_OwnerReference", "Object_Revision_ID", "Object_Revision_Descr", "Object_ConstructionYear", "MeterReadingDate", "MeterReadingValue", "FromWarehouseID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackListItemObject from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if pack_list_item_id (nullable) is None
        # and model_fields_set contains the field
        if self.pack_list_item_id is None and "pack_list_item_id" in self.model_fields_set:
            _dict['PackListItemID'] = None

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if object00_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.object00_location_id is None and "object00_location_id" in self.model_fields_set:
            _dict['Object00LocationID'] = None

        # set to None if rental_object_entry_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_entry_quantity is None and "rental_object_entry_quantity" in self.model_fields_set:
            _dict['RentalObjectEntryQuantity'] = None

        # set to None if rental_object_return_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_return_quantity is None and "rental_object_return_quantity" in self.model_fields_set:
            _dict['RentalObjectReturnQuantity'] = None

        # set to None if object_is_unique_item (nullable) is None
        # and model_fields_set contains the field
        if self.object_is_unique_item is None and "object_is_unique_item" in self.model_fields_set:
            _dict['Object_IsUniqueItem'] = None

        # set to None if meter_reading_date (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_date is None and "meter_reading_date" in self.model_fields_set:
            _dict['MeterReadingDate'] = None

        # set to None if meter_reading_value (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_value is None and "meter_reading_value" in self.model_fields_set:
            _dict['MeterReadingValue'] = None

        # set to None if from_warehouse_id (nullable) is None
        # and model_fields_set contains the field
        if self.from_warehouse_id is None and "from_warehouse_id" in self.model_fields_set:
            _dict['FromWarehouseID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackListItemObject from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "PackListItemID": obj.get("PackListItemID"),
            "ObjectID": obj.get("ObjectID"),
            "Quantity": obj.get("Quantity"),
            "Object00LocationID": obj.get("Object00LocationID"),
            "RentalObjectEntryQuantity": obj.get("RentalObjectEntryQuantity"),
            "RentalObjectReturnQuantity": obj.get("RentalObjectReturnQuantity"),
            "ObjectOwner_ID": obj.get("ObjectOwner_ID"),
            "ObjectOwner_Name": obj.get("ObjectOwner_Name"),
            "ObjectOwner_Name2": obj.get("ObjectOwner_Name2"),
            "Object_Descr": obj.get("Object_Descr"),
            "Object_Descr2": obj.get("Object_Descr2"),
            "Object_AIDC": obj.get("Object_AIDC"),
            "Object_Brand_ID": obj.get("Object_Brand_ID"),
            "Object_Brand_Descr": obj.get("Object_Brand_Descr"),
            "Object_Type_ID": obj.get("Object_Type_ID"),
            "Object_Type_Descr": obj.get("Object_Type_Descr"),
            "Object_SerialNo": obj.get("Object_SerialNo"),
            "Object_IsUniqueItem": obj.get("Object_IsUniqueItem"),
            "Object_ObjectType_ID": obj.get("Object_ObjectType_ID"),
            "Object_ObjectType_Descr": obj.get("Object_ObjectType_Descr"),
            "Object_CostCode_ID": obj.get("Object_CostCode_ID"),
            "Object_CostCode_Descr": obj.get("Object_CostCode_Descr"),
            "Object_CostCategory_ID": obj.get("Object_CostCategory_ID"),
            "Object_CostCategory_Descr": obj.get("Object_CostCategory_Descr"),
            "Object_ObjectStatus_ID": obj.get("Object_ObjectStatus_ID"),
            "Object_ObjectStatus_Descr": obj.get("Object_ObjectStatus_Descr"),
            "Object_Condition_ID": obj.get("Object_Condition_ID"),
            "Object_Condition_Descr": obj.get("Object_Condition_Descr"),
            "Object_OwnerReference": obj.get("Object_OwnerReference"),
            "Object_Revision_ID": obj.get("Object_Revision_ID"),
            "Object_Revision_Descr": obj.get("Object_Revision_Descr"),
            "Object_ConstructionYear": obj.get("Object_ConstructionYear"),
            "MeterReadingDate": obj.get("MeterReadingDate"),
            "MeterReadingValue": obj.get("MeterReadingValue"),
            "FromWarehouseID": obj.get("FromWarehouseID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


