# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt
from typing import Any, ClassVar, Dict, List, Optional
from centix_api_client.models.centix_apidto_read_brand_type_default_mi_schedule import CentixAPIDTOReadBrandTypeDefaultMISchedule
from typing import Optional, Set
from typing_extensions import Self

class CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule(BaseModel):
    """
    CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule
    """ # noqa: E501
    items: Optional[List[CentixAPIDTOReadBrandTypeDefaultMISchedule]] = Field(default=None, alias="Items")
    top: Optional[StrictInt] = Field(default=None, alias="Top")
    skip: Optional[StrictInt] = Field(default=None, alias="Skip")
    total_count: Optional[StrictInt] = Field(default=None, alias="TotalCount")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["Items", "Top", "Skip", "TotalCount"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in items (list)
        _items = []
        if self.items:
            for _item_items in self.items:
                if _item_items:
                    _items.append(_item_items.to_dict())
            _dict['Items'] = _items
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "Items": [CentixAPIDTOReadBrandTypeDefaultMISchedule.from_dict(_item) for _item in obj["Items"]] if obj.get("Items") is not None else None,
            "Top": obj.get("Top"),
            "Skip": obj.get("Skip"),
            "TotalCount": obj.get("TotalCount")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


