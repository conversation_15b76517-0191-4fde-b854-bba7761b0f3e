# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOCreatePerson(BaseModel):
    """
    CentixAPIDTOCreatePerson
    """ # noqa: E501
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    id: Annotated[str, Field(min_length=0, strict=True, max_length=20)] = Field(alias="ID")
    last_name: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="LastName")
    maiden_name: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="MaidenName")
    middle_name: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="MiddleName")
    first_name: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="FirstName")
    initials: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="Initials")
    title_id: Optional[StrictInt] = Field(default=None, alias="TitleID")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    email_address: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=320)]] = Field(default=None, alias="EmailAddress")
    phone_number: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="PhoneNumber")
    street: Optional[StrictStr] = Field(default=None, alias="Street")
    house_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNo")
    house_no_add: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNoAdd")
    postal_code: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=12)]] = Field(default=None, alias="PostalCode")
    city: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="City")
    country_id: Optional[StrictInt] = Field(default=None, alias="CountryID")
    state_id: Optional[StrictInt] = Field(default=None, alias="StateID")
    county_id: Optional[StrictInt] = Field(default=None, alias="CountyID")
    status_id: Optional[StrictInt] = Field(alias="StatusID")
    note: Optional[StrictStr] = Field(default=None, alias="Note")
    personnel_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="PersonnelNo")
    date_in_service: Optional[datetime] = Field(default=None, alias="DateInService")
    date_out_of_service: Optional[datetime] = Field(default=None, alias="DateOutOfService")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["RelationID", "ID", "LastName", "MaidenName", "MiddleName", "FirstName", "Initials", "TitleID", "LanguageID", "EmailAddress", "PhoneNumber", "Street", "HouseNo", "HouseNoAdd", "PostalCode", "City", "CountryID", "StateID", "CountyID", "StatusID", "Note", "PersonnelNo", "DateInService", "DateOutOfService"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreatePerson from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if title_id (nullable) is None
        # and model_fields_set contains the field
        if self.title_id is None and "title_id" in self.model_fields_set:
            _dict['TitleID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if country_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_id is None and "country_id" in self.model_fields_set:
            _dict['CountryID'] = None

        # set to None if state_id (nullable) is None
        # and model_fields_set contains the field
        if self.state_id is None and "state_id" in self.model_fields_set:
            _dict['StateID'] = None

        # set to None if county_id (nullable) is None
        # and model_fields_set contains the field
        if self.county_id is None and "county_id" in self.model_fields_set:
            _dict['CountyID'] = None

        # set to None if status_id (nullable) is None
        # and model_fields_set contains the field
        if self.status_id is None and "status_id" in self.model_fields_set:
            _dict['StatusID'] = None

        # set to None if date_in_service (nullable) is None
        # and model_fields_set contains the field
        if self.date_in_service is None and "date_in_service" in self.model_fields_set:
            _dict['DateInService'] = None

        # set to None if date_out_of_service (nullable) is None
        # and model_fields_set contains the field
        if self.date_out_of_service is None and "date_out_of_service" in self.model_fields_set:
            _dict['DateOutOfService'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreatePerson from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "RelationID": obj.get("RelationID"),
            "ID": obj.get("ID"),
            "LastName": obj.get("LastName"),
            "MaidenName": obj.get("MaidenName"),
            "MiddleName": obj.get("MiddleName"),
            "FirstName": obj.get("FirstName"),
            "Initials": obj.get("Initials"),
            "TitleID": obj.get("TitleID"),
            "LanguageID": obj.get("LanguageID"),
            "EmailAddress": obj.get("EmailAddress"),
            "PhoneNumber": obj.get("PhoneNumber"),
            "Street": obj.get("Street"),
            "HouseNo": obj.get("HouseNo"),
            "HouseNoAdd": obj.get("HouseNoAdd"),
            "PostalCode": obj.get("PostalCode"),
            "City": obj.get("City"),
            "CountryID": obj.get("CountryID"),
            "StateID": obj.get("StateID"),
            "CountyID": obj.get("CountyID"),
            "StatusID": obj.get("StatusID"),
            "Note": obj.get("Note"),
            "PersonnelNo": obj.get("PersonnelNo"),
            "DateInService": obj.get("DateInService"),
            "DateOutOfService": obj.get("DateOutOfService")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


