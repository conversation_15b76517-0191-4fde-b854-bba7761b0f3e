# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule(BaseModel):
    """
    CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule
    """ # noqa: E501
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    measuring_device1_measurement_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1MeasurementID")
    auto_create: Optional[StrictBool] = Field(default=None, alias="AutoCreate")
    auto_create_offset: Optional[StrictInt] = Field(default=None, alias="AutoCreateOffset")
    auto_create_offset_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="AutoCreateOffsetUnit")
    time_schedule_id: Optional[StrictInt] = Field(default=None, alias="TimeScheduleID")
    start_at: Optional[datetime] = Field(default=None, alias="StartAt")
    end_at: Optional[datetime] = Field(default=None, alias="EndAt")
    last_run: Optional[datetime] = Field(default=None, alias="LastRun")
    next_run: Optional[datetime] = Field(default=None, alias="NextRun")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ObjectID", "MeasuringDevice1MeasurementID", "AutoCreate", "AutoCreateOffset", "AutoCreateOffsetUnit", "TimeScheduleID", "StartAt", "EndAt", "LastRun", "NextRun", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('auto_create_offset_unit')
    def auto_create_offset_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if measuring_device1_measurement_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_measurement_id is None and "measuring_device1_measurement_id" in self.model_fields_set:
            _dict['MeasuringDevice1MeasurementID'] = None

        # set to None if auto_create (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create is None and "auto_create" in self.model_fields_set:
            _dict['AutoCreate'] = None

        # set to None if auto_create_offset (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create_offset is None and "auto_create_offset" in self.model_fields_set:
            _dict['AutoCreateOffset'] = None

        # set to None if auto_create_offset_unit (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create_offset_unit is None and "auto_create_offset_unit" in self.model_fields_set:
            _dict['AutoCreateOffsetUnit'] = None

        # set to None if time_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.time_schedule_id is None and "time_schedule_id" in self.model_fields_set:
            _dict['TimeScheduleID'] = None

        # set to None if start_at (nullable) is None
        # and model_fields_set contains the field
        if self.start_at is None and "start_at" in self.model_fields_set:
            _dict['StartAt'] = None

        # set to None if end_at (nullable) is None
        # and model_fields_set contains the field
        if self.end_at is None and "end_at" in self.model_fields_set:
            _dict['EndAt'] = None

        # set to None if last_run (nullable) is None
        # and model_fields_set contains the field
        if self.last_run is None and "last_run" in self.model_fields_set:
            _dict['LastRun'] = None

        # set to None if next_run (nullable) is None
        # and model_fields_set contains the field
        if self.next_run is None and "next_run" in self.model_fields_set:
            _dict['NextRun'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ObjectID": obj.get("ObjectID"),
            "MeasuringDevice1MeasurementID": obj.get("MeasuringDevice1MeasurementID"),
            "AutoCreate": obj.get("AutoCreate"),
            "AutoCreateOffset": obj.get("AutoCreateOffset"),
            "AutoCreateOffsetUnit": obj.get("AutoCreateOffsetUnit"),
            "TimeScheduleID": obj.get("TimeScheduleID"),
            "StartAt": obj.get("StartAt"),
            "EndAt": obj.get("EndAt"),
            "LastRun": obj.get("LastRun"),
            "NextRun": obj.get("NextRun"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


