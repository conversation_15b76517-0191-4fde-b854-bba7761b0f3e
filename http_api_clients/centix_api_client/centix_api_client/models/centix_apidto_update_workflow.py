# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateWorkflow(BaseModel):
    """
    CentixAPIDTOUpdateWorkflow
    """ # noqa: E501
    short_descr: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="ShortDescr")
    contact_person_id: Optional[StrictInt] = Field(default=None, alias="ContactPersonID")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    work_flow_group_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowGroupID")
    work_flow_sub_group_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowSubGroupID")
    work_flow_priority_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowPriorityID")
    work_flow_service_level_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowServiceLevelID")
    escalation_level_id: Optional[StrictInt] = Field(default=None, alias="EscalationLevelID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    contract_id: Optional[StrictInt] = Field(default=None, alias="ContractID")
    status_id: Optional[StrictInt] = Field(alias="StatusID")
    work_flow_kind: Optional[StrictInt] = Field(default=None, alias="WorkFlowKind")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    work_place_id: Optional[StrictInt] = Field(default=None, alias="WorkPlaceID")
    reference: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Reference")
    cause: Optional[StrictStr] = Field(default=None, alias="Cause")
    resolution: Optional[StrictStr] = Field(default=None, alias="Resolution")
    category_id: Optional[StrictInt] = Field(default=None, alias="CategoryID")
    street: Optional[StrictStr] = Field(default=None, alias="Street")
    house_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNo")
    house_no_add: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNoAdd")
    postalcode: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=12)]] = Field(default=None, alias="Postalcode")
    city: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="City")
    country_id: Optional[StrictInt] = Field(default=None, alias="CountryID")
    state_id: Optional[StrictInt] = Field(default=None, alias="StateID")
    county_id: Optional[StrictInt] = Field(default=None, alias="CountyID")
    region_id: Optional[StrictInt] = Field(default=None, alias="RegionID")
    additional_address_info1: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo1")
    additional_address_info2: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo2")
    search_tags: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=256)]] = Field(default=None, alias="SearchTags")
    reported_by_person_id: Optional[StrictInt] = Field(default=None, alias="ReportedByPersonID")
    assigned_to_team_id: Optional[StrictInt] = Field(default=None, alias="AssignedToTeamID")
    due_date: Optional[datetime] = Field(default=None, alias="DueDate")
    is_fixed_price_work: Optional[StrictBool] = Field(default=None, alias="IsFixedPriceWork")
    fixed_price_work_suppress_product_realizations: Optional[StrictBool] = Field(default=None, alias="FixedPriceWorkSuppressProductRealizations")
    fixed_price_work_suppress_hour_realizations: Optional[StrictBool] = Field(default=None, alias="FixedPriceWorkSuppressHourRealizations")
    product_location_id: Optional[StrictInt] = Field(default=None, alias="ProductLocationID")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ShortDescr", "ContactPersonID", "AssignedToPersonID", "Descr", "ObjectID", "WorkFlowGroupID", "WorkFlowSubGroupID", "WorkFlowPriorityID", "WorkFlowServiceLevelID", "EscalationLevelID", "ProjectID", "CostCodeID", "ContractID", "StatusID", "WorkFlowKind", "CostCategoryID", "WorkPlaceID", "Reference", "Cause", "Resolution", "CategoryID", "Street", "HouseNo", "HouseNoAdd", "Postalcode", "City", "CountryID", "StateID", "CountyID", "RegionID", "AdditionalAddressInfo1", "AdditionalAddressInfo2", "SearchTags", "ReportedByPersonID", "AssignedToTeamID", "DueDate", "IsFixedPriceWork", "FixedPriceWorkSuppressProductRealizations", "FixedPriceWorkSuppressHourRealizations", "ProductLocationID"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateWorkflow from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.contact_person_id is None and "contact_person_id" in self.model_fields_set:
            _dict['ContactPersonID'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if work_flow_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_group_id is None and "work_flow_group_id" in self.model_fields_set:
            _dict['WorkFlowGroupID'] = None

        # set to None if work_flow_sub_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_sub_group_id is None and "work_flow_sub_group_id" in self.model_fields_set:
            _dict['WorkFlowSubGroupID'] = None

        # set to None if work_flow_priority_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_priority_id is None and "work_flow_priority_id" in self.model_fields_set:
            _dict['WorkFlowPriorityID'] = None

        # set to None if work_flow_service_level_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_service_level_id is None and "work_flow_service_level_id" in self.model_fields_set:
            _dict['WorkFlowServiceLevelID'] = None

        # set to None if escalation_level_id (nullable) is None
        # and model_fields_set contains the field
        if self.escalation_level_id is None and "escalation_level_id" in self.model_fields_set:
            _dict['EscalationLevelID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if contract_id (nullable) is None
        # and model_fields_set contains the field
        if self.contract_id is None and "contract_id" in self.model_fields_set:
            _dict['ContractID'] = None

        # set to None if status_id (nullable) is None
        # and model_fields_set contains the field
        if self.status_id is None and "status_id" in self.model_fields_set:
            _dict['StatusID'] = None

        # set to None if work_flow_kind (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_kind is None and "work_flow_kind" in self.model_fields_set:
            _dict['WorkFlowKind'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if work_place_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_place_id is None and "work_place_id" in self.model_fields_set:
            _dict['WorkPlaceID'] = None

        # set to None if category_id (nullable) is None
        # and model_fields_set contains the field
        if self.category_id is None and "category_id" in self.model_fields_set:
            _dict['CategoryID'] = None

        # set to None if country_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_id is None and "country_id" in self.model_fields_set:
            _dict['CountryID'] = None

        # set to None if state_id (nullable) is None
        # and model_fields_set contains the field
        if self.state_id is None and "state_id" in self.model_fields_set:
            _dict['StateID'] = None

        # set to None if county_id (nullable) is None
        # and model_fields_set contains the field
        if self.county_id is None and "county_id" in self.model_fields_set:
            _dict['CountyID'] = None

        # set to None if region_id (nullable) is None
        # and model_fields_set contains the field
        if self.region_id is None and "region_id" in self.model_fields_set:
            _dict['RegionID'] = None

        # set to None if reported_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.reported_by_person_id is None and "reported_by_person_id" in self.model_fields_set:
            _dict['ReportedByPersonID'] = None

        # set to None if assigned_to_team_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_team_id is None and "assigned_to_team_id" in self.model_fields_set:
            _dict['AssignedToTeamID'] = None

        # set to None if due_date (nullable) is None
        # and model_fields_set contains the field
        if self.due_date is None and "due_date" in self.model_fields_set:
            _dict['DueDate'] = None

        # set to None if product_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_location_id is None and "product_location_id" in self.model_fields_set:
            _dict['ProductLocationID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateWorkflow from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ShortDescr": obj.get("ShortDescr"),
            "ContactPersonID": obj.get("ContactPersonID"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "Descr": obj.get("Descr"),
            "ObjectID": obj.get("ObjectID"),
            "WorkFlowGroupID": obj.get("WorkFlowGroupID"),
            "WorkFlowSubGroupID": obj.get("WorkFlowSubGroupID"),
            "WorkFlowPriorityID": obj.get("WorkFlowPriorityID"),
            "WorkFlowServiceLevelID": obj.get("WorkFlowServiceLevelID"),
            "EscalationLevelID": obj.get("EscalationLevelID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "ContractID": obj.get("ContractID"),
            "StatusID": obj.get("StatusID"),
            "WorkFlowKind": obj.get("WorkFlowKind"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "WorkPlaceID": obj.get("WorkPlaceID"),
            "Reference": obj.get("Reference"),
            "Cause": obj.get("Cause"),
            "Resolution": obj.get("Resolution"),
            "CategoryID": obj.get("CategoryID"),
            "Street": obj.get("Street"),
            "HouseNo": obj.get("HouseNo"),
            "HouseNoAdd": obj.get("HouseNoAdd"),
            "Postalcode": obj.get("Postalcode"),
            "City": obj.get("City"),
            "CountryID": obj.get("CountryID"),
            "StateID": obj.get("StateID"),
            "CountyID": obj.get("CountyID"),
            "RegionID": obj.get("RegionID"),
            "AdditionalAddressInfo1": obj.get("AdditionalAddressInfo1"),
            "AdditionalAddressInfo2": obj.get("AdditionalAddressInfo2"),
            "SearchTags": obj.get("SearchTags"),
            "ReportedByPersonID": obj.get("ReportedByPersonID"),
            "AssignedToTeamID": obj.get("AssignedToTeamID"),
            "DueDate": obj.get("DueDate"),
            "IsFixedPriceWork": obj.get("IsFixedPriceWork"),
            "FixedPriceWorkSuppressProductRealizations": obj.get("FixedPriceWorkSuppressProductRealizations"),
            "FixedPriceWorkSuppressHourRealizations": obj.get("FixedPriceWorkSuppressHourRealizations"),
            "ProductLocationID": obj.get("ProductLocationID")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


