# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObject(BaseModel):
    """
    CentixAPIDTOReadObject
    """ # noqa: E501
    aidc: Optional[StrictStr] = Field(default=None, alias="AIDC")
    product_id: Optional[StrictInt] = Field(default=None, alias="ProductID")
    brand_id: Optional[StrictInt] = Field(default=None, alias="BrandID")
    condition_id: Optional[StrictInt] = Field(default=None, alias="ConditionID")
    construction_year: Optional[StrictStr] = Field(default=None, alias="ConstructionYear")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    descr2: Optional[StrictStr] = Field(default=None, alias="Descr2")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    is_product: Optional[StrictBool] = Field(default=None, alias="IsProduct")
    is_rental_product: Optional[StrictBool] = Field(default=None, alias="IsRentalProduct")
    is_stock: Optional[StrictBool] = Field(default=None, alias="IsStock")
    is_unique_item: Optional[StrictBool] = Field(default=None, alias="IsUniqueItem")
    measuring_device_type_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDeviceTypeID")
    mi_valid_until_date: Optional[datetime] = Field(default=None, alias="MIValidUntilDate")
    next_inspection_date: Optional[datetime] = Field(default=None, alias="NextInspectionDate")
    object_mi_state: Optional[StrictInt] = Field(default=None, description="1 = Ok, 2 = Fail", alias="ObjectMIState")
    object_owner_id: Optional[StrictInt] = Field(default=None, alias="ObjectOwnerID")
    object_status_id: Optional[StrictInt] = Field(default=None, alias="ObjectStatusID")
    object_type_id: Optional[StrictInt] = Field(default=None, alias="ObjectTypeID")
    owner_reference: Optional[StrictStr] = Field(default=None, alias="OwnerReference")
    parent_object_id: Optional[StrictInt] = Field(default=None, alias="ParentObjectID")
    revision_id: Optional[StrictInt] = Field(default=None, alias="RevisionID")
    search_tag: Optional[StrictStr] = Field(default=None, alias="SearchTag")
    selection_code: Optional[StrictStr] = Field(default=None, alias="SelectionCode")
    send_alert: Optional[StrictBool] = Field(default=None, alias="SendAlert")
    serial_no: Optional[StrictStr] = Field(default=None, alias="SerialNo")
    type_id: Optional[StrictInt] = Field(default=None, alias="TypeID")
    supplier_id: Optional[StrictInt] = Field(default=None, alias="SupplierID")
    supplier_article_no: Optional[StrictStr] = Field(default=None, alias="SupplierArticleNo")
    purchase_date: Optional[datetime] = Field(default=None, alias="PurchaseDate")
    purchase_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PurchasePrice")
    purchase_currency_id: Optional[StrictInt] = Field(default=None, alias="PurchaseCurrencyID")
    purchase_order: Optional[StrictStr] = Field(default=None, alias="PurchaseOrder")
    country_of_origin_id: Optional[StrictInt] = Field(default=None, alias="CountryOfOriginID")
    hs_code_id: Optional[StrictInt] = Field(default=None, alias="HSCodeID")
    note: Optional[StrictStr] = Field(default=None, alias="Note")
    base_location_id: Optional[StrictInt] = Field(default=None, alias="BaseLocationID")
    end_of_life_date: Optional[datetime] = Field(default=None, alias="EndOfLifeDate")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AIDC", "ProductID", "BrandID", "ConditionID", "ConstructionYear", "CostCategoryID", "CostCodeID", "Descr", "Descr2", "ID", "IsProduct", "IsRentalProduct", "IsStock", "IsUniqueItem", "MeasuringDeviceTypeID", "MIValidUntilDate", "NextInspectionDate", "ObjectMIState", "ObjectOwnerID", "ObjectStatusID", "ObjectTypeID", "OwnerReference", "ParentObjectID", "RevisionID", "SearchTag", "SelectionCode", "SendAlert", "SerialNo", "TypeID", "SupplierID", "SupplierArticleNo", "PurchaseDate", "PurchasePrice", "PurchaseCurrencyID", "PurchaseOrder", "CountryOfOriginID", "HSCodeID", "Note", "BaseLocationID", "EndOfLifeDate", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('object_mi_state')
    def object_mi_state_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2]):
            raise ValueError("must be one of enum values (1, 2)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObject from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if brand_id (nullable) is None
        # and model_fields_set contains the field
        if self.brand_id is None and "brand_id" in self.model_fields_set:
            _dict['BrandID'] = None

        # set to None if condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.condition_id is None and "condition_id" in self.model_fields_set:
            _dict['ConditionID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if is_stock (nullable) is None
        # and model_fields_set contains the field
        if self.is_stock is None and "is_stock" in self.model_fields_set:
            _dict['IsStock'] = None

        # set to None if measuring_device_type_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device_type_id is None and "measuring_device_type_id" in self.model_fields_set:
            _dict['MeasuringDeviceTypeID'] = None

        # set to None if mi_valid_until_date (nullable) is None
        # and model_fields_set contains the field
        if self.mi_valid_until_date is None and "mi_valid_until_date" in self.model_fields_set:
            _dict['MIValidUntilDate'] = None

        # set to None if next_inspection_date (nullable) is None
        # and model_fields_set contains the field
        if self.next_inspection_date is None and "next_inspection_date" in self.model_fields_set:
            _dict['NextInspectionDate'] = None

        # set to None if object_mi_state (nullable) is None
        # and model_fields_set contains the field
        if self.object_mi_state is None and "object_mi_state" in self.model_fields_set:
            _dict['ObjectMIState'] = None

        # set to None if parent_object_id (nullable) is None
        # and model_fields_set contains the field
        if self.parent_object_id is None and "parent_object_id" in self.model_fields_set:
            _dict['ParentObjectID'] = None

        # set to None if revision_id (nullable) is None
        # and model_fields_set contains the field
        if self.revision_id is None and "revision_id" in self.model_fields_set:
            _dict['RevisionID'] = None

        # set to None if type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_id is None and "type_id" in self.model_fields_set:
            _dict['TypeID'] = None

        # set to None if supplier_id (nullable) is None
        # and model_fields_set contains the field
        if self.supplier_id is None and "supplier_id" in self.model_fields_set:
            _dict['SupplierID'] = None

        # set to None if purchase_date (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_date is None and "purchase_date" in self.model_fields_set:
            _dict['PurchaseDate'] = None

        # set to None if purchase_price (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_price is None and "purchase_price" in self.model_fields_set:
            _dict['PurchasePrice'] = None

        # set to None if purchase_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_currency_id is None and "purchase_currency_id" in self.model_fields_set:
            _dict['PurchaseCurrencyID'] = None

        # set to None if country_of_origin_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_of_origin_id is None and "country_of_origin_id" in self.model_fields_set:
            _dict['CountryOfOriginID'] = None

        # set to None if hs_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.hs_code_id is None and "hs_code_id" in self.model_fields_set:
            _dict['HSCodeID'] = None

        # set to None if base_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.base_location_id is None and "base_location_id" in self.model_fields_set:
            _dict['BaseLocationID'] = None

        # set to None if end_of_life_date (nullable) is None
        # and model_fields_set contains the field
        if self.end_of_life_date is None and "end_of_life_date" in self.model_fields_set:
            _dict['EndOfLifeDate'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObject from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AIDC": obj.get("AIDC"),
            "ProductID": obj.get("ProductID"),
            "BrandID": obj.get("BrandID"),
            "ConditionID": obj.get("ConditionID"),
            "ConstructionYear": obj.get("ConstructionYear"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "Descr": obj.get("Descr"),
            "Descr2": obj.get("Descr2"),
            "ID": obj.get("ID"),
            "IsProduct": obj.get("IsProduct"),
            "IsRentalProduct": obj.get("IsRentalProduct"),
            "IsStock": obj.get("IsStock"),
            "IsUniqueItem": obj.get("IsUniqueItem"),
            "MeasuringDeviceTypeID": obj.get("MeasuringDeviceTypeID"),
            "MIValidUntilDate": obj.get("MIValidUntilDate"),
            "NextInspectionDate": obj.get("NextInspectionDate"),
            "ObjectMIState": obj.get("ObjectMIState"),
            "ObjectOwnerID": obj.get("ObjectOwnerID"),
            "ObjectStatusID": obj.get("ObjectStatusID"),
            "ObjectTypeID": obj.get("ObjectTypeID"),
            "OwnerReference": obj.get("OwnerReference"),
            "ParentObjectID": obj.get("ParentObjectID"),
            "RevisionID": obj.get("RevisionID"),
            "SearchTag": obj.get("SearchTag"),
            "SelectionCode": obj.get("SelectionCode"),
            "SendAlert": obj.get("SendAlert"),
            "SerialNo": obj.get("SerialNo"),
            "TypeID": obj.get("TypeID"),
            "SupplierID": obj.get("SupplierID"),
            "SupplierArticleNo": obj.get("SupplierArticleNo"),
            "PurchaseDate": obj.get("PurchaseDate"),
            "PurchasePrice": obj.get("PurchasePrice"),
            "PurchaseCurrencyID": obj.get("PurchaseCurrencyID"),
            "PurchaseOrder": obj.get("PurchaseOrder"),
            "CountryOfOriginID": obj.get("CountryOfOriginID"),
            "HSCodeID": obj.get("HSCodeID"),
            "Note": obj.get("Note"),
            "BaseLocationID": obj.get("BaseLocationID"),
            "EndOfLifeDate": obj.get("EndOfLifeDate"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


