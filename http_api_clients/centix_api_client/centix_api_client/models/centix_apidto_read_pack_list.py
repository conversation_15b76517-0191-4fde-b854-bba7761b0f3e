# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from centix_api_client.models.centix_apidto_read_pack_list_item import CentixAPIDTOReadPackListItem
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadPackList(BaseModel):
    """
    CentixAPIDTOReadPackList
    """ # noqa: E501
    pack_list_lines: Optional[List[CentixAPIDTOReadPackListItem]] = Field(default=None, alias="PackListLines")
    order_id: Optional[StrictInt] = Field(default=None, alias="OrderID")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    deliver_date: Optional[datetime] = Field(default=None, alias="DeliverDate")
    invoice_relation_id: Optional[StrictInt] = Field(default=None, alias="InvoiceRelationID")
    deliver_relation_id: Optional[StrictInt] = Field(default=None, alias="DeliverRelationID")
    deliver_address: Optional[StrictStr] = Field(default=None, alias="DeliverAddress")
    deliver_address_house_no: Optional[StrictStr] = Field(default=None, alias="DeliverAddressHouseNo")
    deliver_address_house_no_add: Optional[StrictStr] = Field(default=None, alias="DeliverAddressHouseNoAdd")
    deliver_postal_code: Optional[StrictStr] = Field(default=None, alias="DeliverPostalCode")
    deliver_city: Optional[StrictStr] = Field(default=None, alias="DeliverCity")
    deliver_country_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountryID")
    deliver_country_state_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountryStateID")
    deliver_location_id: Optional[StrictInt] = Field(default=None, alias="DeliverLocationID")
    status: Optional[StrictInt] = Field(default=None, alias="Status")
    deliver_region_id: Optional[StrictInt] = Field(default=None, alias="DeliverRegionID")
    total_weight: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="TotalWeight")
    deliver_relation_descr: Optional[StrictStr] = Field(default=None, alias="DeliverRelationDescr")
    deliver_person_descr: Optional[StrictStr] = Field(default=None, alias="DeliverPersonDescr")
    deliver_additional_address_info1: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo1")
    deliver_additional_address_info2: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo2")
    signed_by: Optional[StrictStr] = Field(default=None, alias="SignedBy")
    no_signature_reason_id: Optional[StrictInt] = Field(default=None, alias="NoSignatureReasonID")
    deliver_country_state_county_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountryStateCountyID")
    deliver_contact_person_id: Optional[StrictInt] = Field(default=None, alias="DeliverContactPersonID")
    invoice_contact_person_id: Optional[StrictInt] = Field(default=None, alias="InvoiceContactPersonID")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    deliver_location_descr: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_Descr")
    deliver_location_descr2: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_Descr2")
    deliver_location_aidc: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_AIDC")
    deliver_location_is_stock: Optional[StrictBool] = Field(default=None, alias="DeliverLocation_IsStock")
    deliver_location_location_owner_id: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationOwner_ID")
    deliver_location_location_owner_name: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationOwner_Name")
    deliver_location_location_owner_name2: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationOwner_Name2")
    deliver_location_location_type_id: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationType_ID")
    deliver_location_location_type_descr: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationType_Descr")
    deliver_location_location_status_id: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationStatus_ID")
    deliver_location_location_status_descr: Optional[StrictStr] = Field(default=None, alias="DeliverLocation_LocationStatus_Descr")
    ware_house_id: Optional[StrictInt] = Field(default=None, alias="WareHouseID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["PackListLines", "OrderID", "AdministrationID", "ID", "DeliverDate", "InvoiceRelationID", "DeliverRelationID", "DeliverAddress", "DeliverAddressHouseNo", "DeliverAddressHouseNoAdd", "DeliverPostalCode", "DeliverCity", "DeliverCountryID", "DeliverCountryStateID", "DeliverLocationID", "Status", "DeliverRegionID", "TotalWeight", "DeliverRelationDescr", "DeliverPersonDescr", "DeliverAdditionalAddressInfo1", "DeliverAdditionalAddressInfo2", "SignedBy", "NoSignatureReasonID", "DeliverCountryStateCountyID", "DeliverContactPersonID", "InvoiceContactPersonID", "AssignedToPersonID", "DeliverLocation_Descr", "DeliverLocation_Descr2", "DeliverLocation_AIDC", "DeliverLocation_IsStock", "DeliverLocation_LocationOwner_ID", "DeliverLocation_LocationOwner_Name", "DeliverLocation_LocationOwner_Name2", "DeliverLocation_LocationType_ID", "DeliverLocation_LocationType_Descr", "DeliverLocation_LocationStatus_ID", "DeliverLocation_LocationStatus_Descr", "WareHouseID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackList from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in pack_list_lines (list)
        _items = []
        if self.pack_list_lines:
            for _item_pack_list_lines in self.pack_list_lines:
                if _item_pack_list_lines:
                    _items.append(_item_pack_list_lines.to_dict())
            _dict['PackListLines'] = _items
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if deliver_country_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_country_id is None and "deliver_country_id" in self.model_fields_set:
            _dict['DeliverCountryID'] = None

        # set to None if deliver_country_state_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_country_state_id is None and "deliver_country_state_id" in self.model_fields_set:
            _dict['DeliverCountryStateID'] = None

        # set to None if deliver_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_location_id is None and "deliver_location_id" in self.model_fields_set:
            _dict['DeliverLocationID'] = None

        # set to None if deliver_region_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_region_id is None and "deliver_region_id" in self.model_fields_set:
            _dict['DeliverRegionID'] = None

        # set to None if no_signature_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.no_signature_reason_id is None and "no_signature_reason_id" in self.model_fields_set:
            _dict['NoSignatureReasonID'] = None

        # set to None if deliver_country_state_county_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_country_state_county_id is None and "deliver_country_state_county_id" in self.model_fields_set:
            _dict['DeliverCountryStateCountyID'] = None

        # set to None if deliver_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_contact_person_id is None and "deliver_contact_person_id" in self.model_fields_set:
            _dict['DeliverContactPersonID'] = None

        # set to None if invoice_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_contact_person_id is None and "invoice_contact_person_id" in self.model_fields_set:
            _dict['InvoiceContactPersonID'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        # set to None if deliver_location_is_stock (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_location_is_stock is None and "deliver_location_is_stock" in self.model_fields_set:
            _dict['DeliverLocation_IsStock'] = None

        # set to None if ware_house_id (nullable) is None
        # and model_fields_set contains the field
        if self.ware_house_id is None and "ware_house_id" in self.model_fields_set:
            _dict['WareHouseID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackList from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "PackListLines": [CentixAPIDTOReadPackListItem.from_dict(_item) for _item in obj["PackListLines"]] if obj.get("PackListLines") is not None else None,
            "OrderID": obj.get("OrderID"),
            "AdministrationID": obj.get("AdministrationID"),
            "ID": obj.get("ID"),
            "DeliverDate": obj.get("DeliverDate"),
            "InvoiceRelationID": obj.get("InvoiceRelationID"),
            "DeliverRelationID": obj.get("DeliverRelationID"),
            "DeliverAddress": obj.get("DeliverAddress"),
            "DeliverAddressHouseNo": obj.get("DeliverAddressHouseNo"),
            "DeliverAddressHouseNoAdd": obj.get("DeliverAddressHouseNoAdd"),
            "DeliverPostalCode": obj.get("DeliverPostalCode"),
            "DeliverCity": obj.get("DeliverCity"),
            "DeliverCountryID": obj.get("DeliverCountryID"),
            "DeliverCountryStateID": obj.get("DeliverCountryStateID"),
            "DeliverLocationID": obj.get("DeliverLocationID"),
            "Status": obj.get("Status"),
            "DeliverRegionID": obj.get("DeliverRegionID"),
            "TotalWeight": obj.get("TotalWeight"),
            "DeliverRelationDescr": obj.get("DeliverRelationDescr"),
            "DeliverPersonDescr": obj.get("DeliverPersonDescr"),
            "DeliverAdditionalAddressInfo1": obj.get("DeliverAdditionalAddressInfo1"),
            "DeliverAdditionalAddressInfo2": obj.get("DeliverAdditionalAddressInfo2"),
            "SignedBy": obj.get("SignedBy"),
            "NoSignatureReasonID": obj.get("NoSignatureReasonID"),
            "DeliverCountryStateCountyID": obj.get("DeliverCountryStateCountyID"),
            "DeliverContactPersonID": obj.get("DeliverContactPersonID"),
            "InvoiceContactPersonID": obj.get("InvoiceContactPersonID"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "DeliverLocation_Descr": obj.get("DeliverLocation_Descr"),
            "DeliverLocation_Descr2": obj.get("DeliverLocation_Descr2"),
            "DeliverLocation_AIDC": obj.get("DeliverLocation_AIDC"),
            "DeliverLocation_IsStock": obj.get("DeliverLocation_IsStock"),
            "DeliverLocation_LocationOwner_ID": obj.get("DeliverLocation_LocationOwner_ID"),
            "DeliverLocation_LocationOwner_Name": obj.get("DeliverLocation_LocationOwner_Name"),
            "DeliverLocation_LocationOwner_Name2": obj.get("DeliverLocation_LocationOwner_Name2"),
            "DeliverLocation_LocationType_ID": obj.get("DeliverLocation_LocationType_ID"),
            "DeliverLocation_LocationType_Descr": obj.get("DeliverLocation_LocationType_Descr"),
            "DeliverLocation_LocationStatus_ID": obj.get("DeliverLocation_LocationStatus_ID"),
            "DeliverLocation_LocationStatus_Descr": obj.get("DeliverLocation_LocationStatus_Descr"),
            "WareHouseID": obj.get("WareHouseID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


