# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectType(BaseModel):
    """
    CentixAPIDTOReadObjectType
    """ # noqa: E501
    object_group_id: Optional[StrictInt] = Field(default=None, alias="ObjectGroupID")
    unique_object_id: Optional[StrictBool] = Field(default=None, alias="UniqueObjectID")
    is_measuring_device: Optional[StrictBool] = Field(default=None, alias="IsMeasuringDevice")
    measuring_device_kind: Optional[StrictInt] = Field(default=None, description="1 = MeterPosition, 2 = MeasuringDevice", alias="MeasuringDeviceKind")
    used_by_products: Optional[StrictBool] = Field(default=None, alias="UsedByProducts")
    lifespan: Optional[StrictInt] = Field(default=None, alias="Lifespan")
    lifespan_calculation_method: Optional[StrictInt] = Field(default=None, description="0 = None, 1 = ConstructionYear, 2 = CreateDate", alias="LifespanCalculationMethod")
    mi_validation_warning: Optional[StrictInt] = Field(default=None, alias="MIValidationWarning")
    endof_life_warning: Optional[StrictInt] = Field(default=None, alias="EndofLifeWarning")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ObjectGroupID", "UniqueObjectID", "IsMeasuringDevice", "MeasuringDeviceKind", "UsedByProducts", "Lifespan", "LifespanCalculationMethod", "MIValidationWarning", "EndofLifeWarning", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('measuring_device_kind')
    def measuring_device_kind_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2]):
            raise ValueError("must be one of enum values (1, 2)")
        return value

    @field_validator('lifespan_calculation_method')
    def lifespan_calculation_method_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2]):
            raise ValueError("must be one of enum values (0, 1, 2)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectType from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if object_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_group_id is None and "object_group_id" in self.model_fields_set:
            _dict['ObjectGroupID'] = None

        # set to None if measuring_device_kind (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device_kind is None and "measuring_device_kind" in self.model_fields_set:
            _dict['MeasuringDeviceKind'] = None

        # set to None if used_by_products (nullable) is None
        # and model_fields_set contains the field
        if self.used_by_products is None and "used_by_products" in self.model_fields_set:
            _dict['UsedByProducts'] = None

        # set to None if lifespan (nullable) is None
        # and model_fields_set contains the field
        if self.lifespan is None and "lifespan" in self.model_fields_set:
            _dict['Lifespan'] = None

        # set to None if mi_validation_warning (nullable) is None
        # and model_fields_set contains the field
        if self.mi_validation_warning is None and "mi_validation_warning" in self.model_fields_set:
            _dict['MIValidationWarning'] = None

        # set to None if endof_life_warning (nullable) is None
        # and model_fields_set contains the field
        if self.endof_life_warning is None and "endof_life_warning" in self.model_fields_set:
            _dict['EndofLifeWarning'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectType from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ObjectGroupID": obj.get("ObjectGroupID"),
            "UniqueObjectID": obj.get("UniqueObjectID"),
            "IsMeasuringDevice": obj.get("IsMeasuringDevice"),
            "MeasuringDeviceKind": obj.get("MeasuringDeviceKind"),
            "UsedByProducts": obj.get("UsedByProducts"),
            "Lifespan": obj.get("Lifespan"),
            "LifespanCalculationMethod": obj.get("LifespanCalculationMethod"),
            "MIValidationWarning": obj.get("MIValidationWarning"),
            "EndofLifeWarning": obj.get("EndofLifeWarning"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


