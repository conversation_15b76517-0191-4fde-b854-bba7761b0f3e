# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadMI(BaseModel):
    """
    CentixAPIDTOReadMI
    """ # noqa: E501
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    mi_date: Optional[datetime] = Field(default=None, alias="MIDate")
    inspection_relation_id: Optional[StrictInt] = Field(default=None, alias="InspectionRelationID")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    result: Optional[StrictInt] = Field(default=None, description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable", alias="Result")
    result_note: Optional[StrictStr] = Field(default=None, alias="ResultNote")
    result_date: Optional[datetime] = Field(default=None, alias="ResultDate")
    status: Optional[StrictInt] = Field(default=None, description="0 = Open, 1 = Finished, 2 = Waiting, 3 = ReOpened", alias="Status")
    inspection_person_id: Optional[StrictInt] = Field(default=None, alias="InspectionPersonID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Descr", "MIDate", "InspectionRelationID", "AdministrationID", "ProjectID", "CostCodeID", "InternalNote", "ExternalNote", "Result", "ResultNote", "ResultDate", "Status", "InspectionPersonID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('result')
    def result_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMI from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if mi_date (nullable) is None
        # and model_fields_set contains the field
        if self.mi_date is None and "mi_date" in self.model_fields_set:
            _dict['MIDate'] = None

        # set to None if inspection_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_relation_id is None and "inspection_relation_id" in self.model_fields_set:
            _dict['InspectionRelationID'] = None

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if result (nullable) is None
        # and model_fields_set contains the field
        if self.result is None and "result" in self.model_fields_set:
            _dict['Result'] = None

        # set to None if result_date (nullable) is None
        # and model_fields_set contains the field
        if self.result_date is None and "result_date" in self.model_fields_set:
            _dict['ResultDate'] = None

        # set to None if inspection_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_person_id is None and "inspection_person_id" in self.model_fields_set:
            _dict['InspectionPersonID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMI from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "MIDate": obj.get("MIDate"),
            "InspectionRelationID": obj.get("InspectionRelationID"),
            "AdministrationID": obj.get("AdministrationID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "InternalNote": obj.get("InternalNote"),
            "ExternalNote": obj.get("ExternalNote"),
            "Result": obj.get("Result"),
            "ResultNote": obj.get("ResultNote"),
            "ResultDate": obj.get("ResultDate"),
            "Status": obj.get("Status"),
            "InspectionPersonID": obj.get("InspectionPersonID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


