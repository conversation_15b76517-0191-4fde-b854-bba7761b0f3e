# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadJournalLine(BaseModel):
    """
    CentixAPIDTOReadJournalLine
    """ # noqa: E501
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Amount")
    book_date: Optional[datetime] = Field(default=None, alias="BookDate")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    day_book_id: Optional[StrictInt] = Field(default=None, alias="DayBookID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    document_number: Optional[StrictStr] = Field(default=None, alias="DocumentNumber")
    general_ledger_id: Optional[StrictInt] = Field(default=None, alias="GeneralLedgerID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="VatAmount")
    vat_id: Optional[StrictInt] = Field(default=None, alias="VatID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AdministrationID", "Amount", "BookDate", "CostCategoryID", "CostCodeID", "DayBookID", "Descr", "DocumentNumber", "GeneralLedgerID", "ProjectID", "RelationID", "VatAmount", "VatID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadJournalLine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if day_book_id (nullable) is None
        # and model_fields_set contains the field
        if self.day_book_id is None and "day_book_id" in self.model_fields_set:
            _dict['DayBookID'] = None

        # set to None if general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.general_ledger_id is None and "general_ledger_id" in self.model_fields_set:
            _dict['GeneralLedgerID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadJournalLine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AdministrationID": obj.get("AdministrationID"),
            "Amount": obj.get("Amount"),
            "BookDate": obj.get("BookDate"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "DayBookID": obj.get("DayBookID"),
            "Descr": obj.get("Descr"),
            "DocumentNumber": obj.get("DocumentNumber"),
            "GeneralLedgerID": obj.get("GeneralLedgerID"),
            "ProjectID": obj.get("ProjectID"),
            "RelationID": obj.get("RelationID"),
            "VatAmount": obj.get("VatAmount"),
            "VatID": obj.get("VatID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


