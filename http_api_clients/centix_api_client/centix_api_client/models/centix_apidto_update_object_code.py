# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateObjectCode(BaseModel):
    """
    CentixAPIDTOUpdateObjectCode
    """ # noqa: E501
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    consumed_at: Optional[datetime] = Field(default=None, alias="ConsumedAt")
    issued_to_relation_id: Optional[StrictInt] = Field(default=None, alias="IssuedToRelationID")
    issued_to_contact_person_id: Optional[StrictInt] = Field(default=None, alias="IssuedToContactPersonID")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["Descr", "ConsumedAt", "IssuedToRelationID", "IssuedToContactPersonID"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateObjectCode from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if consumed_at (nullable) is None
        # and model_fields_set contains the field
        if self.consumed_at is None and "consumed_at" in self.model_fields_set:
            _dict['ConsumedAt'] = None

        # set to None if issued_to_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.issued_to_relation_id is None and "issued_to_relation_id" in self.model_fields_set:
            _dict['IssuedToRelationID'] = None

        # set to None if issued_to_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.issued_to_contact_person_id is None and "issued_to_contact_person_id" in self.model_fields_set:
            _dict['IssuedToContactPersonID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateObjectCode from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "Descr": obj.get("Descr"),
            "ConsumedAt": obj.get("ConsumedAt"),
            "IssuedToRelationID": obj.get("IssuedToRelationID"),
            "IssuedToContactPersonID": obj.get("IssuedToContactPersonID")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


