# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadJournalQuery(BaseModel):
    """
    CentixAPIDTOReadJournalQuery
    """ # noqa: E501
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    type: Optional[StrictInt] = Field(default=None, description="0 = PUR, 1 = SAL, 2 = REV, 3 = DRA, 4 = STO, 5 = DEP", alias="Type")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    book_date: Optional[datetime] = Field(default=None, alias="BookDate")
    fiscal_year: Optional[StrictInt] = Field(default=None, alias="FiscalYear")
    fiscal_period: Optional[StrictInt] = Field(default=None, alias="FiscalPeriod")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    exchange_rate: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ExchangeRate")
    processed_at: Optional[datetime] = Field(default=None, alias="ProcessedAt")
    processed_by_person_id: Optional[StrictInt] = Field(default=None, alias="ProcessedByPersonID")
    document_number: Optional[StrictStr] = Field(default=None, alias="DocumentNumber")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    invoice_id: Optional[StrictInt] = Field(default=None, alias="InvoiceID")
    purchase_invoice_id: Optional[StrictInt] = Field(default=None, alias="PurchaseInvoiceID")
    purchase_order_id: Optional[StrictInt] = Field(default=None, alias="PurchaseOrderID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AdministrationID", "Type", "Descr", "BookDate", "FiscalYear", "FiscalPeriod", "CurrencyID", "ExchangeRate", "ProcessedAt", "ProcessedByPersonID", "DocumentNumber", "RelationID", "InvoiceID", "PurchaseInvoiceID", "PurchaseOrderID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 5]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 5)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadJournalQuery from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if book_date (nullable) is None
        # and model_fields_set contains the field
        if self.book_date is None and "book_date" in self.model_fields_set:
            _dict['BookDate'] = None

        # set to None if fiscal_year (nullable) is None
        # and model_fields_set contains the field
        if self.fiscal_year is None and "fiscal_year" in self.model_fields_set:
            _dict['FiscalYear'] = None

        # set to None if fiscal_period (nullable) is None
        # and model_fields_set contains the field
        if self.fiscal_period is None and "fiscal_period" in self.model_fields_set:
            _dict['FiscalPeriod'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if exchange_rate (nullable) is None
        # and model_fields_set contains the field
        if self.exchange_rate is None and "exchange_rate" in self.model_fields_set:
            _dict['ExchangeRate'] = None

        # set to None if processed_at (nullable) is None
        # and model_fields_set contains the field
        if self.processed_at is None and "processed_at" in self.model_fields_set:
            _dict['ProcessedAt'] = None

        # set to None if processed_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.processed_by_person_id is None and "processed_by_person_id" in self.model_fields_set:
            _dict['ProcessedByPersonID'] = None

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if invoice_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_id is None and "invoice_id" in self.model_fields_set:
            _dict['InvoiceID'] = None

        # set to None if purchase_invoice_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_invoice_id is None and "purchase_invoice_id" in self.model_fields_set:
            _dict['PurchaseInvoiceID'] = None

        # set to None if purchase_order_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_order_id is None and "purchase_order_id" in self.model_fields_set:
            _dict['PurchaseOrderID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadJournalQuery from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AdministrationID": obj.get("AdministrationID"),
            "Type": obj.get("Type"),
            "Descr": obj.get("Descr"),
            "BookDate": obj.get("BookDate"),
            "FiscalYear": obj.get("FiscalYear"),
            "FiscalPeriod": obj.get("FiscalPeriod"),
            "CurrencyID": obj.get("CurrencyID"),
            "ExchangeRate": obj.get("ExchangeRate"),
            "ProcessedAt": obj.get("ProcessedAt"),
            "ProcessedByPersonID": obj.get("ProcessedByPersonID"),
            "DocumentNumber": obj.get("DocumentNumber"),
            "RelationID": obj.get("RelationID"),
            "InvoiceID": obj.get("InvoiceID"),
            "PurchaseInvoiceID": obj.get("PurchaseInvoiceID"),
            "PurchaseOrderID": obj.get("PurchaseOrderID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


