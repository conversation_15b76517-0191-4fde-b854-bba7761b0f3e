# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadInvoice(BaseModel):
    """
    CentixAPIDTOReadInvoice
    """ # noqa: E501
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    blocked: Optional[StrictBool] = Field(default=None, alias="Blocked")
    blocked_for_merge: Optional[StrictBool] = Field(default=None, alias="BlockedForMerge")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    deliver_additional_address_info1: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo1")
    deliver_additional_address_info2: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo2")
    deliver_address: Optional[StrictStr] = Field(default=None, alias="DeliverAddress")
    deliver_city: Optional[StrictStr] = Field(default=None, alias="DeliverCity")
    deliver_contact_person_id: Optional[StrictInt] = Field(default=None, alias="DeliverContactPersonID")
    deliver_country_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountryID")
    deliver_county_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountyID")
    deliver_date: Optional[datetime] = Field(default=None, alias="DeliverDate")
    deliver_house_no: Optional[StrictStr] = Field(default=None, alias="DeliverHouseNo")
    deliver_house_no_add: Optional[StrictStr] = Field(default=None, alias="DeliverHouseNoAdd")
    deliver_location_id: Optional[StrictInt] = Field(default=None, alias="DeliverLocationID")
    deliver_person_descr: Optional[StrictStr] = Field(default=None, alias="DeliverPersonDescr")
    deliver_postalcode: Optional[StrictStr] = Field(default=None, alias="DeliverPostalcode")
    deliver_region_id: Optional[StrictInt] = Field(default=None, alias="DeliverRegionID")
    deliver_relation_descr: Optional[StrictStr] = Field(default=None, alias="DeliverRelationDescr")
    deliver_relation_id: Optional[StrictInt] = Field(default=None, alias="DeliverRelationID")
    deliver_state_id: Optional[StrictInt] = Field(default=None, alias="DeliverStateID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    exchange_rate: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ExchangeRate")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    invoice_contact_person_id: Optional[StrictInt] = Field(default=None, alias="InvoiceContactPersonID")
    invoice_date: Optional[datetime] = Field(default=None, alias="InvoiceDate")
    invoice_period_unit: Optional[StrictInt] = Field(default=None, alias="InvoicePeriodUnit")
    invoice_reference_required: Optional[StrictBool] = Field(default=None, alias="InvoiceReferenceRequired")
    invoice_relation_id: Optional[StrictInt] = Field(default=None, alias="InvoiceRelationID")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    location_rental_price_list_id: Optional[StrictInt] = Field(default=None, alias="LocationRentalPriceListID")
    object_rental_price_list_id: Optional[StrictInt] = Field(default=None, alias="ObjectRentalPriceListID")
    order_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="OrderCosts")
    order_costs_vat_id: Optional[StrictInt] = Field(default=None, alias="OrderCostsVatID")
    order_date: Optional[datetime] = Field(default=None, alias="OrderDate")
    payment_condition_id: Optional[StrictInt] = Field(default=None, alias="PaymentConditionID")
    payment_method_id: Optional[StrictInt] = Field(default=None, alias="PaymentMethodID")
    payments_term: Optional[StrictInt] = Field(default=None, alias="PaymentsTerm")
    periodic_invoice: Optional[StrictBool] = Field(default=None, alias="PeriodicInvoice")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    sale_price_list_id: Optional[StrictInt] = Field(default=None, alias="SalePriceListID")
    selection_code: Optional[StrictStr] = Field(default=None, alias="SelectionCode")
    status: Optional[StrictInt] = Field(default=None, description="0 = New, 1 = Completed, 2 = ConceptRequest, 3 = Voided", alias="Status")
    sub_total: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotal")
    total: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Total")
    transport_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="TransportCosts")
    transport_costs_vat_id: Optional[StrictInt] = Field(default=None, alias="TransportCostsVatID")
    transport_method_id: Optional[StrictInt] = Field(default=None, alias="TransportMethodID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AdministrationID", "AssignedToPersonID", "Blocked", "BlockedForMerge", "CostCategoryID", "CostCodeID", "CurrencyID", "DeliverAdditionalAddressInfo1", "DeliverAdditionalAddressInfo2", "DeliverAddress", "DeliverCity", "DeliverContactPersonID", "DeliverCountryID", "DeliverCountyID", "DeliverDate", "DeliverHouseNo", "DeliverHouseNoAdd", "DeliverLocationID", "DeliverPersonDescr", "DeliverPostalcode", "DeliverRegionID", "DeliverRelationDescr", "DeliverRelationID", "DeliverStateID", "Descr", "ExchangeRate", "ExternalNote", "ID", "InternalNote", "InvoiceContactPersonID", "InvoiceDate", "InvoicePeriodUnit", "InvoiceReferenceRequired", "InvoiceRelationID", "LanguageID", "LocationRentalPriceListID", "ObjectRentalPriceListID", "OrderCosts", "OrderCostsVatID", "OrderDate", "PaymentConditionID", "PaymentMethodID", "PaymentsTerm", "PeriodicInvoice", "ProjectID", "Reference", "SalePriceListID", "SelectionCode", "Status", "SubTotal", "Total", "TransportCosts", "TransportCostsVatID", "TransportMethodID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadInvoice from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if deliver_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_contact_person_id is None and "deliver_contact_person_id" in self.model_fields_set:
            _dict['DeliverContactPersonID'] = None

        # set to None if deliver_country_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_country_id is None and "deliver_country_id" in self.model_fields_set:
            _dict['DeliverCountryID'] = None

        # set to None if deliver_county_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_county_id is None and "deliver_county_id" in self.model_fields_set:
            _dict['DeliverCountyID'] = None

        # set to None if deliver_date (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_date is None and "deliver_date" in self.model_fields_set:
            _dict['DeliverDate'] = None

        # set to None if deliver_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_location_id is None and "deliver_location_id" in self.model_fields_set:
            _dict['DeliverLocationID'] = None

        # set to None if deliver_region_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_region_id is None and "deliver_region_id" in self.model_fields_set:
            _dict['DeliverRegionID'] = None

        # set to None if deliver_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_relation_id is None and "deliver_relation_id" in self.model_fields_set:
            _dict['DeliverRelationID'] = None

        # set to None if deliver_state_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_state_id is None and "deliver_state_id" in self.model_fields_set:
            _dict['DeliverStateID'] = None

        # set to None if invoice_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_contact_person_id is None and "invoice_contact_person_id" in self.model_fields_set:
            _dict['InvoiceContactPersonID'] = None

        # set to None if invoice_date (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_date is None and "invoice_date" in self.model_fields_set:
            _dict['InvoiceDate'] = None

        # set to None if invoice_period_unit (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_period_unit is None and "invoice_period_unit" in self.model_fields_set:
            _dict['InvoicePeriodUnit'] = None

        # set to None if invoice_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_relation_id is None and "invoice_relation_id" in self.model_fields_set:
            _dict['InvoiceRelationID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if location_rental_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_rental_price_list_id is None and "location_rental_price_list_id" in self.model_fields_set:
            _dict['LocationRentalPriceListID'] = None

        # set to None if object_rental_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_rental_price_list_id is None and "object_rental_price_list_id" in self.model_fields_set:
            _dict['ObjectRentalPriceListID'] = None

        # set to None if order_costs (nullable) is None
        # and model_fields_set contains the field
        if self.order_costs is None and "order_costs" in self.model_fields_set:
            _dict['OrderCosts'] = None

        # set to None if order_costs_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.order_costs_vat_id is None and "order_costs_vat_id" in self.model_fields_set:
            _dict['OrderCostsVatID'] = None

        # set to None if order_date (nullable) is None
        # and model_fields_set contains the field
        if self.order_date is None and "order_date" in self.model_fields_set:
            _dict['OrderDate'] = None

        # set to None if payment_condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.payment_condition_id is None and "payment_condition_id" in self.model_fields_set:
            _dict['PaymentConditionID'] = None

        # set to None if payment_method_id (nullable) is None
        # and model_fields_set contains the field
        if self.payment_method_id is None and "payment_method_id" in self.model_fields_set:
            _dict['PaymentMethodID'] = None

        # set to None if payments_term (nullable) is None
        # and model_fields_set contains the field
        if self.payments_term is None and "payments_term" in self.model_fields_set:
            _dict['PaymentsTerm'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if sale_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.sale_price_list_id is None and "sale_price_list_id" in self.model_fields_set:
            _dict['SalePriceListID'] = None

        # set to None if transport_costs (nullable) is None
        # and model_fields_set contains the field
        if self.transport_costs is None and "transport_costs" in self.model_fields_set:
            _dict['TransportCosts'] = None

        # set to None if transport_costs_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.transport_costs_vat_id is None and "transport_costs_vat_id" in self.model_fields_set:
            _dict['TransportCostsVatID'] = None

        # set to None if transport_method_id (nullable) is None
        # and model_fields_set contains the field
        if self.transport_method_id is None and "transport_method_id" in self.model_fields_set:
            _dict['TransportMethodID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadInvoice from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AdministrationID": obj.get("AdministrationID"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "Blocked": obj.get("Blocked"),
            "BlockedForMerge": obj.get("BlockedForMerge"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "CurrencyID": obj.get("CurrencyID"),
            "DeliverAdditionalAddressInfo1": obj.get("DeliverAdditionalAddressInfo1"),
            "DeliverAdditionalAddressInfo2": obj.get("DeliverAdditionalAddressInfo2"),
            "DeliverAddress": obj.get("DeliverAddress"),
            "DeliverCity": obj.get("DeliverCity"),
            "DeliverContactPersonID": obj.get("DeliverContactPersonID"),
            "DeliverCountryID": obj.get("DeliverCountryID"),
            "DeliverCountyID": obj.get("DeliverCountyID"),
            "DeliverDate": obj.get("DeliverDate"),
            "DeliverHouseNo": obj.get("DeliverHouseNo"),
            "DeliverHouseNoAdd": obj.get("DeliverHouseNoAdd"),
            "DeliverLocationID": obj.get("DeliverLocationID"),
            "DeliverPersonDescr": obj.get("DeliverPersonDescr"),
            "DeliverPostalcode": obj.get("DeliverPostalcode"),
            "DeliverRegionID": obj.get("DeliverRegionID"),
            "DeliverRelationDescr": obj.get("DeliverRelationDescr"),
            "DeliverRelationID": obj.get("DeliverRelationID"),
            "DeliverStateID": obj.get("DeliverStateID"),
            "Descr": obj.get("Descr"),
            "ExchangeRate": obj.get("ExchangeRate"),
            "ExternalNote": obj.get("ExternalNote"),
            "ID": obj.get("ID"),
            "InternalNote": obj.get("InternalNote"),
            "InvoiceContactPersonID": obj.get("InvoiceContactPersonID"),
            "InvoiceDate": obj.get("InvoiceDate"),
            "InvoicePeriodUnit": obj.get("InvoicePeriodUnit"),
            "InvoiceReferenceRequired": obj.get("InvoiceReferenceRequired"),
            "InvoiceRelationID": obj.get("InvoiceRelationID"),
            "LanguageID": obj.get("LanguageID"),
            "LocationRentalPriceListID": obj.get("LocationRentalPriceListID"),
            "ObjectRentalPriceListID": obj.get("ObjectRentalPriceListID"),
            "OrderCosts": obj.get("OrderCosts"),
            "OrderCostsVatID": obj.get("OrderCostsVatID"),
            "OrderDate": obj.get("OrderDate"),
            "PaymentConditionID": obj.get("PaymentConditionID"),
            "PaymentMethodID": obj.get("PaymentMethodID"),
            "PaymentsTerm": obj.get("PaymentsTerm"),
            "PeriodicInvoice": obj.get("PeriodicInvoice"),
            "ProjectID": obj.get("ProjectID"),
            "Reference": obj.get("Reference"),
            "SalePriceListID": obj.get("SalePriceListID"),
            "SelectionCode": obj.get("SelectionCode"),
            "Status": obj.get("Status"),
            "SubTotal": obj.get("SubTotal"),
            "Total": obj.get("Total"),
            "TransportCosts": obj.get("TransportCosts"),
            "TransportCostsVatID": obj.get("TransportCostsVatID"),
            "TransportMethodID": obj.get("TransportMethodID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


