# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadOrderLineRentalLog(BaseModel):
    """
    CentixAPIDTOReadOrderLineRentalLog
    """ # noqa: E501
    order_line_id: Optional[StrictInt] = Field(default=None, alias="OrderLineID")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    pack_list_item_object_id: Optional[StrictInt] = Field(default=None, alias="PackListItemObjectID")
    delivery_date: Optional[datetime] = Field(default=None, alias="DeliveryDate")
    return_list_item_id: Optional[StrictInt] = Field(default=None, alias="ReturnListItemID")
    return_date: Optional[datetime] = Field(default=None, alias="ReturnDate")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    invoice_line_id: Optional[StrictInt] = Field(default=None, alias="InvoiceLineID")
    invoiced_till: Optional[datetime] = Field(default=None, alias="InvoicedTill")
    invoiced: Optional[StrictBool] = Field(default=None, alias="Invoiced")
    meter_reading_initial_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingInitialValue")
    meter_reading_initial_date: Optional[datetime] = Field(default=None, alias="MeterReadingInitialDate")
    meter_reading_final_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeterReadingFinalValue")
    meter_reading_final_date: Optional[datetime] = Field(default=None, alias="MeterReadingFinalDate")
    suppress_invoice: Optional[StrictBool] = Field(default=None, alias="SuppressInvoice")
    suppress_invoice_date: Optional[datetime] = Field(default=None, alias="SuppressInvoiceDate")
    suppress_invoice_by: Optional[StrictStr] = Field(default=None, alias="SuppressInvoiceBy")
    suppress_invoice_comment: Optional[StrictStr] = Field(default=None, alias="SuppressInvoiceComment")
    object_id: Optional[StrictStr] = Field(default=None, alias="Object_ID")
    object_descr: Optional[StrictStr] = Field(default=None, alias="Object_Descr")
    is_repaired: Optional[StrictBool] = Field(default=None, alias="IsRepaired")
    repair_product_id: Optional[StrictInt] = Field(default=None, alias="RepairProductID")
    is_cleaned: Optional[StrictBool] = Field(default=None, alias="IsCleaned")
    cleaning_product_id: Optional[StrictInt] = Field(default=None, alias="CleaningProductID")
    is_missing: Optional[StrictBool] = Field(default=None, alias="IsMissing")
    missing_product_id: Optional[StrictInt] = Field(default=None, alias="MissingProductID")
    is_claimed: Optional[StrictBool] = Field(default=None, alias="IsClaimed")
    is_beyond_repair: Optional[StrictBool] = Field(default=None, alias="IsBeyondRepair")
    beyond_repair_product_id: Optional[StrictInt] = Field(default=None, alias="BeyondRepairProductID")
    cleaning_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="CleaningCosts")
    repair_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RepairCosts")
    missing_replacement_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MissingReplacementCosts")
    beyond_repair_replacement_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="BeyondRepairReplacementCosts")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["OrderLineID", "ObjectID", "PackListItemObjectID", "DeliveryDate", "ReturnListItemID", "ReturnDate", "Quantity", "InvoiceLineID", "InvoicedTill", "Invoiced", "MeterReadingInitialValue", "MeterReadingInitialDate", "MeterReadingFinalValue", "MeterReadingFinalDate", "SuppressInvoice", "SuppressInvoiceDate", "SuppressInvoiceBy", "SuppressInvoiceComment", "Object_ID", "Object_Descr", "IsRepaired", "RepairProductID", "IsCleaned", "CleaningProductID", "IsMissing", "MissingProductID", "IsClaimed", "IsBeyondRepair", "BeyondRepairProductID", "CleaningCosts", "RepairCosts", "MissingReplacementCosts", "BeyondRepairReplacementCosts", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrderLineRentalLog from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if pack_list_item_object_id (nullable) is None
        # and model_fields_set contains the field
        if self.pack_list_item_object_id is None and "pack_list_item_object_id" in self.model_fields_set:
            _dict['PackListItemObjectID'] = None

        # set to None if delivery_date (nullable) is None
        # and model_fields_set contains the field
        if self.delivery_date is None and "delivery_date" in self.model_fields_set:
            _dict['DeliveryDate'] = None

        # set to None if return_list_item_id (nullable) is None
        # and model_fields_set contains the field
        if self.return_list_item_id is None and "return_list_item_id" in self.model_fields_set:
            _dict['ReturnListItemID'] = None

        # set to None if return_date (nullable) is None
        # and model_fields_set contains the field
        if self.return_date is None and "return_date" in self.model_fields_set:
            _dict['ReturnDate'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if invoice_line_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_line_id is None and "invoice_line_id" in self.model_fields_set:
            _dict['InvoiceLineID'] = None

        # set to None if invoiced_till (nullable) is None
        # and model_fields_set contains the field
        if self.invoiced_till is None and "invoiced_till" in self.model_fields_set:
            _dict['InvoicedTill'] = None

        # set to None if invoiced (nullable) is None
        # and model_fields_set contains the field
        if self.invoiced is None and "invoiced" in self.model_fields_set:
            _dict['Invoiced'] = None

        # set to None if meter_reading_initial_value (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_initial_value is None and "meter_reading_initial_value" in self.model_fields_set:
            _dict['MeterReadingInitialValue'] = None

        # set to None if meter_reading_initial_date (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_initial_date is None and "meter_reading_initial_date" in self.model_fields_set:
            _dict['MeterReadingInitialDate'] = None

        # set to None if meter_reading_final_value (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_final_value is None and "meter_reading_final_value" in self.model_fields_set:
            _dict['MeterReadingFinalValue'] = None

        # set to None if meter_reading_final_date (nullable) is None
        # and model_fields_set contains the field
        if self.meter_reading_final_date is None and "meter_reading_final_date" in self.model_fields_set:
            _dict['MeterReadingFinalDate'] = None

        # set to None if suppress_invoice (nullable) is None
        # and model_fields_set contains the field
        if self.suppress_invoice is None and "suppress_invoice" in self.model_fields_set:
            _dict['SuppressInvoice'] = None

        # set to None if suppress_invoice_date (nullable) is None
        # and model_fields_set contains the field
        if self.suppress_invoice_date is None and "suppress_invoice_date" in self.model_fields_set:
            _dict['SuppressInvoiceDate'] = None

        # set to None if repair_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.repair_product_id is None and "repair_product_id" in self.model_fields_set:
            _dict['RepairProductID'] = None

        # set to None if cleaning_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.cleaning_product_id is None and "cleaning_product_id" in self.model_fields_set:
            _dict['CleaningProductID'] = None

        # set to None if missing_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.missing_product_id is None and "missing_product_id" in self.model_fields_set:
            _dict['MissingProductID'] = None

        # set to None if beyond_repair_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.beyond_repair_product_id is None and "beyond_repair_product_id" in self.model_fields_set:
            _dict['BeyondRepairProductID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrderLineRentalLog from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "OrderLineID": obj.get("OrderLineID"),
            "ObjectID": obj.get("ObjectID"),
            "PackListItemObjectID": obj.get("PackListItemObjectID"),
            "DeliveryDate": obj.get("DeliveryDate"),
            "ReturnListItemID": obj.get("ReturnListItemID"),
            "ReturnDate": obj.get("ReturnDate"),
            "Quantity": obj.get("Quantity"),
            "InvoiceLineID": obj.get("InvoiceLineID"),
            "InvoicedTill": obj.get("InvoicedTill"),
            "Invoiced": obj.get("Invoiced"),
            "MeterReadingInitialValue": obj.get("MeterReadingInitialValue"),
            "MeterReadingInitialDate": obj.get("MeterReadingInitialDate"),
            "MeterReadingFinalValue": obj.get("MeterReadingFinalValue"),
            "MeterReadingFinalDate": obj.get("MeterReadingFinalDate"),
            "SuppressInvoice": obj.get("SuppressInvoice"),
            "SuppressInvoiceDate": obj.get("SuppressInvoiceDate"),
            "SuppressInvoiceBy": obj.get("SuppressInvoiceBy"),
            "SuppressInvoiceComment": obj.get("SuppressInvoiceComment"),
            "Object_ID": obj.get("Object_ID"),
            "Object_Descr": obj.get("Object_Descr"),
            "IsRepaired": obj.get("IsRepaired"),
            "RepairProductID": obj.get("RepairProductID"),
            "IsCleaned": obj.get("IsCleaned"),
            "CleaningProductID": obj.get("CleaningProductID"),
            "IsMissing": obj.get("IsMissing"),
            "MissingProductID": obj.get("MissingProductID"),
            "IsClaimed": obj.get("IsClaimed"),
            "IsBeyondRepair": obj.get("IsBeyondRepair"),
            "BeyondRepairProductID": obj.get("BeyondRepairProductID"),
            "CleaningCosts": obj.get("CleaningCosts"),
            "RepairCosts": obj.get("RepairCosts"),
            "MissingReplacementCosts": obj.get("MissingReplacementCosts"),
            "BeyondRepairReplacementCosts": obj.get("BeyondRepairReplacementCosts"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


