# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadWorkingTicket(BaseModel):
    """
    CentixAPIDTOReadWorkingTicket
    """ # noqa: E501
    id: Annotated[str, Field(min_length=0, strict=True, max_length=20)] = Field(alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    note: Optional[StrictStr] = Field(default=None, alias="Note")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    administration_id: Optional[StrictInt] = Field(alias="AdministrationID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    street: Optional[StrictStr] = Field(default=None, alias="Street")
    house_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNo")
    house_no_add: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNoAdd")
    postalcode: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=12)]] = Field(default=None, alias="Postalcode")
    city: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="City")
    country_id: Optional[StrictInt] = Field(default=None, alias="CountryID")
    state_id: Optional[StrictInt] = Field(default=None, alias="StateID")
    region_id: Optional[StrictInt] = Field(default=None, alias="RegionID")
    additional_address_info1: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo1")
    additional_address_info2: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo2")
    county_id: Optional[StrictInt] = Field(default=None, alias="CountyID")
    no_relation_signature_reason_id: Optional[StrictInt] = Field(default=None, alias="NoRelationSignatureReasonID")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    approved_by_person_id: Optional[StrictInt] = Field(default=None, alias="ApprovedByPersonID")
    approved_at: Optional[datetime] = Field(default=None, alias="ApprovedAt")
    processed_by_person_id: Optional[StrictInt] = Field(default=None, alias="ProcessedByPersonID")
    processed_at: Optional[datetime] = Field(default=None, alias="ProcessedAt")
    contact_person_id: Optional[StrictInt] = Field(default=None, alias="ContactPersonID")
    sale_price_list_id: Optional[StrictInt] = Field(default=None, alias="SalePriceListID")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    reference: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Reference")
    short_descr: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="ShortDescr")
    start_at: Optional[datetime] = Field(default=None, alias="StartAt")
    endat: Optional[datetime] = Field(default=None, alias="Endat")
    is_fixed_time_appointment: Optional[StrictBool] = Field(default=None, alias="IsFixedTimeAppointment")
    plan_block_id: Optional[StrictInt] = Field(default=None, alias="PlanBlockID")
    status_id: Optional[StrictInt] = Field(alias="StatusID")
    transport_type: Optional[StrictInt] = Field(default=None, description="0 = None, 1 = Car, 2 = CarWithTrailer, 3 = SmallVan, 4 = Van, 5 = Truck", alias="TransportType")
    travel_time: Optional[datetime] = Field(default=None, alias="TravelTime")
    related_working_ticket_id: Optional[StrictInt] = Field(default=None, alias="RelatedWorkingTicketID")
    location_id: Optional[StrictInt] = Field(default=None, alias="LocationID")
    assigned_to_team_id: Optional[StrictInt] = Field(default=None, alias="AssignedToTeamID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Descr", "Note", "RelationID", "AdministrationID", "ProjectID", "CostCodeID", "Street", "HouseNo", "HouseNoAdd", "Postalcode", "City", "CountryID", "StateID", "RegionID", "AdditionalAddressInfo1", "AdditionalAddressInfo2", "CountyID", "NoRelationSignatureReasonID", "AssignedToPersonID", "ApprovedByPersonID", "ApprovedAt", "ProcessedByPersonID", "ProcessedAt", "ContactPersonID", "SalePriceListID", "CostCategoryID", "Reference", "ShortDescr", "StartAt", "Endat", "IsFixedTimeAppointment", "PlanBlockID", "StatusID", "TransportType", "TravelTime", "RelatedWorkingTicketID", "LocationID", "AssignedToTeamID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('transport_type')
    def transport_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 5]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 5)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWorkingTicket from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if country_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_id is None and "country_id" in self.model_fields_set:
            _dict['CountryID'] = None

        # set to None if state_id (nullable) is None
        # and model_fields_set contains the field
        if self.state_id is None and "state_id" in self.model_fields_set:
            _dict['StateID'] = None

        # set to None if region_id (nullable) is None
        # and model_fields_set contains the field
        if self.region_id is None and "region_id" in self.model_fields_set:
            _dict['RegionID'] = None

        # set to None if county_id (nullable) is None
        # and model_fields_set contains the field
        if self.county_id is None and "county_id" in self.model_fields_set:
            _dict['CountyID'] = None

        # set to None if no_relation_signature_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.no_relation_signature_reason_id is None and "no_relation_signature_reason_id" in self.model_fields_set:
            _dict['NoRelationSignatureReasonID'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        # set to None if approved_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.approved_by_person_id is None and "approved_by_person_id" in self.model_fields_set:
            _dict['ApprovedByPersonID'] = None

        # set to None if approved_at (nullable) is None
        # and model_fields_set contains the field
        if self.approved_at is None and "approved_at" in self.model_fields_set:
            _dict['ApprovedAt'] = None

        # set to None if processed_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.processed_by_person_id is None and "processed_by_person_id" in self.model_fields_set:
            _dict['ProcessedByPersonID'] = None

        # set to None if processed_at (nullable) is None
        # and model_fields_set contains the field
        if self.processed_at is None and "processed_at" in self.model_fields_set:
            _dict['ProcessedAt'] = None

        # set to None if contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.contact_person_id is None and "contact_person_id" in self.model_fields_set:
            _dict['ContactPersonID'] = None

        # set to None if sale_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.sale_price_list_id is None and "sale_price_list_id" in self.model_fields_set:
            _dict['SalePriceListID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if start_at (nullable) is None
        # and model_fields_set contains the field
        if self.start_at is None and "start_at" in self.model_fields_set:
            _dict['StartAt'] = None

        # set to None if endat (nullable) is None
        # and model_fields_set contains the field
        if self.endat is None and "endat" in self.model_fields_set:
            _dict['Endat'] = None

        # set to None if is_fixed_time_appointment (nullable) is None
        # and model_fields_set contains the field
        if self.is_fixed_time_appointment is None and "is_fixed_time_appointment" in self.model_fields_set:
            _dict['IsFixedTimeAppointment'] = None

        # set to None if plan_block_id (nullable) is None
        # and model_fields_set contains the field
        if self.plan_block_id is None and "plan_block_id" in self.model_fields_set:
            _dict['PlanBlockID'] = None

        # set to None if status_id (nullable) is None
        # and model_fields_set contains the field
        if self.status_id is None and "status_id" in self.model_fields_set:
            _dict['StatusID'] = None

        # set to None if travel_time (nullable) is None
        # and model_fields_set contains the field
        if self.travel_time is None and "travel_time" in self.model_fields_set:
            _dict['TravelTime'] = None

        # set to None if related_working_ticket_id (nullable) is None
        # and model_fields_set contains the field
        if self.related_working_ticket_id is None and "related_working_ticket_id" in self.model_fields_set:
            _dict['RelatedWorkingTicketID'] = None

        # set to None if location_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_id is None and "location_id" in self.model_fields_set:
            _dict['LocationID'] = None

        # set to None if assigned_to_team_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_team_id is None and "assigned_to_team_id" in self.model_fields_set:
            _dict['AssignedToTeamID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWorkingTicket from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "Note": obj.get("Note"),
            "RelationID": obj.get("RelationID"),
            "AdministrationID": obj.get("AdministrationID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "Street": obj.get("Street"),
            "HouseNo": obj.get("HouseNo"),
            "HouseNoAdd": obj.get("HouseNoAdd"),
            "Postalcode": obj.get("Postalcode"),
            "City": obj.get("City"),
            "CountryID": obj.get("CountryID"),
            "StateID": obj.get("StateID"),
            "RegionID": obj.get("RegionID"),
            "AdditionalAddressInfo1": obj.get("AdditionalAddressInfo1"),
            "AdditionalAddressInfo2": obj.get("AdditionalAddressInfo2"),
            "CountyID": obj.get("CountyID"),
            "NoRelationSignatureReasonID": obj.get("NoRelationSignatureReasonID"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "ApprovedByPersonID": obj.get("ApprovedByPersonID"),
            "ApprovedAt": obj.get("ApprovedAt"),
            "ProcessedByPersonID": obj.get("ProcessedByPersonID"),
            "ProcessedAt": obj.get("ProcessedAt"),
            "ContactPersonID": obj.get("ContactPersonID"),
            "SalePriceListID": obj.get("SalePriceListID"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "Reference": obj.get("Reference"),
            "ShortDescr": obj.get("ShortDescr"),
            "StartAt": obj.get("StartAt"),
            "Endat": obj.get("Endat"),
            "IsFixedTimeAppointment": obj.get("IsFixedTimeAppointment"),
            "PlanBlockID": obj.get("PlanBlockID"),
            "StatusID": obj.get("StatusID"),
            "TransportType": obj.get("TransportType"),
            "TravelTime": obj.get("TravelTime"),
            "RelatedWorkingTicketID": obj.get("RelatedWorkingTicketID"),
            "LocationID": obj.get("LocationID"),
            "AssignedToTeamID": obj.get("AssignedToTeamID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


