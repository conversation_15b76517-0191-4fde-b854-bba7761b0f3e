# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectMISchedule(BaseModel):
    """
    CentixAPIDTOReadObjectMISchedule
    """ # noqa: E501
    auto_create: Optional[StrictBool] = Field(default=None, alias="AutoCreate")
    auto_create_offset: Optional[StrictInt] = Field(default=None, alias="AutoCreateOffset")
    auto_create_offset_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="AutoCreateOffsetUnit")
    automated_scheduling: Optional[StrictBool] = Field(default=None, alias="AutomatedScheduling")
    contract_id: Optional[StrictInt] = Field(default=None, alias="ContractID")
    enable_interval_interrupt: Optional[StrictBool] = Field(default=None, alias="EnableIntervalInterrupt")
    end_at: Optional[datetime] = Field(default=None, alias="EndAt")
    in_active: Optional[StrictBool] = Field(default=None, alias="InActive")
    inspection_person_id: Optional[StrictInt] = Field(default=None, alias="InspectionPersonID")
    inspection_relation_id: Optional[StrictInt] = Field(default=None, alias="InspectionRelationID")
    last_next_run: Optional[datetime] = Field(default=None, alias="LastNextRun")
    last_result: Optional[StrictInt] = Field(default=None, alias="LastResult")
    last_run: Optional[datetime] = Field(default=None, alias="LastRun")
    last_run_measuring_device1_measurement_function_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="LastRunMeasuringDevice1MeasurementFunctionValue")
    last_successful_run: Optional[datetime] = Field(default=None, alias="LastSuccessfulRun")
    measuring_device1_measurement_function_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1MeasurementFunctionID")
    measuring_device1_measurement_function_value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="MeasuringDevice1MeasurementFunctionValue")
    measuring_device1_object_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1ObjectID")
    measuring_device1_object_measurement_schedule_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1ObjectMeasurementScheduleID")
    mi_interval_count: Optional[StrictInt] = Field(default=None, alias="MIIntervalCount")
    mi_interval_repeat: Optional[StrictBool] = Field(default=None, alias="MIIntervalRepeat")
    mi_interval_repeat_count: Optional[StrictInt] = Field(default=None, alias="MIIntervalRepeatCount")
    mi_interval_skip_count: Optional[StrictInt] = Field(default=None, alias="MIIntervalSkipCount")
    mi_plan_id: Optional[StrictInt] = Field(default=None, alias="MIPlanID")
    mi_plan_sequence_id: Optional[StrictInt] = Field(default=None, alias="MIPlanSequenceID")
    mi_validation_expires: Optional[StrictBool] = Field(default=None, alias="MIValidationExpires")
    mi_validation_time_schedule_id: Optional[StrictInt] = Field(default=None, alias="MIValidationTimeScheduleID")
    mi_valid_until_date: Optional[datetime] = Field(default=None, alias="MIValidUntilDate")
    mi_valid_until_date_calculation_method: Optional[StrictInt] = Field(default=None, description="0 = Default, 1 = EndOfMonth", alias="MIValidUntilDateCalculationMethod")
    mi_work_flow_setting_id: Optional[StrictInt] = Field(default=None, alias="MIWorkFlowSettingID")
    next_run: Optional[datetime] = Field(default=None, alias="NextRun")
    next_run_calculation_method: Optional[StrictInt] = Field(default=None, description="0 = OnLastSuccessfulRun, 1 = OnLastRun", alias="NextRunCalculationMethod")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    repeat_meter_position_schedule: Optional[StrictBool] = Field(default=None, alias="RepeatMeterPositionSchedule")
    start_at: Optional[datetime] = Field(default=None, alias="StartAt")
    time_schedule_id: Optional[StrictInt] = Field(default=None, alias="TimeScheduleID")
    use_contract_dates: Optional[StrictBool] = Field(default=None, alias="UseContractDates")
    use_contract_visit_count: Optional[StrictBool] = Field(default=None, alias="UseContractVisitCount")
    visit_count: Optional[StrictInt] = Field(default=None, alias="VisitCount")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AutoCreate", "AutoCreateOffset", "AutoCreateOffsetUnit", "AutomatedScheduling", "ContractID", "EnableIntervalInterrupt", "EndAt", "InActive", "InspectionPersonID", "InspectionRelationID", "LastNextRun", "LastResult", "LastRun", "LastRunMeasuringDevice1MeasurementFunctionValue", "LastSuccessfulRun", "MeasuringDevice1MeasurementFunctionID", "MeasuringDevice1MeasurementFunctionValue", "MeasuringDevice1ObjectID", "MeasuringDevice1ObjectMeasurementScheduleID", "MIIntervalCount", "MIIntervalRepeat", "MIIntervalRepeatCount", "MIIntervalSkipCount", "MIPlanID", "MIPlanSequenceID", "MIValidationExpires", "MIValidationTimeScheduleID", "MIValidUntilDate", "MIValidUntilDateCalculationMethod", "MIWorkFlowSettingID", "NextRun", "NextRunCalculationMethod", "ObjectID", "RepeatMeterPositionSchedule", "StartAt", "TimeScheduleID", "UseContractDates", "UseContractVisitCount", "VisitCount", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('auto_create_offset_unit')
    def auto_create_offset_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    @field_validator('mi_valid_until_date_calculation_method')
    def mi_valid_until_date_calculation_method_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    @field_validator('next_run_calculation_method')
    def next_run_calculation_method_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectMISchedule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if auto_create (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create is None and "auto_create" in self.model_fields_set:
            _dict['AutoCreate'] = None

        # set to None if auto_create_offset (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create_offset is None and "auto_create_offset" in self.model_fields_set:
            _dict['AutoCreateOffset'] = None

        # set to None if auto_create_offset_unit (nullable) is None
        # and model_fields_set contains the field
        if self.auto_create_offset_unit is None and "auto_create_offset_unit" in self.model_fields_set:
            _dict['AutoCreateOffsetUnit'] = None

        # set to None if automated_scheduling (nullable) is None
        # and model_fields_set contains the field
        if self.automated_scheduling is None and "automated_scheduling" in self.model_fields_set:
            _dict['AutomatedScheduling'] = None

        # set to None if contract_id (nullable) is None
        # and model_fields_set contains the field
        if self.contract_id is None and "contract_id" in self.model_fields_set:
            _dict['ContractID'] = None

        # set to None if enable_interval_interrupt (nullable) is None
        # and model_fields_set contains the field
        if self.enable_interval_interrupt is None and "enable_interval_interrupt" in self.model_fields_set:
            _dict['EnableIntervalInterrupt'] = None

        # set to None if end_at (nullable) is None
        # and model_fields_set contains the field
        if self.end_at is None and "end_at" in self.model_fields_set:
            _dict['EndAt'] = None

        # set to None if in_active (nullable) is None
        # and model_fields_set contains the field
        if self.in_active is None and "in_active" in self.model_fields_set:
            _dict['InActive'] = None

        # set to None if inspection_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_person_id is None and "inspection_person_id" in self.model_fields_set:
            _dict['InspectionPersonID'] = None

        # set to None if inspection_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_relation_id is None and "inspection_relation_id" in self.model_fields_set:
            _dict['InspectionRelationID'] = None

        # set to None if last_next_run (nullable) is None
        # and model_fields_set contains the field
        if self.last_next_run is None and "last_next_run" in self.model_fields_set:
            _dict['LastNextRun'] = None

        # set to None if last_result (nullable) is None
        # and model_fields_set contains the field
        if self.last_result is None and "last_result" in self.model_fields_set:
            _dict['LastResult'] = None

        # set to None if last_run (nullable) is None
        # and model_fields_set contains the field
        if self.last_run is None and "last_run" in self.model_fields_set:
            _dict['LastRun'] = None

        # set to None if last_run_measuring_device1_measurement_function_value (nullable) is None
        # and model_fields_set contains the field
        if self.last_run_measuring_device1_measurement_function_value is None and "last_run_measuring_device1_measurement_function_value" in self.model_fields_set:
            _dict['LastRunMeasuringDevice1MeasurementFunctionValue'] = None

        # set to None if last_successful_run (nullable) is None
        # and model_fields_set contains the field
        if self.last_successful_run is None and "last_successful_run" in self.model_fields_set:
            _dict['LastSuccessfulRun'] = None

        # set to None if measuring_device1_measurement_function_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_measurement_function_id is None and "measuring_device1_measurement_function_id" in self.model_fields_set:
            _dict['MeasuringDevice1MeasurementFunctionID'] = None

        # set to None if measuring_device1_measurement_function_value (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_measurement_function_value is None and "measuring_device1_measurement_function_value" in self.model_fields_set:
            _dict['MeasuringDevice1MeasurementFunctionValue'] = None

        # set to None if measuring_device1_object_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_object_id is None and "measuring_device1_object_id" in self.model_fields_set:
            _dict['MeasuringDevice1ObjectID'] = None

        # set to None if measuring_device1_object_measurement_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_object_measurement_schedule_id is None and "measuring_device1_object_measurement_schedule_id" in self.model_fields_set:
            _dict['MeasuringDevice1ObjectMeasurementScheduleID'] = None

        # set to None if mi_interval_count (nullable) is None
        # and model_fields_set contains the field
        if self.mi_interval_count is None and "mi_interval_count" in self.model_fields_set:
            _dict['MIIntervalCount'] = None

        # set to None if mi_interval_repeat (nullable) is None
        # and model_fields_set contains the field
        if self.mi_interval_repeat is None and "mi_interval_repeat" in self.model_fields_set:
            _dict['MIIntervalRepeat'] = None

        # set to None if mi_interval_repeat_count (nullable) is None
        # and model_fields_set contains the field
        if self.mi_interval_repeat_count is None and "mi_interval_repeat_count" in self.model_fields_set:
            _dict['MIIntervalRepeatCount'] = None

        # set to None if mi_interval_skip_count (nullable) is None
        # and model_fields_set contains the field
        if self.mi_interval_skip_count is None and "mi_interval_skip_count" in self.model_fields_set:
            _dict['MIIntervalSkipCount'] = None

        # set to None if mi_plan_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_plan_id is None and "mi_plan_id" in self.model_fields_set:
            _dict['MIPlanID'] = None

        # set to None if mi_plan_sequence_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_plan_sequence_id is None and "mi_plan_sequence_id" in self.model_fields_set:
            _dict['MIPlanSequenceID'] = None

        # set to None if mi_validation_expires (nullable) is None
        # and model_fields_set contains the field
        if self.mi_validation_expires is None and "mi_validation_expires" in self.model_fields_set:
            _dict['MIValidationExpires'] = None

        # set to None if mi_validation_time_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_validation_time_schedule_id is None and "mi_validation_time_schedule_id" in self.model_fields_set:
            _dict['MIValidationTimeScheduleID'] = None

        # set to None if mi_valid_until_date (nullable) is None
        # and model_fields_set contains the field
        if self.mi_valid_until_date is None and "mi_valid_until_date" in self.model_fields_set:
            _dict['MIValidUntilDate'] = None

        # set to None if mi_valid_until_date_calculation_method (nullable) is None
        # and model_fields_set contains the field
        if self.mi_valid_until_date_calculation_method is None and "mi_valid_until_date_calculation_method" in self.model_fields_set:
            _dict['MIValidUntilDateCalculationMethod'] = None

        # set to None if mi_work_flow_setting_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_work_flow_setting_id is None and "mi_work_flow_setting_id" in self.model_fields_set:
            _dict['MIWorkFlowSettingID'] = None

        # set to None if next_run (nullable) is None
        # and model_fields_set contains the field
        if self.next_run is None and "next_run" in self.model_fields_set:
            _dict['NextRun'] = None

        # set to None if next_run_calculation_method (nullable) is None
        # and model_fields_set contains the field
        if self.next_run_calculation_method is None and "next_run_calculation_method" in self.model_fields_set:
            _dict['NextRunCalculationMethod'] = None

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if repeat_meter_position_schedule (nullable) is None
        # and model_fields_set contains the field
        if self.repeat_meter_position_schedule is None and "repeat_meter_position_schedule" in self.model_fields_set:
            _dict['RepeatMeterPositionSchedule'] = None

        # set to None if start_at (nullable) is None
        # and model_fields_set contains the field
        if self.start_at is None and "start_at" in self.model_fields_set:
            _dict['StartAt'] = None

        # set to None if time_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.time_schedule_id is None and "time_schedule_id" in self.model_fields_set:
            _dict['TimeScheduleID'] = None

        # set to None if use_contract_dates (nullable) is None
        # and model_fields_set contains the field
        if self.use_contract_dates is None and "use_contract_dates" in self.model_fields_set:
            _dict['UseContractDates'] = None

        # set to None if use_contract_visit_count (nullable) is None
        # and model_fields_set contains the field
        if self.use_contract_visit_count is None and "use_contract_visit_count" in self.model_fields_set:
            _dict['UseContractVisitCount'] = None

        # set to None if visit_count (nullable) is None
        # and model_fields_set contains the field
        if self.visit_count is None and "visit_count" in self.model_fields_set:
            _dict['VisitCount'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectMISchedule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AutoCreate": obj.get("AutoCreate"),
            "AutoCreateOffset": obj.get("AutoCreateOffset"),
            "AutoCreateOffsetUnit": obj.get("AutoCreateOffsetUnit"),
            "AutomatedScheduling": obj.get("AutomatedScheduling"),
            "ContractID": obj.get("ContractID"),
            "EnableIntervalInterrupt": obj.get("EnableIntervalInterrupt"),
            "EndAt": obj.get("EndAt"),
            "InActive": obj.get("InActive"),
            "InspectionPersonID": obj.get("InspectionPersonID"),
            "InspectionRelationID": obj.get("InspectionRelationID"),
            "LastNextRun": obj.get("LastNextRun"),
            "LastResult": obj.get("LastResult"),
            "LastRun": obj.get("LastRun"),
            "LastRunMeasuringDevice1MeasurementFunctionValue": obj.get("LastRunMeasuringDevice1MeasurementFunctionValue"),
            "LastSuccessfulRun": obj.get("LastSuccessfulRun"),
            "MeasuringDevice1MeasurementFunctionID": obj.get("MeasuringDevice1MeasurementFunctionID"),
            "MeasuringDevice1MeasurementFunctionValue": obj.get("MeasuringDevice1MeasurementFunctionValue"),
            "MeasuringDevice1ObjectID": obj.get("MeasuringDevice1ObjectID"),
            "MeasuringDevice1ObjectMeasurementScheduleID": obj.get("MeasuringDevice1ObjectMeasurementScheduleID"),
            "MIIntervalCount": obj.get("MIIntervalCount"),
            "MIIntervalRepeat": obj.get("MIIntervalRepeat"),
            "MIIntervalRepeatCount": obj.get("MIIntervalRepeatCount"),
            "MIIntervalSkipCount": obj.get("MIIntervalSkipCount"),
            "MIPlanID": obj.get("MIPlanID"),
            "MIPlanSequenceID": obj.get("MIPlanSequenceID"),
            "MIValidationExpires": obj.get("MIValidationExpires"),
            "MIValidationTimeScheduleID": obj.get("MIValidationTimeScheduleID"),
            "MIValidUntilDate": obj.get("MIValidUntilDate"),
            "MIValidUntilDateCalculationMethod": obj.get("MIValidUntilDateCalculationMethod"),
            "MIWorkFlowSettingID": obj.get("MIWorkFlowSettingID"),
            "NextRun": obj.get("NextRun"),
            "NextRunCalculationMethod": obj.get("NextRunCalculationMethod"),
            "ObjectID": obj.get("ObjectID"),
            "RepeatMeterPositionSchedule": obj.get("RepeatMeterPositionSchedule"),
            "StartAt": obj.get("StartAt"),
            "TimeScheduleID": obj.get("TimeScheduleID"),
            "UseContractDates": obj.get("UseContractDates"),
            "UseContractVisitCount": obj.get("UseContractVisitCount"),
            "VisitCount": obj.get("VisitCount"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


