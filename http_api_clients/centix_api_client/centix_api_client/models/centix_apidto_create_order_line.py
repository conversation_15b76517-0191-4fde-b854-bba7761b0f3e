# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOCreateOrderLine(BaseModel):
    """
    CentixAPIDTOCreateOrderLine
    """ # noqa: E501
    allow_repurchase: Optional[StrictBool] = Field(default=None, alias="AllowRepurchase")
    product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="When the value is empty, the value of the property on the product will be used.", alias="ProductCalcPrice")
    product_id: Optional[StrictInt] = Field(default=None, description="When the product is empty, a comment line will be created.", alias="ProductID")
    budget_code_id: Optional[StrictInt] = Field(default=None, alias="BudgetCodeID")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    descr: Optional[StrictStr] = Field(default=None, description="When the value is empty, the value of the property on the product will be used.", alias="Descr")
    discount_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="DiscountPCT")
    dont_allow_partial_delivery: Optional[StrictBool] = Field(default=None, alias="DontAllowPartialDelivery")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    invoice_order_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceOrderCosts")
    invoice_transport_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceTransportCosts")
    is_drop_shipment: Optional[StrictBool] = Field(default=None, alias="IsDropShipment")
    lock_price: Optional[StrictBool] = Field(default=None, alias="LockPrice")
    order_quantity: Optional[StrictInt] = Field(default=None, alias="OrderQuantity")
    position_no: Optional[StrictInt] = Field(default=None, alias="PositionNo")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    rented_from: Optional[datetime] = Field(default=None, alias="RentedFrom")
    rented_till: Optional[datetime] = Field(default=None, alias="RentedTill")
    repurchase_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RepurchasePCT")
    vat_id: Optional[StrictInt] = Field(default=None, alias="VatID")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AllowRepurchase", "ProductCalcPrice", "ProductID", "BudgetCodeID", "CostCategoryID", "CostCodeID", "Descr", "DiscountPCT", "DontAllowPartialDelivery", "ExternalNote", "InternalNote", "InvoiceOrderCosts", "InvoiceTransportCosts", "IsDropShipment", "LockPrice", "OrderQuantity", "PositionNo", "ProjectID", "Reference", "RentedFrom", "RentedTill", "RepurchasePCT", "VatID"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateOrderLine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if budget_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.budget_code_id is None and "budget_code_id" in self.model_fields_set:
            _dict['BudgetCodeID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if discount_pct (nullable) is None
        # and model_fields_set contains the field
        if self.discount_pct is None and "discount_pct" in self.model_fields_set:
            _dict['DiscountPCT'] = None

        # set to None if invoice_order_costs (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_order_costs is None and "invoice_order_costs" in self.model_fields_set:
            _dict['InvoiceOrderCosts'] = None

        # set to None if invoice_transport_costs (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_transport_costs is None and "invoice_transport_costs" in self.model_fields_set:
            _dict['InvoiceTransportCosts'] = None

        # set to None if order_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.order_quantity is None and "order_quantity" in self.model_fields_set:
            _dict['OrderQuantity'] = None

        # set to None if position_no (nullable) is None
        # and model_fields_set contains the field
        if self.position_no is None and "position_no" in self.model_fields_set:
            _dict['PositionNo'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if rented_from (nullable) is None
        # and model_fields_set contains the field
        if self.rented_from is None and "rented_from" in self.model_fields_set:
            _dict['RentedFrom'] = None

        # set to None if rented_till (nullable) is None
        # and model_fields_set contains the field
        if self.rented_till is None and "rented_till" in self.model_fields_set:
            _dict['RentedTill'] = None

        # set to None if repurchase_pct (nullable) is None
        # and model_fields_set contains the field
        if self.repurchase_pct is None and "repurchase_pct" in self.model_fields_set:
            _dict['RepurchasePCT'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateOrderLine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AllowRepurchase": obj.get("AllowRepurchase"),
            "ProductCalcPrice": obj.get("ProductCalcPrice"),
            "ProductID": obj.get("ProductID"),
            "BudgetCodeID": obj.get("BudgetCodeID"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "Descr": obj.get("Descr"),
            "DiscountPCT": obj.get("DiscountPCT"),
            "DontAllowPartialDelivery": obj.get("DontAllowPartialDelivery"),
            "ExternalNote": obj.get("ExternalNote"),
            "InternalNote": obj.get("InternalNote"),
            "InvoiceOrderCosts": obj.get("InvoiceOrderCosts"),
            "InvoiceTransportCosts": obj.get("InvoiceTransportCosts"),
            "IsDropShipment": obj.get("IsDropShipment"),
            "LockPrice": obj.get("LockPrice"),
            "OrderQuantity": obj.get("OrderQuantity"),
            "PositionNo": obj.get("PositionNo"),
            "ProjectID": obj.get("ProjectID"),
            "Reference": obj.get("Reference"),
            "RentedFrom": obj.get("RentedFrom"),
            "RentedTill": obj.get("RentedTill"),
            "RepurchasePCT": obj.get("RepurchasePCT"),
            "VatID": obj.get("VatID")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


