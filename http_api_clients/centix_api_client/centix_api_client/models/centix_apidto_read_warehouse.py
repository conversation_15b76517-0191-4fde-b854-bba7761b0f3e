# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadWarehouse(BaseModel):
    """
    CentixAPIDTOReadWarehouse
    """ # noqa: E501
    default_warehouse_location_id: Optional[StrictInt] = Field(default=None, alias="DefaultWarehouseLocationID")
    repair_location_id: Optional[StrictInt] = Field(default=None, alias="RepairLocationID")
    cleaning_location_id: Optional[StrictInt] = Field(default=None, alias="CleaningLocationID")
    missing_location_id: Optional[StrictInt] = Field(default=None, alias="MissingLocationID")
    claim_location_id: Optional[StrictInt] = Field(default=None, alias="ClaimLocationID")
    beyond_repair_location_id: Optional[StrictInt] = Field(default=None, alias="BeyondRepairLocationID")
    time_zone: Optional[StrictStr] = Field(default=None, alias="TimeZone")
    ware_house_manager_id: Optional[StrictInt] = Field(default=None, alias="WareHouseManagerID")
    workable_day_id: Optional[StrictInt] = Field(default=None, alias="WorkableDayID")
    work_hour_plan_id: Optional[StrictInt] = Field(default=None, alias="WorkHourPlanID")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["DefaultWarehouseLocationID", "RepairLocationID", "CleaningLocationID", "MissingLocationID", "ClaimLocationID", "BeyondRepairLocationID", "TimeZone", "WareHouseManagerID", "WorkableDayID", "WorkHourPlanID", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWarehouse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if default_warehouse_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.default_warehouse_location_id is None and "default_warehouse_location_id" in self.model_fields_set:
            _dict['DefaultWarehouseLocationID'] = None

        # set to None if repair_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.repair_location_id is None and "repair_location_id" in self.model_fields_set:
            _dict['RepairLocationID'] = None

        # set to None if cleaning_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.cleaning_location_id is None and "cleaning_location_id" in self.model_fields_set:
            _dict['CleaningLocationID'] = None

        # set to None if missing_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.missing_location_id is None and "missing_location_id" in self.model_fields_set:
            _dict['MissingLocationID'] = None

        # set to None if claim_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.claim_location_id is None and "claim_location_id" in self.model_fields_set:
            _dict['ClaimLocationID'] = None

        # set to None if beyond_repair_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.beyond_repair_location_id is None and "beyond_repair_location_id" in self.model_fields_set:
            _dict['BeyondRepairLocationID'] = None

        # set to None if ware_house_manager_id (nullable) is None
        # and model_fields_set contains the field
        if self.ware_house_manager_id is None and "ware_house_manager_id" in self.model_fields_set:
            _dict['WareHouseManagerID'] = None

        # set to None if workable_day_id (nullable) is None
        # and model_fields_set contains the field
        if self.workable_day_id is None and "workable_day_id" in self.model_fields_set:
            _dict['WorkableDayID'] = None

        # set to None if work_hour_plan_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_hour_plan_id is None and "work_hour_plan_id" in self.model_fields_set:
            _dict['WorkHourPlanID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWarehouse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "DefaultWarehouseLocationID": obj.get("DefaultWarehouseLocationID"),
            "RepairLocationID": obj.get("RepairLocationID"),
            "CleaningLocationID": obj.get("CleaningLocationID"),
            "MissingLocationID": obj.get("MissingLocationID"),
            "ClaimLocationID": obj.get("ClaimLocationID"),
            "BeyondRepairLocationID": obj.get("BeyondRepairLocationID"),
            "TimeZone": obj.get("TimeZone"),
            "WareHouseManagerID": obj.get("WareHouseManagerID"),
            "WorkableDayID": obj.get("WorkableDayID"),
            "WorkHourPlanID": obj.get("WorkHourPlanID"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


