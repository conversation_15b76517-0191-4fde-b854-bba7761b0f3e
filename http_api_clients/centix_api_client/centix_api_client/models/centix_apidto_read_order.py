# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from centix_api_client.models.centix_apidto_read_order_line import CentixAPIDTOReadOrderLine
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadOrder(BaseModel):
    """
    CentixAPIDTOReadOrder
    """ # noqa: E501
    order_lines: Optional[List[CentixAPIDTOReadOrderLine]] = Field(default=None, alias="OrderLines")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    approve_before: Optional[datetime] = Field(default=None, alias="ApproveBefore")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    blocked_for_invoice: Optional[StrictBool] = Field(default=None, alias="BlockedForInvoice")
    blocked_for_merge: Optional[StrictBool] = Field(default=None, alias="BlockedForMerge")
    blocked: Optional[StrictBool] = Field(default=None, alias="Blocked")
    cancel_comment: Optional[StrictStr] = Field(default=None, alias="CancelComment")
    cancelled_at: Optional[datetime] = Field(default=None, alias="CancelledAt")
    cancel_reason_id: Optional[StrictInt] = Field(default=None, alias="CancelReasonID")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    created_by_person_id: Optional[StrictInt] = Field(default=None, alias="CreatedByPersonID")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    deliver_additional_address_info1: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo1")
    deliver_additional_address_info2: Optional[StrictStr] = Field(default=None, alias="DeliverAdditionalAddressInfo2")
    deliver_address: Optional[StrictStr] = Field(default=None, alias="DeliverAddress")
    deliver_city: Optional[StrictStr] = Field(default=None, alias="DeliverCity")
    deliver_contact_person_id: Optional[StrictInt] = Field(default=None, alias="DeliverContactPersonID")
    deliver_country_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountryID")
    deliver_county_id: Optional[StrictInt] = Field(default=None, alias="DeliverCountyID")
    deliver_date: Optional[datetime] = Field(default=None, alias="DeliverDate")
    deliver_house_no: Optional[StrictStr] = Field(default=None, alias="DeliverHouseNo")
    deliver_house_no_add: Optional[StrictStr] = Field(default=None, alias="DeliverHouseNoAdd")
    deliver_location_id: Optional[StrictInt] = Field(default=None, alias="DeliverLocationID")
    deliver_person_descr: Optional[StrictStr] = Field(default=None, alias="DeliverPersonDescr")
    deliver_postalcode: Optional[StrictStr] = Field(default=None, alias="DeliverPostalcode")
    deliver_region_id: Optional[StrictInt] = Field(default=None, alias="DeliverRegionID")
    deliver_relation_descr: Optional[StrictStr] = Field(default=None, alias="DeliverRelationDescr")
    deliver_relation_drop_shipment: Optional[StrictInt] = Field(default=None, description="0 = DontAllowDropShipment, 1 = AllowDropShipment, 2 = DefaultDropShipment, 3 = DropShipmentRequired", alias="DeliverRelationDropShipment")
    deliver_relation_id: Optional[StrictInt] = Field(default=None, alias="DeliverRelationID")
    deliver_state_id: Optional[StrictInt] = Field(default=None, alias="DeliverStateID")
    dont_allow_partial_order_delivery: Optional[StrictBool] = Field(default=None, alias="DontAllowPartialOrderDelivery")
    expected_return_date: Optional[datetime] = Field(default=None, alias="ExpectedReturnDate")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    invoice_contact_person_id: Optional[StrictInt] = Field(default=None, alias="InvoiceContactPersonID")
    invoiced: Optional[StrictBool] = Field(default=None, alias="Invoiced")
    invoice_order_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceOrderCosts")
    invoice_period_unit: Optional[StrictInt] = Field(default=None, description="3 = Daily, 4 = Weekly, 5 = Monthly, 6 = Quarterly, 7 = Yearly, 8 = HalfYearly, 10 = Completely", alias="InvoicePeriodUnit")
    invoice_relation_id: Optional[StrictInt] = Field(default=None, alias="InvoiceRelationID")
    invoice_transport_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceTransportCosts")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    location_rental_price_list_id: Optional[StrictInt] = Field(default=None, alias="LocationRentalPriceListID")
    object_rental_price_list_id: Optional[StrictInt] = Field(default=None, alias="ObjectRentalPriceListID")
    order_cost_method_calculation: Optional[StrictInt] = Field(default=None, description="0 = Order, 1 = Shipment", alias="OrderCostMethodCalculation")
    order_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="OrderCosts")
    order_costs_invoiced: Optional[StrictBool] = Field(default=None, alias="OrderCostsInvoiced")
    order_costs_vat_id: Optional[StrictInt] = Field(default=None, alias="OrderCostsVatID")
    order_date: Optional[datetime] = Field(default=None, alias="OrderDate")
    payment_condition_id: Optional[StrictInt] = Field(default=None, alias="PaymentConditionID")
    payment_method_id: Optional[StrictInt] = Field(default=None, alias="PaymentMethodID")
    periodic_order: Optional[StrictBool] = Field(default=None, alias="PeriodicOrder")
    preferred_approval_user_id: Optional[StrictInt] = Field(default=None, alias="PreferredApprovalUserID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    sale_price_list_id: Optional[StrictInt] = Field(default=None, alias="SalePriceListID")
    scoring_change: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="ScoringChange")
    selection_code: Optional[StrictStr] = Field(default=None, alias="SelectionCode")
    solve_before: Optional[datetime] = Field(default=None, alias="SolveBefore")
    status: Optional[StrictInt] = Field(default=None, description="0 = OfferRequest, 1 = NotAuthorized, 2 = Order, 3 = Supply, 4 = Backorder, 5 = RentalOutstanding, 6 = Completed, 7 = Cancelled, 8 = Authorized, 9 = Refused, 13 = OfferRequest_Refused, -1 = Basket", alias="Status")
    transport_costs: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="TransportCosts")
    transport_costs_vat_id: Optional[StrictInt] = Field(default=None, alias="TransportCostsVatID")
    transport_invoiced: Optional[StrictBool] = Field(default=None, alias="TransportInvoiced")
    transport_method_calculation: Optional[StrictInt] = Field(default=None, description="0 = Order, 1 = Shipment", alias="TransportMethodCalculation")
    transport_method_id: Optional[StrictInt] = Field(default=None, alias="TransportMethodID")
    transport_method_option_id: Optional[StrictInt] = Field(default=None, alias="TransportMethodOptionID")
    validity_date: Optional[datetime] = Field(default=None, alias="ValidityDate")
    validity_period: Optional[StrictInt] = Field(default=None, alias="ValidityPeriod")
    validity_period_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="ValidityPeriodUnit")
    warehouse_id: Optional[StrictInt] = Field(default=None, alias="WarehouseID")
    workable_day_id: Optional[StrictInt] = Field(default=None, alias="WorkableDayID")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["OrderLines", "AdministrationID", "ApproveBefore", "AssignedToPersonID", "BlockedForInvoice", "BlockedForMerge", "Blocked", "CancelComment", "CancelledAt", "CancelReasonID", "CostCategoryID", "CostCodeID", "CreatedByPersonID", "CurrencyID", "DeliverAdditionalAddressInfo1", "DeliverAdditionalAddressInfo2", "DeliverAddress", "DeliverCity", "DeliverContactPersonID", "DeliverCountryID", "DeliverCountyID", "DeliverDate", "DeliverHouseNo", "DeliverHouseNoAdd", "DeliverLocationID", "DeliverPersonDescr", "DeliverPostalcode", "DeliverRegionID", "DeliverRelationDescr", "DeliverRelationDropShipment", "DeliverRelationID", "DeliverStateID", "DontAllowPartialOrderDelivery", "ExpectedReturnDate", "ExternalNote", "InternalNote", "InvoiceContactPersonID", "Invoiced", "InvoiceOrderCosts", "InvoicePeriodUnit", "InvoiceRelationID", "InvoiceTransportCosts", "LanguageID", "LocationRentalPriceListID", "ObjectRentalPriceListID", "OrderCostMethodCalculation", "OrderCosts", "OrderCostsInvoiced", "OrderCostsVatID", "OrderDate", "PaymentConditionID", "PaymentMethodID", "PeriodicOrder", "PreferredApprovalUserID", "ProjectID", "Reference", "SalePriceListID", "ScoringChange", "SelectionCode", "SolveBefore", "Status", "TransportCosts", "TransportCostsVatID", "TransportInvoiced", "TransportMethodCalculation", "TransportMethodID", "TransportMethodOptionID", "ValidityDate", "ValidityPeriod", "ValidityPeriodUnit", "WarehouseID", "WorkableDayID", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('deliver_relation_drop_shipment')
    def deliver_relation_drop_shipment_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    @field_validator('invoice_period_unit')
    def invoice_period_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([3, 4, 5, 6, 7, 8, 10]):
            raise ValueError("must be one of enum values (3, 4, 5, 6, 7, 8, 10)")
        return value

    @field_validator('order_cost_method_calculation')
    def order_cost_method_calculation_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, -1]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, -1)")
        return value

    @field_validator('transport_method_calculation')
    def transport_method_calculation_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    @field_validator('validity_period_unit')
    def validity_period_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrder from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in order_lines (list)
        _items = []
        if self.order_lines:
            for _item_order_lines in self.order_lines:
                if _item_order_lines:
                    _items.append(_item_order_lines.to_dict())
            _dict['OrderLines'] = _items
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if approve_before (nullable) is None
        # and model_fields_set contains the field
        if self.approve_before is None and "approve_before" in self.model_fields_set:
            _dict['ApproveBefore'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        # set to None if cancelled_at (nullable) is None
        # and model_fields_set contains the field
        if self.cancelled_at is None and "cancelled_at" in self.model_fields_set:
            _dict['CancelledAt'] = None

        # set to None if cancel_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.cancel_reason_id is None and "cancel_reason_id" in self.model_fields_set:
            _dict['CancelReasonID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if created_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.created_by_person_id is None and "created_by_person_id" in self.model_fields_set:
            _dict['CreatedByPersonID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if deliver_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_contact_person_id is None and "deliver_contact_person_id" in self.model_fields_set:
            _dict['DeliverContactPersonID'] = None

        # set to None if deliver_country_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_country_id is None and "deliver_country_id" in self.model_fields_set:
            _dict['DeliverCountryID'] = None

        # set to None if deliver_county_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_county_id is None and "deliver_county_id" in self.model_fields_set:
            _dict['DeliverCountyID'] = None

        # set to None if deliver_date (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_date is None and "deliver_date" in self.model_fields_set:
            _dict['DeliverDate'] = None

        # set to None if deliver_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_location_id is None and "deliver_location_id" in self.model_fields_set:
            _dict['DeliverLocationID'] = None

        # set to None if deliver_region_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_region_id is None and "deliver_region_id" in self.model_fields_set:
            _dict['DeliverRegionID'] = None

        # set to None if deliver_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_relation_id is None and "deliver_relation_id" in self.model_fields_set:
            _dict['DeliverRelationID'] = None

        # set to None if deliver_state_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_state_id is None and "deliver_state_id" in self.model_fields_set:
            _dict['DeliverStateID'] = None

        # set to None if expected_return_date (nullable) is None
        # and model_fields_set contains the field
        if self.expected_return_date is None and "expected_return_date" in self.model_fields_set:
            _dict['ExpectedReturnDate'] = None

        # set to None if invoice_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_contact_person_id is None and "invoice_contact_person_id" in self.model_fields_set:
            _dict['InvoiceContactPersonID'] = None

        # set to None if invoice_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_relation_id is None and "invoice_relation_id" in self.model_fields_set:
            _dict['InvoiceRelationID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if location_rental_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_rental_price_list_id is None and "location_rental_price_list_id" in self.model_fields_set:
            _dict['LocationRentalPriceListID'] = None

        # set to None if object_rental_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_rental_price_list_id is None and "object_rental_price_list_id" in self.model_fields_set:
            _dict['ObjectRentalPriceListID'] = None

        # set to None if order_cost_method_calculation (nullable) is None
        # and model_fields_set contains the field
        if self.order_cost_method_calculation is None and "order_cost_method_calculation" in self.model_fields_set:
            _dict['OrderCostMethodCalculation'] = None

        # set to None if order_costs (nullable) is None
        # and model_fields_set contains the field
        if self.order_costs is None and "order_costs" in self.model_fields_set:
            _dict['OrderCosts'] = None

        # set to None if order_costs_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.order_costs_vat_id is None and "order_costs_vat_id" in self.model_fields_set:
            _dict['OrderCostsVatID'] = None

        # set to None if order_date (nullable) is None
        # and model_fields_set contains the field
        if self.order_date is None and "order_date" in self.model_fields_set:
            _dict['OrderDate'] = None

        # set to None if payment_condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.payment_condition_id is None and "payment_condition_id" in self.model_fields_set:
            _dict['PaymentConditionID'] = None

        # set to None if payment_method_id (nullable) is None
        # and model_fields_set contains the field
        if self.payment_method_id is None and "payment_method_id" in self.model_fields_set:
            _dict['PaymentMethodID'] = None

        # set to None if preferred_approval_user_id (nullable) is None
        # and model_fields_set contains the field
        if self.preferred_approval_user_id is None and "preferred_approval_user_id" in self.model_fields_set:
            _dict['PreferredApprovalUserID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if sale_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.sale_price_list_id is None and "sale_price_list_id" in self.model_fields_set:
            _dict['SalePriceListID'] = None

        # set to None if scoring_change (nullable) is None
        # and model_fields_set contains the field
        if self.scoring_change is None and "scoring_change" in self.model_fields_set:
            _dict['ScoringChange'] = None

        # set to None if solve_before (nullable) is None
        # and model_fields_set contains the field
        if self.solve_before is None and "solve_before" in self.model_fields_set:
            _dict['SolveBefore'] = None

        # set to None if transport_costs (nullable) is None
        # and model_fields_set contains the field
        if self.transport_costs is None and "transport_costs" in self.model_fields_set:
            _dict['TransportCosts'] = None

        # set to None if transport_costs_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.transport_costs_vat_id is None and "transport_costs_vat_id" in self.model_fields_set:
            _dict['TransportCostsVatID'] = None

        # set to None if transport_method_calculation (nullable) is None
        # and model_fields_set contains the field
        if self.transport_method_calculation is None and "transport_method_calculation" in self.model_fields_set:
            _dict['TransportMethodCalculation'] = None

        # set to None if transport_method_id (nullable) is None
        # and model_fields_set contains the field
        if self.transport_method_id is None and "transport_method_id" in self.model_fields_set:
            _dict['TransportMethodID'] = None

        # set to None if transport_method_option_id (nullable) is None
        # and model_fields_set contains the field
        if self.transport_method_option_id is None and "transport_method_option_id" in self.model_fields_set:
            _dict['TransportMethodOptionID'] = None

        # set to None if validity_date (nullable) is None
        # and model_fields_set contains the field
        if self.validity_date is None and "validity_date" in self.model_fields_set:
            _dict['ValidityDate'] = None

        # set to None if validity_period (nullable) is None
        # and model_fields_set contains the field
        if self.validity_period is None and "validity_period" in self.model_fields_set:
            _dict['ValidityPeriod'] = None

        # set to None if warehouse_id (nullable) is None
        # and model_fields_set contains the field
        if self.warehouse_id is None and "warehouse_id" in self.model_fields_set:
            _dict['WarehouseID'] = None

        # set to None if workable_day_id (nullable) is None
        # and model_fields_set contains the field
        if self.workable_day_id is None and "workable_day_id" in self.model_fields_set:
            _dict['WorkableDayID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrder from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "OrderLines": [CentixAPIDTOReadOrderLine.from_dict(_item) for _item in obj["OrderLines"]] if obj.get("OrderLines") is not None else None,
            "AdministrationID": obj.get("AdministrationID"),
            "ApproveBefore": obj.get("ApproveBefore"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "BlockedForInvoice": obj.get("BlockedForInvoice"),
            "BlockedForMerge": obj.get("BlockedForMerge"),
            "Blocked": obj.get("Blocked"),
            "CancelComment": obj.get("CancelComment"),
            "CancelledAt": obj.get("CancelledAt"),
            "CancelReasonID": obj.get("CancelReasonID"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "CreatedByPersonID": obj.get("CreatedByPersonID"),
            "CurrencyID": obj.get("CurrencyID"),
            "DeliverAdditionalAddressInfo1": obj.get("DeliverAdditionalAddressInfo1"),
            "DeliverAdditionalAddressInfo2": obj.get("DeliverAdditionalAddressInfo2"),
            "DeliverAddress": obj.get("DeliverAddress"),
            "DeliverCity": obj.get("DeliverCity"),
            "DeliverContactPersonID": obj.get("DeliverContactPersonID"),
            "DeliverCountryID": obj.get("DeliverCountryID"),
            "DeliverCountyID": obj.get("DeliverCountyID"),
            "DeliverDate": obj.get("DeliverDate"),
            "DeliverHouseNo": obj.get("DeliverHouseNo"),
            "DeliverHouseNoAdd": obj.get("DeliverHouseNoAdd"),
            "DeliverLocationID": obj.get("DeliverLocationID"),
            "DeliverPersonDescr": obj.get("DeliverPersonDescr"),
            "DeliverPostalcode": obj.get("DeliverPostalcode"),
            "DeliverRegionID": obj.get("DeliverRegionID"),
            "DeliverRelationDescr": obj.get("DeliverRelationDescr"),
            "DeliverRelationDropShipment": obj.get("DeliverRelationDropShipment"),
            "DeliverRelationID": obj.get("DeliverRelationID"),
            "DeliverStateID": obj.get("DeliverStateID"),
            "DontAllowPartialOrderDelivery": obj.get("DontAllowPartialOrderDelivery"),
            "ExpectedReturnDate": obj.get("ExpectedReturnDate"),
            "ExternalNote": obj.get("ExternalNote"),
            "InternalNote": obj.get("InternalNote"),
            "InvoiceContactPersonID": obj.get("InvoiceContactPersonID"),
            "Invoiced": obj.get("Invoiced"),
            "InvoiceOrderCosts": obj.get("InvoiceOrderCosts"),
            "InvoicePeriodUnit": obj.get("InvoicePeriodUnit"),
            "InvoiceRelationID": obj.get("InvoiceRelationID"),
            "InvoiceTransportCosts": obj.get("InvoiceTransportCosts"),
            "LanguageID": obj.get("LanguageID"),
            "LocationRentalPriceListID": obj.get("LocationRentalPriceListID"),
            "ObjectRentalPriceListID": obj.get("ObjectRentalPriceListID"),
            "OrderCostMethodCalculation": obj.get("OrderCostMethodCalculation"),
            "OrderCosts": obj.get("OrderCosts"),
            "OrderCostsInvoiced": obj.get("OrderCostsInvoiced"),
            "OrderCostsVatID": obj.get("OrderCostsVatID"),
            "OrderDate": obj.get("OrderDate"),
            "PaymentConditionID": obj.get("PaymentConditionID"),
            "PaymentMethodID": obj.get("PaymentMethodID"),
            "PeriodicOrder": obj.get("PeriodicOrder"),
            "PreferredApprovalUserID": obj.get("PreferredApprovalUserID"),
            "ProjectID": obj.get("ProjectID"),
            "Reference": obj.get("Reference"),
            "SalePriceListID": obj.get("SalePriceListID"),
            "ScoringChange": obj.get("ScoringChange"),
            "SelectionCode": obj.get("SelectionCode"),
            "SolveBefore": obj.get("SolveBefore"),
            "Status": obj.get("Status"),
            "TransportCosts": obj.get("TransportCosts"),
            "TransportCostsVatID": obj.get("TransportCostsVatID"),
            "TransportInvoiced": obj.get("TransportInvoiced"),
            "TransportMethodCalculation": obj.get("TransportMethodCalculation"),
            "TransportMethodID": obj.get("TransportMethodID"),
            "TransportMethodOptionID": obj.get("TransportMethodOptionID"),
            "ValidityDate": obj.get("ValidityDate"),
            "ValidityPeriod": obj.get("ValidityPeriod"),
            "ValidityPeriodUnit": obj.get("ValidityPeriodUnit"),
            "WarehouseID": obj.get("WarehouseID"),
            "WorkableDayID": obj.get("WorkableDayID"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


