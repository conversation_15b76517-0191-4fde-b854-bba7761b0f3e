# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateDocument(BaseModel):
    """
    CentixAPIDTOUpdateDocument
    """ # noqa: E501
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    document_kind_id: Optional[StrictInt] = Field(default=None, alias="DocumentKindID")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    comment: Optional[StrictStr] = Field(default=None, alias="Comment")
    expire_date: Optional[datetime] = Field(default=None, alias="ExpireDate")
    file_name: Optional[StrictStr] = Field(default=None, alias="FileName")
    file_extension: Optional[StrictStr] = Field(default=None, alias="FileExtension")
    file_size: Optional[StrictInt] = Field(default=None, alias="FileSize")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Descr", "DocumentKindID", "LanguageID", "Comment", "ExpireDate", "FileName", "FileExtension", "FileSize"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateDocument from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if document_kind_id (nullable) is None
        # and model_fields_set contains the field
        if self.document_kind_id is None and "document_kind_id" in self.model_fields_set:
            _dict['DocumentKindID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if expire_date (nullable) is None
        # and model_fields_set contains the field
        if self.expire_date is None and "expire_date" in self.model_fields_set:
            _dict['ExpireDate'] = None

        # set to None if file_size (nullable) is None
        # and model_fields_set contains the field
        if self.file_size is None and "file_size" in self.model_fields_set:
            _dict['FileSize'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateDocument from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "DocumentKindID": obj.get("DocumentKindID"),
            "LanguageID": obj.get("LanguageID"),
            "Comment": obj.get("Comment"),
            "ExpireDate": obj.get("ExpireDate"),
            "FileName": obj.get("FileName"),
            "FileExtension": obj.get("FileExtension"),
            "FileSize": obj.get("FileSize")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


