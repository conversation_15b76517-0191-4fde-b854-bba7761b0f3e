# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadProductGroup(BaseModel):
    """
    CentixAPIDTOReadProductGroup
    """ # noqa: E501
    revenue_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="RevenueGeneralLedgerID")
    receptions_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="ReceptionsGeneralLedgerID")
    price_difference_purchase_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="PriceDifferencePurchaseGeneralLedgerID")
    stock_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="StockGeneralLedgerID")
    cost_price_sale_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="CostPriceSaleGeneralLedgerID")
    stock_revaluation_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="StockRevaluationGeneralLedgerID")
    expense_cover_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="ExpenseCoverGeneralLedgerID")
    jobs_in_progress_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="JobsInProgressGeneralLedgerID")
    invest_below_invest_incentives_limit_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="InvestBelowInvestIncentivesLimitGeneralLedgerID")
    invest_above_invest_incentives_limit_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="InvestAboveInvestIncentivesLimitGeneralLedgerID")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["RevenueGeneralLedgerID", "ReceptionsGeneralLedgerID", "PriceDifferencePurchaseGeneralLedgerID", "StockGeneralLedgerID", "CostPriceSaleGeneralLedgerID", "StockRevaluationGeneralLedgerID", "ExpenseCoverGeneralLedgerID", "JobsInProgressGeneralLedgerID", "InvestBelowInvestIncentivesLimitGeneralLedgerID", "InvestAboveInvestIncentivesLimitGeneralLedgerID", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadProductGroup from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if revenue_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.revenue_general_ledger_id is None and "revenue_general_ledger_id" in self.model_fields_set:
            _dict['RevenueGeneralLedgerID'] = None

        # set to None if receptions_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.receptions_general_ledger_id is None and "receptions_general_ledger_id" in self.model_fields_set:
            _dict['ReceptionsGeneralLedgerID'] = None

        # set to None if price_difference_purchase_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.price_difference_purchase_general_ledger_id is None and "price_difference_purchase_general_ledger_id" in self.model_fields_set:
            _dict['PriceDifferencePurchaseGeneralLedgerID'] = None

        # set to None if stock_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.stock_general_ledger_id is None and "stock_general_ledger_id" in self.model_fields_set:
            _dict['StockGeneralLedgerID'] = None

        # set to None if cost_price_sale_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_price_sale_general_ledger_id is None and "cost_price_sale_general_ledger_id" in self.model_fields_set:
            _dict['CostPriceSaleGeneralLedgerID'] = None

        # set to None if stock_revaluation_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.stock_revaluation_general_ledger_id is None and "stock_revaluation_general_ledger_id" in self.model_fields_set:
            _dict['StockRevaluationGeneralLedgerID'] = None

        # set to None if expense_cover_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.expense_cover_general_ledger_id is None and "expense_cover_general_ledger_id" in self.model_fields_set:
            _dict['ExpenseCoverGeneralLedgerID'] = None

        # set to None if jobs_in_progress_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.jobs_in_progress_general_ledger_id is None and "jobs_in_progress_general_ledger_id" in self.model_fields_set:
            _dict['JobsInProgressGeneralLedgerID'] = None

        # set to None if invest_below_invest_incentives_limit_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.invest_below_invest_incentives_limit_general_ledger_id is None and "invest_below_invest_incentives_limit_general_ledger_id" in self.model_fields_set:
            _dict['InvestBelowInvestIncentivesLimitGeneralLedgerID'] = None

        # set to None if invest_above_invest_incentives_limit_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.invest_above_invest_incentives_limit_general_ledger_id is None and "invest_above_invest_incentives_limit_general_ledger_id" in self.model_fields_set:
            _dict['InvestAboveInvestIncentivesLimitGeneralLedgerID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadProductGroup from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "RevenueGeneralLedgerID": obj.get("RevenueGeneralLedgerID"),
            "ReceptionsGeneralLedgerID": obj.get("ReceptionsGeneralLedgerID"),
            "PriceDifferencePurchaseGeneralLedgerID": obj.get("PriceDifferencePurchaseGeneralLedgerID"),
            "StockGeneralLedgerID": obj.get("StockGeneralLedgerID"),
            "CostPriceSaleGeneralLedgerID": obj.get("CostPriceSaleGeneralLedgerID"),
            "StockRevaluationGeneralLedgerID": obj.get("StockRevaluationGeneralLedgerID"),
            "ExpenseCoverGeneralLedgerID": obj.get("ExpenseCoverGeneralLedgerID"),
            "JobsInProgressGeneralLedgerID": obj.get("JobsInProgressGeneralLedgerID"),
            "InvestBelowInvestIncentivesLimitGeneralLedgerID": obj.get("InvestBelowInvestIncentivesLimitGeneralLedgerID"),
            "InvestAboveInvestIncentivesLimitGeneralLedgerID": obj.get("InvestAboveInvestIncentivesLimitGeneralLedgerID"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


