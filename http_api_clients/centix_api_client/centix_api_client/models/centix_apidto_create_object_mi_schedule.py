# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOCreateObjectMISchedule(BaseModel):
    """
    CentixAPIDTOCreateObjectMISchedule
    """ # noqa: E501
    mi_plan_id: StrictInt = Field(alias="MIPlanID")
    mi_plan_sequence_id: Optional[StrictInt] = Field(default=None, alias="MIPlanSequenceID")
    object_id: StrictInt = Field(alias="ObjectID")
    time_schedule_id: Optional[StrictInt] = Field(default=None, alias="TimeScheduleID")
    next_run_calculation_method: Optional[StrictInt] = Field(default=None, description="0 = OnLastSuccessfulRun, 1 = OnLastRun", alias="NextRunCalculationMethod")
    in_active: Optional[StrictBool] = Field(default=None, alias="InActive")
    inspection_person_id: Optional[StrictInt] = Field(default=None, alias="InspectionPersonID")
    inspection_relation_id: Optional[StrictInt] = Field(default=None, alias="InspectionRelationID")
    mi_validation_expires: Optional[StrictBool] = Field(default=None, alias="MIValidationExpires")
    mi_valid_until_date_calculation_method: Optional[StrictInt] = Field(default=None, description="0 = Default, 1 = EndOfMonth", alias="MIValidUntilDateCalculationMethod")
    mi_validation_time_schedule_id: Optional[StrictInt] = Field(default=None, alias="MIValidationTimeScheduleID")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["MIPlanID", "MIPlanSequenceID", "ObjectID", "TimeScheduleID", "NextRunCalculationMethod", "InActive", "InspectionPersonID", "InspectionRelationID", "MIValidationExpires", "MIValidUntilDateCalculationMethod", "MIValidationTimeScheduleID"]

    @field_validator('next_run_calculation_method')
    def next_run_calculation_method_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    @field_validator('mi_valid_until_date_calculation_method')
    def mi_valid_until_date_calculation_method_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1]):
            raise ValueError("must be one of enum values (0, 1)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateObjectMISchedule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if mi_plan_sequence_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_plan_sequence_id is None and "mi_plan_sequence_id" in self.model_fields_set:
            _dict['MIPlanSequenceID'] = None

        # set to None if time_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.time_schedule_id is None and "time_schedule_id" in self.model_fields_set:
            _dict['TimeScheduleID'] = None

        # set to None if next_run_calculation_method (nullable) is None
        # and model_fields_set contains the field
        if self.next_run_calculation_method is None and "next_run_calculation_method" in self.model_fields_set:
            _dict['NextRunCalculationMethod'] = None

        # set to None if in_active (nullable) is None
        # and model_fields_set contains the field
        if self.in_active is None and "in_active" in self.model_fields_set:
            _dict['InActive'] = None

        # set to None if inspection_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_person_id is None and "inspection_person_id" in self.model_fields_set:
            _dict['InspectionPersonID'] = None

        # set to None if inspection_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_relation_id is None and "inspection_relation_id" in self.model_fields_set:
            _dict['InspectionRelationID'] = None

        # set to None if mi_validation_expires (nullable) is None
        # and model_fields_set contains the field
        if self.mi_validation_expires is None and "mi_validation_expires" in self.model_fields_set:
            _dict['MIValidationExpires'] = None

        # set to None if mi_valid_until_date_calculation_method (nullable) is None
        # and model_fields_set contains the field
        if self.mi_valid_until_date_calculation_method is None and "mi_valid_until_date_calculation_method" in self.model_fields_set:
            _dict['MIValidUntilDateCalculationMethod'] = None

        # set to None if mi_validation_time_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.mi_validation_time_schedule_id is None and "mi_validation_time_schedule_id" in self.model_fields_set:
            _dict['MIValidationTimeScheduleID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateObjectMISchedule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "MIPlanID": obj.get("MIPlanID"),
            "MIPlanSequenceID": obj.get("MIPlanSequenceID"),
            "ObjectID": obj.get("ObjectID"),
            "TimeScheduleID": obj.get("TimeScheduleID"),
            "NextRunCalculationMethod": obj.get("NextRunCalculationMethod"),
            "InActive": obj.get("InActive"),
            "InspectionPersonID": obj.get("InspectionPersonID"),
            "InspectionRelationID": obj.get("InspectionRelationID"),
            "MIValidationExpires": obj.get("MIValidationExpires"),
            "MIValidUntilDateCalculationMethod": obj.get("MIValidUntilDateCalculationMethod"),
            "MIValidationTimeScheduleID": obj.get("MIValidationTimeScheduleID")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


