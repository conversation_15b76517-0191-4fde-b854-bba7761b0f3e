# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTORentalRate(BaseModel):
    """
    CentixAPIDTORentalRate
    """ # noqa: E501
    rental_rate_code_id: Optional[StrictInt] = Field(default=None, alias="RentalRateCodeID")
    effective_date: Optional[datetime] = Field(default=None, alias="EffectiveDate")
    rental_rate_day1: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay1")
    rental_rate_day2: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay2")
    rental_rate_day3: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay3")
    rental_rate_day4: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay4")
    rental_rate_day5: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay5")
    rental_rate_day6: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateDay6")
    rental_rate_week: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateWeek")
    rental_rate_month: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateMonth")
    rental_rate_quarter: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateQuarter")
    rental_rate_year: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="RentalRateYear")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["RentalRateCodeID", "EffectiveDate", "RentalRateDay1", "RentalRateDay2", "RentalRateDay3", "RentalRateDay4", "RentalRateDay5", "RentalRateDay6", "RentalRateWeek", "RentalRateMonth", "RentalRateQuarter", "RentalRateYear"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTORentalRate from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if rental_rate_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.rental_rate_code_id is None and "rental_rate_code_id" in self.model_fields_set:
            _dict['RentalRateCodeID'] = None

        # set to None if effective_date (nullable) is None
        # and model_fields_set contains the field
        if self.effective_date is None and "effective_date" in self.model_fields_set:
            _dict['EffectiveDate'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTORentalRate from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "RentalRateCodeID": obj.get("RentalRateCodeID"),
            "EffectiveDate": obj.get("EffectiveDate"),
            "RentalRateDay1": obj.get("RentalRateDay1"),
            "RentalRateDay2": obj.get("RentalRateDay2"),
            "RentalRateDay3": obj.get("RentalRateDay3"),
            "RentalRateDay4": obj.get("RentalRateDay4"),
            "RentalRateDay5": obj.get("RentalRateDay5"),
            "RentalRateDay6": obj.get("RentalRateDay6"),
            "RentalRateWeek": obj.get("RentalRateWeek"),
            "RentalRateMonth": obj.get("RentalRateMonth"),
            "RentalRateQuarter": obj.get("RentalRateQuarter"),
            "RentalRateYear": obj.get("RentalRateYear")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


