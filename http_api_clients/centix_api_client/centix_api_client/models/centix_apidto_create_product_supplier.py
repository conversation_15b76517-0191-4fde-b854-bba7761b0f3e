# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOCreateProductSupplier(BaseModel):
    """
    CentixAPIDTOCreateProductSupplier
    """ # noqa: E501
    product_id: StrictInt = Field(alias="ProductID")
    supplier_id: StrictInt = Field(alias="SupplierID")
    order_size: Optional[StrictInt] = Field(default=None, alias="OrderSize")
    order_level: Optional[StrictInt] = Field(default=None, alias="OrderLevel")
    deliver_time: Optional[StrictInt] = Field(default=None, alias="DeliverTime")
    price: Union[StrictFloat, StrictInt] = Field(alias="Price")
    currency_id: Optional[StrictInt] = Field(alias="CurrencyID")
    vat_id: Optional[StrictInt] = Field(alias="VatID")
    is_default_supplier: Optional[StrictBool] = Field(default=None, alias="IsDefaultSupplier")
    supplier_product_no: Optional[StrictStr] = Field(default=None, alias="SupplierProductNo")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ProductID", "SupplierID", "OrderSize", "OrderLevel", "DeliverTime", "Price", "CurrencyID", "VatID", "IsDefaultSupplier", "SupplierProductNo"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateProductSupplier from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateProductSupplier from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ProductID": obj.get("ProductID"),
            "SupplierID": obj.get("SupplierID"),
            "OrderSize": obj.get("OrderSize"),
            "OrderLevel": obj.get("OrderLevel"),
            "DeliverTime": obj.get("DeliverTime"),
            "Price": obj.get("Price"),
            "CurrencyID": obj.get("CurrencyID"),
            "VatID": obj.get("VatID"),
            "IsDefaultSupplier": obj.get("IsDefaultSupplier"),
            "SupplierProductNo": obj.get("SupplierProductNo")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


