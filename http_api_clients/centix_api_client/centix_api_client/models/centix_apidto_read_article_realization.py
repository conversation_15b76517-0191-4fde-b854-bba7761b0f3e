# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadArticleRealization(BaseModel):
    """
    CentixAPIDTOReadArticleRealization
    """ # noqa: E501
    product_id: Optional[StrictInt] = Field(default=None, alias="ProductID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    product_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductPrice")
    discount_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="DiscountPCT")
    vat_id: Optional[StrictInt] = Field(default=None, alias="VatID")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Price")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    sub_total: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotal")
    approved_by_person_id: Optional[StrictInt] = Field(default=None, alias="ApprovedByPersonID")
    approved_at: Optional[datetime] = Field(default=None, alias="ApprovedAt")
    processed_by_person_id: Optional[StrictInt] = Field(default=None, alias="ProcessedByPersonID")
    processed_at: Optional[datetime] = Field(default=None, alias="ProcessedAt")
    position_no: Optional[StrictInt] = Field(default=None, alias="PositionNo")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductCalcPrice")
    price_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceIncl")
    price_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceVatAmount")
    sub_total_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalIncl")
    sub_total_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalVatAmount")
    sub_total_article_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalArticleCalcPrice")
    work_flow_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowID")
    budget_code_id: Optional[StrictInt] = Field(default=None, alias="BudgetCodeID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ProductID", "Descr", "Quantity", "ProductPrice", "DiscountPCT", "VatID", "CurrencyID", "Price", "AdministrationID", "ProjectID", "CostCodeID", "SubTotal", "ApprovedByPersonID", "ApprovedAt", "ProcessedByPersonID", "ProcessedAt", "PositionNo", "CostCategoryID", "ProductCalcPrice", "PriceIncl", "PriceVatAmount", "SubTotalIncl", "SubTotalVatAmount", "SubTotalArticleCalcPrice", "WorkFlowID", "BudgetCodeID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadArticleRealization from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if product_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_price is None and "product_price" in self.model_fields_set:
            _dict['ProductPrice'] = None

        # set to None if discount_pct (nullable) is None
        # and model_fields_set contains the field
        if self.discount_pct is None and "discount_pct" in self.model_fields_set:
            _dict['DiscountPCT'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if price (nullable) is None
        # and model_fields_set contains the field
        if self.price is None and "price" in self.model_fields_set:
            _dict['Price'] = None

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if approved_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.approved_by_person_id is None and "approved_by_person_id" in self.model_fields_set:
            _dict['ApprovedByPersonID'] = None

        # set to None if approved_at (nullable) is None
        # and model_fields_set contains the field
        if self.approved_at is None and "approved_at" in self.model_fields_set:
            _dict['ApprovedAt'] = None

        # set to None if processed_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.processed_by_person_id is None and "processed_by_person_id" in self.model_fields_set:
            _dict['ProcessedByPersonID'] = None

        # set to None if processed_at (nullable) is None
        # and model_fields_set contains the field
        if self.processed_at is None and "processed_at" in self.model_fields_set:
            _dict['ProcessedAt'] = None

        # set to None if position_no (nullable) is None
        # and model_fields_set contains the field
        if self.position_no is None and "position_no" in self.model_fields_set:
            _dict['PositionNo'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if product_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_calc_price is None and "product_calc_price" in self.model_fields_set:
            _dict['ProductCalcPrice'] = None

        # set to None if price_incl (nullable) is None
        # and model_fields_set contains the field
        if self.price_incl is None and "price_incl" in self.model_fields_set:
            _dict['PriceIncl'] = None

        # set to None if price_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.price_vat_amount is None and "price_vat_amount" in self.model_fields_set:
            _dict['PriceVatAmount'] = None

        # set to None if sub_total_incl (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_incl is None and "sub_total_incl" in self.model_fields_set:
            _dict['SubTotalIncl'] = None

        # set to None if sub_total_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_vat_amount is None and "sub_total_vat_amount" in self.model_fields_set:
            _dict['SubTotalVatAmount'] = None

        # set to None if sub_total_article_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_article_calc_price is None and "sub_total_article_calc_price" in self.model_fields_set:
            _dict['SubTotalArticleCalcPrice'] = None

        # set to None if work_flow_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_id is None and "work_flow_id" in self.model_fields_set:
            _dict['WorkFlowID'] = None

        # set to None if budget_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.budget_code_id is None and "budget_code_id" in self.model_fields_set:
            _dict['BudgetCodeID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadArticleRealization from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ProductID": obj.get("ProductID"),
            "Descr": obj.get("Descr"),
            "Quantity": obj.get("Quantity"),
            "ProductPrice": obj.get("ProductPrice"),
            "DiscountPCT": obj.get("DiscountPCT"),
            "VatID": obj.get("VatID"),
            "CurrencyID": obj.get("CurrencyID"),
            "Price": obj.get("Price"),
            "AdministrationID": obj.get("AdministrationID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "SubTotal": obj.get("SubTotal"),
            "ApprovedByPersonID": obj.get("ApprovedByPersonID"),
            "ApprovedAt": obj.get("ApprovedAt"),
            "ProcessedByPersonID": obj.get("ProcessedByPersonID"),
            "ProcessedAt": obj.get("ProcessedAt"),
            "PositionNo": obj.get("PositionNo"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "ProductCalcPrice": obj.get("ProductCalcPrice"),
            "PriceIncl": obj.get("PriceIncl"),
            "PriceVatAmount": obj.get("PriceVatAmount"),
            "SubTotalIncl": obj.get("SubTotalIncl"),
            "SubTotalVatAmount": obj.get("SubTotalVatAmount"),
            "SubTotalArticleCalcPrice": obj.get("SubTotalArticleCalcPrice"),
            "WorkFlowID": obj.get("WorkFlowID"),
            "BudgetCodeID": obj.get("BudgetCodeID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


