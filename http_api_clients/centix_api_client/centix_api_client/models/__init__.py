# coding: utf-8

# flake8: noqa
"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


# import models into model package
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_absence_registration import CentixAPICoreListResultCentixAPIDTOReadAbsenceRegistration
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_brand_type_default_mi_schedule import CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_brand_type_revision_query import CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevisionQuery
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_device import CentixAPICoreListResultCentixAPIDTOReadDevice
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_device_event import CentixAPICoreListResultCentixAPIDTOReadDeviceEvent
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_document import CentixAPICoreListResultCentixAPIDTOReadDocument
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_e_invoice import CentixAPICoreListResultCentixAPIDTOReadEInvoice
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_e_invoice_line import CentixAPICoreListResultCentixAPIDTOReadEInvoiceLine
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_invoice import CentixAPICoreListResultCentixAPIDTOReadInvoice
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_invoice_line import CentixAPICoreListResultCentixAPIDTOReadInvoiceLine
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_journal_query import CentixAPICoreListResultCentixAPIDTOReadJournalQuery
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_location import CentixAPICoreListResultCentixAPIDTOReadLocation
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_location_user import CentixAPICoreListResultCentixAPIDTOReadLocationUser
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_mi import CentixAPICoreListResultCentixAPIDTOReadMI
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_measuring_device1_object_measurement_schedule import CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_measuring_device1_object_measurement_view import CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementView
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object import CentixAPICoreListResultCentixAPIDTOReadObject
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_code import CentixAPICoreListResultCentixAPIDTOReadObjectCode
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_event_log import CentixAPICoreListResultCentixAPIDTOReadObjectEventLog
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_location import CentixAPICoreListResultCentixAPIDTOReadObjectLocation
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_location_log import CentixAPICoreListResultCentixAPIDTOReadObjectLocationLog
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_mi_schedule import CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_mi_view import CentixAPICoreListResultCentixAPIDTOReadObjectMIView
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_type_default_mi_schedule import CentixAPICoreListResultCentixAPIDTOReadObjectTypeDefaultMISchedule
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_order_line import CentixAPICoreListResultCentixAPIDTOReadOrderLine
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_order_line_rental_log import CentixAPICoreListResultCentixAPIDTOReadOrderLineRentalLog
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_order_query import CentixAPICoreListResultCentixAPIDTOReadOrderQuery
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_pack_list_query import CentixAPICoreListResultCentixAPIDTOReadPackListQuery
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_person import CentixAPICoreListResultCentixAPIDTOReadPerson
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_product_supplier import CentixAPICoreListResultCentixAPIDTOReadProductSupplier
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_product_warehouse import CentixAPICoreListResultCentixAPIDTOReadProductWarehouse
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_relation import CentixAPICoreListResultCentixAPIDTOReadRelation
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_rental_product import CentixAPICoreListResultCentixAPIDTOReadRentalProduct
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_sales_product import CentixAPICoreListResultCentixAPIDTOReadSalesProduct
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_user import CentixAPICoreListResultCentixAPIDTOReadUser
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_workflow import CentixAPICoreListResultCentixAPIDTOReadWorkflow
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_working_ticket import CentixAPICoreListResultCentixAPIDTOReadWorkingTicket
from centix_api_client.models.centix_apidto_address import CentixAPIDTOAddress
from centix_api_client.models.centix_apidto_content_language_item import CentixAPIDTOContentLanguageItem
from centix_api_client.models.centix_apidto_create_absence_registration import CentixAPIDTOCreateAbsenceRegistration
from centix_api_client.models.centix_apidto_create_brand import CentixAPIDTOCreateBrand
from centix_api_client.models.centix_apidto_create_brand_type import CentixAPIDTOCreateBrandType
from centix_api_client.models.centix_apidto_create_brand_type_default_mi_schedule import CentixAPIDTOCreateBrandTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_create_brand_type_revision import CentixAPIDTOCreateBrandTypeRevision
from centix_api_client.models.centix_apidto_create_budget_code import CentixAPIDTOCreateBudgetCode
from centix_api_client.models.centix_apidto_create_cost_category import CentixAPIDTOCreateCostCategory
from centix_api_client.models.centix_apidto_create_cost_code import CentixAPIDTOCreateCostCode
from centix_api_client.models.centix_apidto_create_document_from_url import CentixAPIDTOCreateDocumentFromUrl
from centix_api_client.models.centix_apidto_create_location import CentixAPIDTOCreateLocation
from centix_api_client.models.centix_apidto_create_location_user import CentixAPIDTOCreateLocationUser
from centix_api_client.models.centix_apidto_create_object import CentixAPIDTOCreateObject
from centix_api_client.models.centix_apidto_create_object_code import CentixAPIDTOCreateObjectCode
from centix_api_client.models.centix_apidto_create_object_mi_schedule import CentixAPIDTOCreateObjectMISchedule
from centix_api_client.models.centix_apidto_create_object_measurement import CentixAPIDTOCreateObjectMeasurement
from centix_api_client.models.centix_apidto_create_object_measurement_function import CentixAPIDTOCreateObjectMeasurementFunction
from centix_api_client.models.centix_apidto_create_object_type_default_mi_schedule import CentixAPIDTOCreateObjectTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_create_order import CentixAPIDTOCreateOrder
from centix_api_client.models.centix_apidto_create_order_line import CentixAPIDTOCreateOrderLine
from centix_api_client.models.centix_apidto_create_person import CentixAPIDTOCreatePerson
from centix_api_client.models.centix_apidto_create_product_supplier import CentixAPIDTOCreateProductSupplier
from centix_api_client.models.centix_apidto_create_product_warehouse import CentixAPIDTOCreateProductWarehouse
from centix_api_client.models.centix_apidto_create_relation import CentixAPIDTOCreateRelation
from centix_api_client.models.centix_apidto_create_rental_product import CentixAPIDTOCreateRentalProduct
from centix_api_client.models.centix_apidto_create_sales_product import CentixAPIDTOCreateSalesProduct
from centix_api_client.models.centix_apidto_create_workflow import CentixAPIDTOCreateWorkflow
from centix_api_client.models.centix_apidto_execute_pick_and_pack_orderlines import CentixAPIDTOExecutePickAndPackOrderlines
from centix_api_client.models.centix_apidto_full_read_object import CentixAPIDTOFullReadObject
from centix_api_client.models.centix_apidto_full_read_rental_product import CentixAPIDTOFullReadRentalProduct
from centix_api_client.models.centix_apidto_full_read_sales_product import CentixAPIDTOFullReadSalesProduct
from centix_api_client.models.centix_apidto_move_object import CentixAPIDTOMoveObject
from centix_api_client.models.centix_apidto_pack_list_created_result import CentixAPIDTOPackListCreatedResult
from centix_api_client.models.centix_apidto_pick_and_pack_orderline import CentixAPIDTOPickAndPackOrderline
from centix_api_client.models.centix_apidto_process_journal import CentixAPIDTOProcessJournal
from centix_api_client.models.centix_apidto_read_absence_kind import CentixAPIDTOReadAbsenceKind
from centix_api_client.models.centix_apidto_read_absence_registration import CentixAPIDTOReadAbsenceRegistration
from centix_api_client.models.centix_apidto_read_administration import CentixAPIDTOReadAdministration
from centix_api_client.models.centix_apidto_read_article_realization import CentixAPIDTOReadArticleRealization
from centix_api_client.models.centix_apidto_read_block_reason import CentixAPIDTOReadBlockReason
from centix_api_client.models.centix_apidto_read_brand import CentixAPIDTOReadBrand
from centix_api_client.models.centix_apidto_read_brand_type import CentixAPIDTOReadBrandType
from centix_api_client.models.centix_apidto_read_brand_type_default_mi_schedule import CentixAPIDTOReadBrandTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_read_brand_type_revision import CentixAPIDTOReadBrandTypeRevision
from centix_api_client.models.centix_apidto_read_brand_type_revision_query import CentixAPIDTOReadBrandTypeRevisionQuery
from centix_api_client.models.centix_apidto_read_budget_code import CentixAPIDTOReadBudgetCode
from centix_api_client.models.centix_apidto_read_composed_product import CentixAPIDTOReadComposedProduct
from centix_api_client.models.centix_apidto_read_composed_product_part import CentixAPIDTOReadComposedProductPart
from centix_api_client.models.centix_apidto_read_condition import CentixAPIDTOReadCondition
from centix_api_client.models.centix_apidto_read_cost_category import CentixAPIDTOReadCostCategory
from centix_api_client.models.centix_apidto_read_cost_code import CentixAPIDTOReadCostCode
from centix_api_client.models.centix_apidto_read_country import CentixAPIDTOReadCountry
from centix_api_client.models.centix_apidto_read_country_state import CentixAPIDTOReadCountryState
from centix_api_client.models.centix_apidto_read_country_state_county import CentixAPIDTOReadCountryStateCounty
from centix_api_client.models.centix_apidto_read_currency import CentixAPIDTOReadCurrency
from centix_api_client.models.centix_apidto_read_device import CentixAPIDTOReadDevice
from centix_api_client.models.centix_apidto_read_device_event import CentixAPIDTOReadDeviceEvent
from centix_api_client.models.centix_apidto_read_document import CentixAPIDTOReadDocument
from centix_api_client.models.centix_apidto_read_document_kind import CentixAPIDTOReadDocumentKind
from centix_api_client.models.centix_apidto_read_e_invoice import CentixAPIDTOReadEInvoice
from centix_api_client.models.centix_apidto_read_e_invoice_line import CentixAPIDTOReadEInvoiceLine
from centix_api_client.models.centix_apidto_read_field_dictionary import CentixAPIDTOReadFieldDictionary
from centix_api_client.models.centix_apidto_read_fixed_asset_group import CentixAPIDTOReadFixedAssetGroup
from centix_api_client.models.centix_apidto_read_general_ledger import CentixAPIDTOReadGeneralLedger
from centix_api_client.models.centix_apidto_read_hs_code import CentixAPIDTOReadHSCode
from centix_api_client.models.centix_apidto_read_hour_registration import CentixAPIDTOReadHourRegistration
from centix_api_client.models.centix_apidto_read_invoice import CentixAPIDTOReadInvoice
from centix_api_client.models.centix_apidto_read_invoice_line import CentixAPIDTOReadInvoiceLine
from centix_api_client.models.centix_apidto_read_journal import CentixAPIDTOReadJournal
from centix_api_client.models.centix_apidto_read_journal_line import CentixAPIDTOReadJournalLine
from centix_api_client.models.centix_apidto_read_journal_query import CentixAPIDTOReadJournalQuery
from centix_api_client.models.centix_apidto_read_language import CentixAPIDTOReadLanguage
from centix_api_client.models.centix_apidto_read_location import CentixAPIDTOReadLocation
from centix_api_client.models.centix_apidto_read_location_group import CentixAPIDTOReadLocationGroup
from centix_api_client.models.centix_apidto_read_location_status import CentixAPIDTOReadLocationStatus
from centix_api_client.models.centix_apidto_read_location_type import CentixAPIDTOReadLocationType
from centix_api_client.models.centix_apidto_read_location_user import CentixAPIDTOReadLocationUser
from centix_api_client.models.centix_apidto_read_lock_reason import CentixAPIDTOReadLockReason
from centix_api_client.models.centix_apidto_read_mi import CentixAPIDTOReadMI
from centix_api_client.models.centix_apidto_read_mi_plan import CentixAPIDTOReadMIPlan
from centix_api_client.models.centix_apidto_read_measuring_device1_measurement import CentixAPIDTOReadMeasuringDevice1Measurement
from centix_api_client.models.centix_apidto_read_measuring_device1_measurement_function import CentixAPIDTOReadMeasuringDevice1MeasurementFunction
from centix_api_client.models.centix_apidto_read_measuring_device1_object_measurement_schedule import CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule
from centix_api_client.models.centix_apidto_read_measuring_device1_object_measurement_view import CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView
from centix_api_client.models.centix_apidto_read_no_signature_reason import CentixAPIDTOReadNoSignatureReason
from centix_api_client.models.centix_apidto_read_object import CentixAPIDTOReadObject
from centix_api_client.models.centix_apidto_read_object_code import CentixAPIDTOReadObjectCode
from centix_api_client.models.centix_apidto_read_object_event_log import CentixAPIDTOReadObjectEventLog
from centix_api_client.models.centix_apidto_read_object_group import CentixAPIDTOReadObjectGroup
from centix_api_client.models.centix_apidto_read_object_location import CentixAPIDTOReadObjectLocation
from centix_api_client.models.centix_apidto_read_object_location_log import CentixAPIDTOReadObjectLocationLog
from centix_api_client.models.centix_apidto_read_object_mi import CentixAPIDTOReadObjectMI
from centix_api_client.models.centix_apidto_read_object_mi_schedule import CentixAPIDTOReadObjectMISchedule
from centix_api_client.models.centix_apidto_read_object_mi_view import CentixAPIDTOReadObjectMIView
from centix_api_client.models.centix_apidto_read_object_status import CentixAPIDTOReadObjectStatus
from centix_api_client.models.centix_apidto_read_object_type import CentixAPIDTOReadObjectType
from centix_api_client.models.centix_apidto_read_object_type_default_mi_schedule import CentixAPIDTOReadObjectTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_read_order import CentixAPIDTOReadOrder
from centix_api_client.models.centix_apidto_read_order_line import CentixAPIDTOReadOrderLine
from centix_api_client.models.centix_apidto_read_order_line_rental_log import CentixAPIDTOReadOrderLineRentalLog
from centix_api_client.models.centix_apidto_read_order_query import CentixAPIDTOReadOrderQuery
from centix_api_client.models.centix_apidto_read_pack_list import CentixAPIDTOReadPackList
from centix_api_client.models.centix_apidto_read_pack_list_item import CentixAPIDTOReadPackListItem
from centix_api_client.models.centix_apidto_read_pack_list_item_object import CentixAPIDTOReadPackListItemObject
from centix_api_client.models.centix_apidto_read_pack_list_query import CentixAPIDTOReadPackListQuery
from centix_api_client.models.centix_apidto_read_payment_condition import CentixAPIDTOReadPaymentCondition
from centix_api_client.models.centix_apidto_read_person import CentixAPIDTOReadPerson
from centix_api_client.models.centix_apidto_read_person_status import CentixAPIDTOReadPersonStatus
from centix_api_client.models.centix_apidto_read_plan_block import CentixAPIDTOReadPlanBlock
from centix_api_client.models.centix_apidto_read_product_category import CentixAPIDTOReadProductCategory
from centix_api_client.models.centix_apidto_read_product_group import CentixAPIDTOReadProductGroup
from centix_api_client.models.centix_apidto_read_product_line import CentixAPIDTOReadProductLine
from centix_api_client.models.centix_apidto_read_product_status import CentixAPIDTOReadProductStatus
from centix_api_client.models.centix_apidto_read_product_supplier import CentixAPIDTOReadProductSupplier
from centix_api_client.models.centix_apidto_read_product_unit import CentixAPIDTOReadProductUnit
from centix_api_client.models.centix_apidto_read_product_warehouse import CentixAPIDTOReadProductWarehouse
from centix_api_client.models.centix_apidto_read_project import CentixAPIDTOReadProject
from centix_api_client.models.centix_apidto_read_project_status import CentixAPIDTOReadProjectStatus
from centix_api_client.models.centix_apidto_read_project_type import CentixAPIDTOReadProjectType
from centix_api_client.models.centix_apidto_read_reason import CentixAPIDTOReadReason
from centix_api_client.models.centix_apidto_read_region import CentixAPIDTOReadRegion
from centix_api_client.models.centix_apidto_read_relation import CentixAPIDTOReadRelation
from centix_api_client.models.centix_apidto_read_relation_group import CentixAPIDTOReadRelationGroup
from centix_api_client.models.centix_apidto_read_relation_status import CentixAPIDTOReadRelationStatus
from centix_api_client.models.centix_apidto_read_rental_product import CentixAPIDTOReadRentalProduct
from centix_api_client.models.centix_apidto_read_sales_product import CentixAPIDTOReadSalesProduct
from centix_api_client.models.centix_apidto_read_stock_indication import CentixAPIDTOReadStockIndication
from centix_api_client.models.centix_apidto_read_team import CentixAPIDTOReadTeam
from centix_api_client.models.centix_apidto_read_time_schedule import CentixAPIDTOReadTimeSchedule
from centix_api_client.models.centix_apidto_read_title import CentixAPIDTOReadTitle
from centix_api_client.models.centix_apidto_read_transport_method import CentixAPIDTOReadTransportMethod
from centix_api_client.models.centix_apidto_read_user import CentixAPIDTOReadUser
from centix_api_client.models.centix_apidto_read_vat import CentixAPIDTOReadVat
from centix_api_client.models.centix_apidto_read_warehouse import CentixAPIDTOReadWarehouse
from centix_api_client.models.centix_apidto_read_warehouse_pickzone import CentixAPIDTOReadWarehousePickzone
from centix_api_client.models.centix_apidto_read_work_flow_group import CentixAPIDTOReadWorkFlowGroup
from centix_api_client.models.centix_apidto_read_work_flow_status import CentixAPIDTOReadWorkFlowStatus
from centix_api_client.models.centix_apidto_read_work_flow_status_log import CentixAPIDTOReadWorkFlowStatusLog
from centix_api_client.models.centix_apidto_read_work_flow_sub_group import CentixAPIDTOReadWorkFlowSubGroup
from centix_api_client.models.centix_apidto_read_workflow import CentixAPIDTOReadWorkflow
from centix_api_client.models.centix_apidto_read_working_ticket import CentixAPIDTOReadWorkingTicket
from centix_api_client.models.centix_apidto_read_working_ticket_status import CentixAPIDTOReadWorkingTicketStatus
from centix_api_client.models.centix_apidto_rental_rate import CentixAPIDTORentalRate
from centix_api_client.models.centix_apidto_update_absence_registration import CentixAPIDTOUpdateAbsenceRegistration
from centix_api_client.models.centix_apidto_update_brand import CentixAPIDTOUpdateBrand
from centix_api_client.models.centix_apidto_update_brand_type import CentixAPIDTOUpdateBrandType
from centix_api_client.models.centix_apidto_update_brand_type_default_mi_schedule import CentixAPIDTOUpdateBrandTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_update_brand_type_revision import CentixAPIDTOUpdateBrandTypeRevision
from centix_api_client.models.centix_apidto_update_budget_code import CentixAPIDTOUpdateBudgetCode
from centix_api_client.models.centix_apidto_update_cost_category import CentixAPIDTOUpdateCostCategory
from centix_api_client.models.centix_apidto_update_cost_code import CentixAPIDTOUpdateCostCode
from centix_api_client.models.centix_apidto_update_document import CentixAPIDTOUpdateDocument
from centix_api_client.models.centix_apidto_update_location import CentixAPIDTOUpdateLocation
from centix_api_client.models.centix_apidto_update_object import CentixAPIDTOUpdateObject
from centix_api_client.models.centix_apidto_update_object_code import CentixAPIDTOUpdateObjectCode
from centix_api_client.models.centix_apidto_update_object_type_default_mi_schedule import CentixAPIDTOUpdateObjectTypeDefaultMISchedule
from centix_api_client.models.centix_apidto_update_person import CentixAPIDTOUpdatePerson
from centix_api_client.models.centix_apidto_update_product_supplier import CentixAPIDTOUpdateProductSupplier
from centix_api_client.models.centix_apidto_update_product_warehouse import CentixAPIDTOUpdateProductWarehouse
from centix_api_client.models.centix_apidto_update_relation import CentixAPIDTOUpdateRelation
from centix_api_client.models.centix_apidto_update_rental_product import CentixAPIDTOUpdateRentalProduct
from centix_api_client.models.centix_apidto_update_sales_product import CentixAPIDTOUpdateSalesProduct
from centix_api_client.models.centix_apidto_update_workflow import CentixAPIDTOUpdateWorkflow
from centix_api_client.models.centix_api_validation_error import CentixAPIValidationError
from centix_api_client.models.centix_api_validation_result import CentixAPIValidationResult
