# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from centix_api_client.models.centix_apidto_read_pack_list_item_object import CentixAPIDTOReadPackListItemObject
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadPackListItem(BaseModel):
    """
    CentixAPIDTOReadPackListItem
    """ # noqa: E501
    pack_list_id: Optional[StrictInt] = Field(default=None, alias="PackListID")
    product_id: Optional[StrictInt] = Field(default=None, alias="ProductID")
    product_descr: Optional[StrictStr] = Field(default=None, alias="ProductDescr")
    product_unit_id: Optional[StrictInt] = Field(default=None, alias="ProductUnitID")
    is_stock_product: Optional[StrictBool] = Field(default=None, alias="IsStockProduct")
    order_line_id: Optional[StrictInt] = Field(default=None, alias="OrderLineID")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    picked_quantity: Optional[StrictInt] = Field(default=None, alias="PickedQuantity")
    invoiced: Optional[StrictBool] = Field(default=None, alias="Invoiced")
    is_administrative_product: Optional[StrictBool] = Field(default=None, alias="IsAdministrativeProduct")
    invoice_line_id: Optional[StrictInt] = Field(default=None, alias="InvoiceLineID")
    rental_object_entry_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectEntryQuantity")
    rental_object_return_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectReturnQuantity")
    pack_list_item_objects: Optional[List[CentixAPIDTOReadPackListItemObject]] = Field(default=None, alias="PackListItemObjects")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["PackListID", "ProductID", "ProductDescr", "ProductUnitID", "IsStockProduct", "OrderLineID", "Quantity", "PickedQuantity", "Invoiced", "IsAdministrativeProduct", "InvoiceLineID", "RentalObjectEntryQuantity", "RentalObjectReturnQuantity", "PackListItemObjects", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackListItem from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in pack_list_item_objects (list)
        _items = []
        if self.pack_list_item_objects:
            for _item_pack_list_item_objects in self.pack_list_item_objects:
                if _item_pack_list_item_objects:
                    _items.append(_item_pack_list_item_objects.to_dict())
            _dict['PackListItemObjects'] = _items
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if pack_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.pack_list_id is None and "pack_list_id" in self.model_fields_set:
            _dict['PackListID'] = None

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if product_unit_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_unit_id is None and "product_unit_id" in self.model_fields_set:
            _dict['ProductUnitID'] = None

        # set to None if is_stock_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_stock_product is None and "is_stock_product" in self.model_fields_set:
            _dict['IsStockProduct'] = None

        # set to None if order_line_id (nullable) is None
        # and model_fields_set contains the field
        if self.order_line_id is None and "order_line_id" in self.model_fields_set:
            _dict['OrderLineID'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if picked_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.picked_quantity is None and "picked_quantity" in self.model_fields_set:
            _dict['PickedQuantity'] = None

        # set to None if invoiced (nullable) is None
        # and model_fields_set contains the field
        if self.invoiced is None and "invoiced" in self.model_fields_set:
            _dict['Invoiced'] = None

        # set to None if is_administrative_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_administrative_product is None and "is_administrative_product" in self.model_fields_set:
            _dict['IsAdministrativeProduct'] = None

        # set to None if invoice_line_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_line_id is None and "invoice_line_id" in self.model_fields_set:
            _dict['InvoiceLineID'] = None

        # set to None if rental_object_entry_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_entry_quantity is None and "rental_object_entry_quantity" in self.model_fields_set:
            _dict['RentalObjectEntryQuantity'] = None

        # set to None if rental_object_return_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_return_quantity is None and "rental_object_return_quantity" in self.model_fields_set:
            _dict['RentalObjectReturnQuantity'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadPackListItem from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "PackListID": obj.get("PackListID"),
            "ProductID": obj.get("ProductID"),
            "ProductDescr": obj.get("ProductDescr"),
            "ProductUnitID": obj.get("ProductUnitID"),
            "IsStockProduct": obj.get("IsStockProduct"),
            "OrderLineID": obj.get("OrderLineID"),
            "Quantity": obj.get("Quantity"),
            "PickedQuantity": obj.get("PickedQuantity"),
            "Invoiced": obj.get("Invoiced"),
            "IsAdministrativeProduct": obj.get("IsAdministrativeProduct"),
            "InvoiceLineID": obj.get("InvoiceLineID"),
            "RentalObjectEntryQuantity": obj.get("RentalObjectEntryQuantity"),
            "RentalObjectReturnQuantity": obj.get("RentalObjectReturnQuantity"),
            "PackListItemObjects": [CentixAPIDTOReadPackListItemObject.from_dict(_item) for _item in obj["PackListItemObjects"]] if obj.get("PackListItemObjects") is not None else None,
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


