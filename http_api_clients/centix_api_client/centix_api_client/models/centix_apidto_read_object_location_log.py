# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectLocationLog(BaseModel):
    """
    CentixAPIDTOReadObjectLocationLog
    """ # noqa: E501
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    user_id: Optional[StrictInt] = Field(default=None, alias="UserID")
    log_kind: Optional[StrictInt] = Field(default=None, description="1 = Create, 2 = Move, 3 = Remove", alias="LogKind")
    log_date: Optional[datetime] = Field(default=None, alias="LogDate")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    relation_descr: Optional[StrictStr] = Field(default=None, alias="RelationDescr")
    location_id: Optional[StrictInt] = Field(default=None, alias="LocationID")
    location_descr: Optional[StrictStr] = Field(default=None, alias="LocationDescr")
    to_relation_id: Optional[StrictInt] = Field(default=None, alias="ToRelationID")
    to_relation_descr: Optional[StrictStr] = Field(default=None, alias="ToRelationDescr")
    to_location_id: Optional[StrictInt] = Field(default=None, alias="ToLocationID")
    to_location_descr: Optional[StrictStr] = Field(default=None, alias="ToLocationDescr")
    log_comment: Optional[StrictStr] = Field(default=None, alias="LogComment")
    move_basket_id: Optional[StrictStr] = Field(default=None, alias="MoveBasketID")
    location_id: Optional[StrictStr] = Field(default=None, alias="Location_ID")
    location_descr2: Optional[StrictStr] = Field(default=None, alias="LocationDescr2")
    to_location_id: Optional[StrictStr] = Field(default=None, alias="ToLocation_ID")
    to_location_descr2: Optional[StrictStr] = Field(default=None, alias="ToLocationDescr2")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ObjectID", "Quantity", "UserID", "LogKind", "LogDate", "RelationID", "RelationDescr", "LocationID", "LocationDescr", "ToRelationID", "ToRelationDescr", "ToLocationID", "ToLocationDescr", "LogComment", "MoveBasketID", "Location_ID", "LocationDescr2", "ToLocation_ID", "ToLocationDescr2", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('log_kind')
    def log_kind_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3]):
            raise ValueError("must be one of enum values (1, 2, 3)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectLocationLog from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if quantity (nullable) is None
        # and model_fields_set contains the field
        if self.quantity is None and "quantity" in self.model_fields_set:
            _dict['Quantity'] = None

        # set to None if user_id (nullable) is None
        # and model_fields_set contains the field
        if self.user_id is None and "user_id" in self.model_fields_set:
            _dict['UserID'] = None

        # set to None if log_kind (nullable) is None
        # and model_fields_set contains the field
        if self.log_kind is None and "log_kind" in self.model_fields_set:
            _dict['LogKind'] = None

        # set to None if log_date (nullable) is None
        # and model_fields_set contains the field
        if self.log_date is None and "log_date" in self.model_fields_set:
            _dict['LogDate'] = None

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if location_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_id is None and "location_id" in self.model_fields_set:
            _dict['LocationID'] = None

        # set to None if to_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.to_relation_id is None and "to_relation_id" in self.model_fields_set:
            _dict['ToRelationID'] = None

        # set to None if to_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.to_location_id is None and "to_location_id" in self.model_fields_set:
            _dict['ToLocationID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectLocationLog from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ObjectID": obj.get("ObjectID"),
            "Quantity": obj.get("Quantity"),
            "UserID": obj.get("UserID"),
            "LogKind": obj.get("LogKind"),
            "LogDate": obj.get("LogDate"),
            "RelationID": obj.get("RelationID"),
            "RelationDescr": obj.get("RelationDescr"),
            "LocationID": obj.get("LocationID"),
            "LocationDescr": obj.get("LocationDescr"),
            "ToRelationID": obj.get("ToRelationID"),
            "ToRelationDescr": obj.get("ToRelationDescr"),
            "ToLocationID": obj.get("ToLocationID"),
            "ToLocationDescr": obj.get("ToLocationDescr"),
            "LogComment": obj.get("LogComment"),
            "MoveBasketID": obj.get("MoveBasketID"),
            "Location_ID": obj.get("Location_ID"),
            "LocationDescr2": obj.get("LocationDescr2"),
            "ToLocation_ID": obj.get("ToLocation_ID"),
            "ToLocationDescr2": obj.get("ToLocationDescr2"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


