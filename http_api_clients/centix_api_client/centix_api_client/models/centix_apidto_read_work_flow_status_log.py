# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadWorkFlowStatusLog(BaseModel):
    """
    CentixAPIDTOReadWorkFlowStatusLog
    """ # noqa: E501
    work_flow_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowID")
    user_id: Optional[StrictInt] = Field(default=None, alias="UserID")
    log_date: Optional[datetime] = Field(default=None, alias="LogDate")
    status_id: Optional[StrictInt] = Field(default=None, alias="StatusID")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    execute_at: Optional[datetime] = Field(default=None, alias="ExecuteAt")
    copy_send_to: Optional[StrictStr] = Field(default=None, alias="CopySendTo")
    copy_send_status: Optional[StrictInt] = Field(default=None, alias="CopySendStatus")
    copy_send_date: Optional[datetime] = Field(default=None, alias="CopySendDate")
    copy_send_error: Optional[StrictStr] = Field(default=None, alias="CopySendError")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    event: Optional[StrictInt] = Field(default=None, description="0 = StatusChange, 1 = EmployeeChange, 2 = GroupChange, 3 = SubGroupChange, 4 = PriorityChange", alias="Event")
    person_id: Optional[StrictInt] = Field(default=None, alias="PersonID")
    work_flow_group_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowGroupID")
    work_flow_sub_group_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowSubGroupID")
    work_flow_priority_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowPriorityID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["WorkFlowID", "UserID", "LogDate", "StatusID", "RelationID", "ExecuteAt", "CopySendTo", "CopySendStatus", "CopySendDate", "CopySendError", "AdministrationID", "Event", "PersonID", "WorkFlowGroupID", "WorkFlowSubGroupID", "WorkFlowPriorityID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('event')
    def event_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWorkFlowStatusLog from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if user_id (nullable) is None
        # and model_fields_set contains the field
        if self.user_id is None and "user_id" in self.model_fields_set:
            _dict['UserID'] = None

        # set to None if log_date (nullable) is None
        # and model_fields_set contains the field
        if self.log_date is None and "log_date" in self.model_fields_set:
            _dict['LogDate'] = None

        # set to None if status_id (nullable) is None
        # and model_fields_set contains the field
        if self.status_id is None and "status_id" in self.model_fields_set:
            _dict['StatusID'] = None

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if execute_at (nullable) is None
        # and model_fields_set contains the field
        if self.execute_at is None and "execute_at" in self.model_fields_set:
            _dict['ExecuteAt'] = None

        # set to None if copy_send_status (nullable) is None
        # and model_fields_set contains the field
        if self.copy_send_status is None and "copy_send_status" in self.model_fields_set:
            _dict['CopySendStatus'] = None

        # set to None if copy_send_date (nullable) is None
        # and model_fields_set contains the field
        if self.copy_send_date is None and "copy_send_date" in self.model_fields_set:
            _dict['CopySendDate'] = None

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if event (nullable) is None
        # and model_fields_set contains the field
        if self.event is None and "event" in self.model_fields_set:
            _dict['Event'] = None

        # set to None if person_id (nullable) is None
        # and model_fields_set contains the field
        if self.person_id is None and "person_id" in self.model_fields_set:
            _dict['PersonID'] = None

        # set to None if work_flow_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_group_id is None and "work_flow_group_id" in self.model_fields_set:
            _dict['WorkFlowGroupID'] = None

        # set to None if work_flow_sub_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_sub_group_id is None and "work_flow_sub_group_id" in self.model_fields_set:
            _dict['WorkFlowSubGroupID'] = None

        # set to None if work_flow_priority_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_priority_id is None and "work_flow_priority_id" in self.model_fields_set:
            _dict['WorkFlowPriorityID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadWorkFlowStatusLog from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "WorkFlowID": obj.get("WorkFlowID"),
            "UserID": obj.get("UserID"),
            "LogDate": obj.get("LogDate"),
            "StatusID": obj.get("StatusID"),
            "RelationID": obj.get("RelationID"),
            "ExecuteAt": obj.get("ExecuteAt"),
            "CopySendTo": obj.get("CopySendTo"),
            "CopySendStatus": obj.get("CopySendStatus"),
            "CopySendDate": obj.get("CopySendDate"),
            "CopySendError": obj.get("CopySendError"),
            "AdministrationID": obj.get("AdministrationID"),
            "Event": obj.get("Event"),
            "PersonID": obj.get("PersonID"),
            "WorkFlowGroupID": obj.get("WorkFlowGroupID"),
            "WorkFlowSubGroupID": obj.get("WorkFlowSubGroupID"),
            "WorkFlowPriorityID": obj.get("WorkFlowPriorityID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


