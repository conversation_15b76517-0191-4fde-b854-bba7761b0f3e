# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateObject(BaseModel):
    """
    CentixAPIDTOUpdateObject
    """ # noqa: E501
    aidc: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="AIDC")
    brand_id: Optional[StrictInt] = Field(default=None, alias="BrandID")
    condition_id: Optional[StrictInt] = Field(default=None, alias="ConditionID")
    construction_year: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="ConstructionYear")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    descr: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Descr")
    descr2: Optional[StrictStr] = Field(default=None, alias="Descr2")
    id: Annotated[str, Field(min_length=0, strict=True, max_length=30)] = Field(alias="ID")
    object_owner_id: StrictInt = Field(alias="ObjectOwnerID")
    object_status_id: StrictInt = Field(alias="ObjectStatusID")
    owner_reference: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="OwnerReference")
    revision_id: Optional[StrictInt] = Field(default=None, alias="RevisionID")
    search_tag: Optional[StrictStr] = Field(default=None, alias="SearchTag")
    selection_code: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="SelectionCode")
    send_alert: Optional[StrictBool] = Field(default=None, alias="SendAlert")
    serial_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="SerialNo")
    type_id: Optional[StrictInt] = Field(default=None, alias="TypeID")
    supplier_id: Optional[StrictInt] = Field(default=None, alias="SupplierID")
    supplier_article_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=30)]] = Field(default=None, alias="SupplierArticleNo")
    purchase_date: Optional[datetime] = Field(default=None, alias="PurchaseDate")
    purchase_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PurchasePrice")
    purchase_currency_id: Optional[StrictInt] = Field(default=None, alias="PurchaseCurrencyID")
    purchase_order: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="PurchaseOrder")
    country_of_origin_id: Optional[StrictInt] = Field(default=None, alias="CountryOfOriginID")
    hs_code_id: Optional[StrictInt] = Field(default=None, alias="HSCodeID")
    note: Optional[StrictStr] = Field(default=None, alias="Note")
    base_location_id: Optional[StrictInt] = Field(default=None, alias="BaseLocationID")
    end_of_life_date: Optional[datetime] = Field(default=None, alias="EndOfLifeDate")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["AIDC", "BrandID", "ConditionID", "ConstructionYear", "CostCategoryID", "CostCodeID", "Descr", "Descr2", "ID", "ObjectOwnerID", "ObjectStatusID", "OwnerReference", "RevisionID", "SearchTag", "SelectionCode", "SendAlert", "SerialNo", "TypeID", "SupplierID", "SupplierArticleNo", "PurchaseDate", "PurchasePrice", "PurchaseCurrencyID", "PurchaseOrder", "CountryOfOriginID", "HSCodeID", "Note", "BaseLocationID", "EndOfLifeDate"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateObject from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if brand_id (nullable) is None
        # and model_fields_set contains the field
        if self.brand_id is None and "brand_id" in self.model_fields_set:
            _dict['BrandID'] = None

        # set to None if condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.condition_id is None and "condition_id" in self.model_fields_set:
            _dict['ConditionID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if revision_id (nullable) is None
        # and model_fields_set contains the field
        if self.revision_id is None and "revision_id" in self.model_fields_set:
            _dict['RevisionID'] = None

        # set to None if type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_id is None and "type_id" in self.model_fields_set:
            _dict['TypeID'] = None

        # set to None if supplier_id (nullable) is None
        # and model_fields_set contains the field
        if self.supplier_id is None and "supplier_id" in self.model_fields_set:
            _dict['SupplierID'] = None

        # set to None if purchase_date (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_date is None and "purchase_date" in self.model_fields_set:
            _dict['PurchaseDate'] = None

        # set to None if purchase_price (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_price is None and "purchase_price" in self.model_fields_set:
            _dict['PurchasePrice'] = None

        # set to None if purchase_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.purchase_currency_id is None and "purchase_currency_id" in self.model_fields_set:
            _dict['PurchaseCurrencyID'] = None

        # set to None if country_of_origin_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_of_origin_id is None and "country_of_origin_id" in self.model_fields_set:
            _dict['CountryOfOriginID'] = None

        # set to None if hs_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.hs_code_id is None and "hs_code_id" in self.model_fields_set:
            _dict['HSCodeID'] = None

        # set to None if base_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.base_location_id is None and "base_location_id" in self.model_fields_set:
            _dict['BaseLocationID'] = None

        # set to None if end_of_life_date (nullable) is None
        # and model_fields_set contains the field
        if self.end_of_life_date is None and "end_of_life_date" in self.model_fields_set:
            _dict['EndOfLifeDate'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateObject from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "AIDC": obj.get("AIDC"),
            "BrandID": obj.get("BrandID"),
            "ConditionID": obj.get("ConditionID"),
            "ConstructionYear": obj.get("ConstructionYear"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "Descr": obj.get("Descr"),
            "Descr2": obj.get("Descr2"),
            "ID": obj.get("ID"),
            "ObjectOwnerID": obj.get("ObjectOwnerID"),
            "ObjectStatusID": obj.get("ObjectStatusID"),
            "OwnerReference": obj.get("OwnerReference"),
            "RevisionID": obj.get("RevisionID"),
            "SearchTag": obj.get("SearchTag"),
            "SelectionCode": obj.get("SelectionCode"),
            "SendAlert": obj.get("SendAlert"),
            "SerialNo": obj.get("SerialNo"),
            "TypeID": obj.get("TypeID"),
            "SupplierID": obj.get("SupplierID"),
            "SupplierArticleNo": obj.get("SupplierArticleNo"),
            "PurchaseDate": obj.get("PurchaseDate"),
            "PurchasePrice": obj.get("PurchasePrice"),
            "PurchaseCurrencyID": obj.get("PurchaseCurrencyID"),
            "PurchaseOrder": obj.get("PurchaseOrder"),
            "CountryOfOriginID": obj.get("CountryOfOriginID"),
            "HSCodeID": obj.get("HSCodeID"),
            "Note": obj.get("Note"),
            "BaseLocationID": obj.get("BaseLocationID"),
            "EndOfLifeDate": obj.get("EndOfLifeDate")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


