# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadProject(BaseModel):
    """
    CentixAPIDTOReadProject
    """ # noqa: E501
    type_id: Optional[StrictInt] = Field(default=None, alias="TypeID")
    administration_id: Optional[StrictInt] = Field(default=None, alias="AdministrationID")
    relation_id: Optional[StrictInt] = Field(default=None, alias="RelationID")
    start_date: Optional[datetime] = Field(default=None, alias="StartDate")
    end_date: Optional[datetime] = Field(default=None, alias="EndDate")
    completed: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Completed")
    status_id: Optional[StrictInt] = Field(default=None, alias="StatusID")
    internal_project: Optional[StrictBool] = Field(default=None, alias="InternalProject")
    invoice_method: Optional[StrictInt] = Field(default=None, alias="InvoiceMethod")
    parent_project_id: Optional[StrictInt] = Field(default=None, alias="ParentProjectID")
    use_invoice_terms: Optional[StrictBool] = Field(default=None, alias="UseInvoiceTerms")
    invoice_on_progress: Optional[StrictBool] = Field(default=None, alias="InvoiceOnProgress")
    invoiced_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="InvoicedAmount")
    last_invoice_date: Optional[datetime] = Field(default=None, alias="LastInvoiceDate")
    charge: Optional[StrictBool] = Field(default=None, alias="Charge")
    assigned_to_person_id: Optional[StrictInt] = Field(default=None, alias="AssignedToPersonID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    budget_code_required: Optional[StrictBool] = Field(default=None, alias="BudgetCodeRequired")
    allow_project_budget_codes_only: Optional[StrictBool] = Field(default=None, alias="AllowProjectBudgetCodesOnly")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["TypeID", "AdministrationID", "RelationID", "StartDate", "EndDate", "Completed", "StatusID", "InternalProject", "InvoiceMethod", "ParentProjectID", "UseInvoiceTerms", "InvoiceOnProgress", "InvoicedAmount", "LastInvoiceDate", "Charge", "AssignedToPersonID", "InternalNote", "ExternalNote", "BudgetCodeRequired", "AllowProjectBudgetCodesOnly", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadProject from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if type_id (nullable) is None
        # and model_fields_set contains the field
        if self.type_id is None and "type_id" in self.model_fields_set:
            _dict['TypeID'] = None

        # set to None if administration_id (nullable) is None
        # and model_fields_set contains the field
        if self.administration_id is None and "administration_id" in self.model_fields_set:
            _dict['AdministrationID'] = None

        # set to None if relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_id is None and "relation_id" in self.model_fields_set:
            _dict['RelationID'] = None

        # set to None if start_date (nullable) is None
        # and model_fields_set contains the field
        if self.start_date is None and "start_date" in self.model_fields_set:
            _dict['StartDate'] = None

        # set to None if end_date (nullable) is None
        # and model_fields_set contains the field
        if self.end_date is None and "end_date" in self.model_fields_set:
            _dict['EndDate'] = None

        # set to None if completed (nullable) is None
        # and model_fields_set contains the field
        if self.completed is None and "completed" in self.model_fields_set:
            _dict['Completed'] = None

        # set to None if status_id (nullable) is None
        # and model_fields_set contains the field
        if self.status_id is None and "status_id" in self.model_fields_set:
            _dict['StatusID'] = None

        # set to None if invoice_method (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_method is None and "invoice_method" in self.model_fields_set:
            _dict['InvoiceMethod'] = None

        # set to None if parent_project_id (nullable) is None
        # and model_fields_set contains the field
        if self.parent_project_id is None and "parent_project_id" in self.model_fields_set:
            _dict['ParentProjectID'] = None

        # set to None if use_invoice_terms (nullable) is None
        # and model_fields_set contains the field
        if self.use_invoice_terms is None and "use_invoice_terms" in self.model_fields_set:
            _dict['UseInvoiceTerms'] = None

        # set to None if invoice_on_progress (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_on_progress is None and "invoice_on_progress" in self.model_fields_set:
            _dict['InvoiceOnProgress'] = None

        # set to None if invoiced_amount (nullable) is None
        # and model_fields_set contains the field
        if self.invoiced_amount is None and "invoiced_amount" in self.model_fields_set:
            _dict['InvoicedAmount'] = None

        # set to None if last_invoice_date (nullable) is None
        # and model_fields_set contains the field
        if self.last_invoice_date is None and "last_invoice_date" in self.model_fields_set:
            _dict['LastInvoiceDate'] = None

        # set to None if charge (nullable) is None
        # and model_fields_set contains the field
        if self.charge is None and "charge" in self.model_fields_set:
            _dict['Charge'] = None

        # set to None if assigned_to_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.assigned_to_person_id is None and "assigned_to_person_id" in self.model_fields_set:
            _dict['AssignedToPersonID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadProject from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "TypeID": obj.get("TypeID"),
            "AdministrationID": obj.get("AdministrationID"),
            "RelationID": obj.get("RelationID"),
            "StartDate": obj.get("StartDate"),
            "EndDate": obj.get("EndDate"),
            "Completed": obj.get("Completed"),
            "StatusID": obj.get("StatusID"),
            "InternalProject": obj.get("InternalProject"),
            "InvoiceMethod": obj.get("InvoiceMethod"),
            "ParentProjectID": obj.get("ParentProjectID"),
            "UseInvoiceTerms": obj.get("UseInvoiceTerms"),
            "InvoiceOnProgress": obj.get("InvoiceOnProgress"),
            "InvoicedAmount": obj.get("InvoicedAmount"),
            "LastInvoiceDate": obj.get("LastInvoiceDate"),
            "Charge": obj.get("Charge"),
            "AssignedToPersonID": obj.get("AssignedToPersonID"),
            "InternalNote": obj.get("InternalNote"),
            "ExternalNote": obj.get("ExternalNote"),
            "BudgetCodeRequired": obj.get("BudgetCodeRequired"),
            "AllowProjectBudgetCodesOnly": obj.get("AllowProjectBudgetCodesOnly"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


