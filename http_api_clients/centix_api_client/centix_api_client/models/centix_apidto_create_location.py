# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOCreateLocation(BaseModel):
    """
    CentixAPIDTOCreateLocation
    """ # noqa: E501
    location_type_id: Optional[StrictInt] = Field(alias="LocationTypeID")
    aidc: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="AIDC")
    apply_order_setting: Optional[StrictBool] = Field(default=None, alias="ApplyOrderSetting")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    descr: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Descr")
    descr2: Optional[StrictStr] = Field(default=None, alias="Descr2")
    id: Annotated[str, Field(min_length=0, strict=True, max_length=20)] = Field(alias="ID")
    is_stock: Optional[StrictBool] = Field(default=None, alias="IsStock")
    location_owner_id: StrictInt = Field(alias="LocationOwnerID")
    location_status_id: Optional[StrictInt] = Field(alias="LocationStatusID")
    object_rental_price_list_id: Optional[StrictInt] = Field(default=None, alias="ObjectRentalPriceListID")
    order_description: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="OrderDescription")
    order_reference: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="OrderReference")
    order_warehouse_id: Optional[StrictInt] = Field(default=None, alias="OrderWarehouseID")
    parent_location_id: Optional[StrictInt] = Field(default=None, alias="ParentLocationID")
    person_id: Optional[StrictInt] = Field(default=None, alias="PersonID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    sale_price_list_id: Optional[StrictInt] = Field(default=None, alias="SalePriceListID")
    warehouse_id: Optional[StrictInt] = Field(default=None, alias="WarehouseID")
    workable_day_id: Optional[StrictInt] = Field(default=None, alias="WorkableDayID")
    additional_address_info1: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo1")
    additional_address_info2: Optional[StrictStr] = Field(default=None, alias="AdditionalAddressInfo2")
    city: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="City")
    country_id: Optional[StrictInt] = Field(default=None, alias="CountryID")
    state_id: Optional[StrictInt] = Field(default=None, alias="StateID")
    county_id: Optional[StrictInt] = Field(default=None, alias="CountyID")
    house_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNo")
    house_no_add: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=10)]] = Field(default=None, alias="HouseNoAdd")
    postalcode: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=12)]] = Field(default=None, alias="Postalcode")
    region_id: Optional[StrictInt] = Field(default=None, alias="RegionID")
    street: Optional[StrictStr] = Field(default=None, alias="Street")
    warehouse_pickzone_id: Optional[StrictInt] = Field(default=None, alias="WarehousePickzoneID")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["LocationTypeID", "AIDC", "ApplyOrderSetting", "CostCategoryID", "CostCodeID", "Descr", "Descr2", "ID", "IsStock", "LocationOwnerID", "LocationStatusID", "ObjectRentalPriceListID", "OrderDescription", "OrderReference", "OrderWarehouseID", "ParentLocationID", "PersonID", "ProjectID", "SalePriceListID", "WarehouseID", "WorkableDayID", "AdditionalAddressInfo1", "AdditionalAddressInfo2", "City", "CountryID", "StateID", "CountyID", "HouseNo", "HouseNoAdd", "Postalcode", "RegionID", "Street", "WarehousePickzoneID"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateLocation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if location_type_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_type_id is None and "location_type_id" in self.model_fields_set:
            _dict['LocationTypeID'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if is_stock (nullable) is None
        # and model_fields_set contains the field
        if self.is_stock is None and "is_stock" in self.model_fields_set:
            _dict['IsStock'] = None

        # set to None if location_status_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_status_id is None and "location_status_id" in self.model_fields_set:
            _dict['LocationStatusID'] = None

        # set to None if object_rental_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_rental_price_list_id is None and "object_rental_price_list_id" in self.model_fields_set:
            _dict['ObjectRentalPriceListID'] = None

        # set to None if order_warehouse_id (nullable) is None
        # and model_fields_set contains the field
        if self.order_warehouse_id is None and "order_warehouse_id" in self.model_fields_set:
            _dict['OrderWarehouseID'] = None

        # set to None if parent_location_id (nullable) is None
        # and model_fields_set contains the field
        if self.parent_location_id is None and "parent_location_id" in self.model_fields_set:
            _dict['ParentLocationID'] = None

        # set to None if person_id (nullable) is None
        # and model_fields_set contains the field
        if self.person_id is None and "person_id" in self.model_fields_set:
            _dict['PersonID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if sale_price_list_id (nullable) is None
        # and model_fields_set contains the field
        if self.sale_price_list_id is None and "sale_price_list_id" in self.model_fields_set:
            _dict['SalePriceListID'] = None

        # set to None if warehouse_id (nullable) is None
        # and model_fields_set contains the field
        if self.warehouse_id is None and "warehouse_id" in self.model_fields_set:
            _dict['WarehouseID'] = None

        # set to None if workable_day_id (nullable) is None
        # and model_fields_set contains the field
        if self.workable_day_id is None and "workable_day_id" in self.model_fields_set:
            _dict['WorkableDayID'] = None

        # set to None if country_id (nullable) is None
        # and model_fields_set contains the field
        if self.country_id is None and "country_id" in self.model_fields_set:
            _dict['CountryID'] = None

        # set to None if state_id (nullable) is None
        # and model_fields_set contains the field
        if self.state_id is None and "state_id" in self.model_fields_set:
            _dict['StateID'] = None

        # set to None if county_id (nullable) is None
        # and model_fields_set contains the field
        if self.county_id is None and "county_id" in self.model_fields_set:
            _dict['CountyID'] = None

        # set to None if region_id (nullable) is None
        # and model_fields_set contains the field
        if self.region_id is None and "region_id" in self.model_fields_set:
            _dict['RegionID'] = None

        # set to None if warehouse_pickzone_id (nullable) is None
        # and model_fields_set contains the field
        if self.warehouse_pickzone_id is None and "warehouse_pickzone_id" in self.model_fields_set:
            _dict['WarehousePickzoneID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOCreateLocation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "LocationTypeID": obj.get("LocationTypeID"),
            "AIDC": obj.get("AIDC"),
            "ApplyOrderSetting": obj.get("ApplyOrderSetting"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "CostCodeID": obj.get("CostCodeID"),
            "Descr": obj.get("Descr"),
            "Descr2": obj.get("Descr2"),
            "ID": obj.get("ID"),
            "IsStock": obj.get("IsStock"),
            "LocationOwnerID": obj.get("LocationOwnerID"),
            "LocationStatusID": obj.get("LocationStatusID"),
            "ObjectRentalPriceListID": obj.get("ObjectRentalPriceListID"),
            "OrderDescription": obj.get("OrderDescription"),
            "OrderReference": obj.get("OrderReference"),
            "OrderWarehouseID": obj.get("OrderWarehouseID"),
            "ParentLocationID": obj.get("ParentLocationID"),
            "PersonID": obj.get("PersonID"),
            "ProjectID": obj.get("ProjectID"),
            "SalePriceListID": obj.get("SalePriceListID"),
            "WarehouseID": obj.get("WarehouseID"),
            "WorkableDayID": obj.get("WorkableDayID"),
            "AdditionalAddressInfo1": obj.get("AdditionalAddressInfo1"),
            "AdditionalAddressInfo2": obj.get("AdditionalAddressInfo2"),
            "City": obj.get("City"),
            "CountryID": obj.get("CountryID"),
            "StateID": obj.get("StateID"),
            "CountyID": obj.get("CountyID"),
            "HouseNo": obj.get("HouseNo"),
            "HouseNoAdd": obj.get("HouseNoAdd"),
            "Postalcode": obj.get("Postalcode"),
            "RegionID": obj.get("RegionID"),
            "Street": obj.get("Street"),
            "WarehousePickzoneID": obj.get("WarehousePickzoneID")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


