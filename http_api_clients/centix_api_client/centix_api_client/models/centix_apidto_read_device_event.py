# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadDeviceEvent(BaseModel):
    """
    CentixAPIDTOReadDeviceEvent
    """ # noqa: E501
    device_id: Optional[StrictInt] = Field(default=None, alias="DeviceID")
    accuracy: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Accuracy")
    location_type: Optional[StrictStr] = Field(default=None, alias="LocationType")
    location_date: Optional[datetime] = Field(default=None, alias="LocationDate")
    lat: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Lat")
    long: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Long")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["DeviceID", "Accuracy", "LocationType", "LocationDate", "Lat", "Long", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDeviceEvent from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if device_id (nullable) is None
        # and model_fields_set contains the field
        if self.device_id is None and "device_id" in self.model_fields_set:
            _dict['DeviceID'] = None

        # set to None if accuracy (nullable) is None
        # and model_fields_set contains the field
        if self.accuracy is None and "accuracy" in self.model_fields_set:
            _dict['Accuracy'] = None

        # set to None if location_date (nullable) is None
        # and model_fields_set contains the field
        if self.location_date is None and "location_date" in self.model_fields_set:
            _dict['LocationDate'] = None

        # set to None if lat (nullable) is None
        # and model_fields_set contains the field
        if self.lat is None and "lat" in self.model_fields_set:
            _dict['Lat'] = None

        # set to None if long (nullable) is None
        # and model_fields_set contains the field
        if self.long is None and "long" in self.model_fields_set:
            _dict['Long'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDeviceEvent from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "DeviceID": obj.get("DeviceID"),
            "Accuracy": obj.get("Accuracy"),
            "LocationType": obj.get("LocationType"),
            "LocationDate": obj.get("LocationDate"),
            "Lat": obj.get("Lat"),
            "Long": obj.get("Long"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


