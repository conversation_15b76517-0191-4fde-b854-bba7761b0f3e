# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadDocumentKind(BaseModel):
    """
    CentixAPIDTOReadDocumentKind
    """ # noqa: E501
    use_restricted_access: Optional[StrictBool] = Field(default=None, alias="UseRestrictedAccess")
    archive: Optional[StrictBool] = Field(default=None, alias="Archive")
    use_retention_period: Optional[StrictBool] = Field(default=None, alias="UseRetentionPeriod")
    retention_period_unit: Optional[StrictInt] = Field(default=None, description="0 = Weeks, 1 = Months, 2 = Years", alias="RetentionPeriodUnit")
    retention_period: Optional[StrictInt] = Field(default=None, alias="RetentionPeriod")
    apply_to_workflows: Optional[StrictBool] = Field(default=None, alias="ApplyToWorkflows")
    apply_to_objects: Optional[StrictBool] = Field(default=None, alias="ApplyToObjects")
    apply_to_locations: Optional[StrictBool] = Field(default=None, alias="ApplyToLocations")
    apply_to_contracts: Optional[StrictBool] = Field(default=None, alias="ApplyToContracts")
    apply_to_deprecations: Optional[StrictBool] = Field(default=None, alias="ApplyToDeprecations")
    apply_to_movebaskets: Optional[StrictBool] = Field(default=None, alias="ApplyToMovebaskets")
    apply_to_inspections: Optional[StrictBool] = Field(default=None, alias="ApplyToInspections")
    apply_to_purchase_orders: Optional[StrictBool] = Field(default=None, alias="ApplyToPurchaseOrders")
    apply_to_products: Optional[StrictBool] = Field(default=None, alias="ApplyToProducts")
    apply_to_requirements: Optional[StrictBool] = Field(default=None, alias="ApplyToRequirements")
    apply_to_persons: Optional[StrictBool] = Field(default=None, alias="ApplyToPersons")
    apply_to_packlists: Optional[StrictBool] = Field(default=None, alias="ApplyToPacklists")
    apply_to_relations: Optional[StrictBool] = Field(default=None, alias="ApplyToRelations")
    apply_to_projects: Optional[StrictBool] = Field(default=None, alias="ApplyToProjects")
    apply_to_returns: Optional[StrictBool] = Field(default=None, alias="ApplyToReturns")
    apply_to_overhaul_orders: Optional[StrictBool] = Field(default=None, alias="ApplyToOverhaulOrders")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["UseRestrictedAccess", "Archive", "UseRetentionPeriod", "RetentionPeriodUnit", "RetentionPeriod", "ApplyToWorkflows", "ApplyToObjects", "ApplyToLocations", "ApplyToContracts", "ApplyToDeprecations", "ApplyToMovebaskets", "ApplyToInspections", "ApplyToPurchaseOrders", "ApplyToProducts", "ApplyToRequirements", "ApplyToPersons", "ApplyToPacklists", "ApplyToRelations", "ApplyToProjects", "ApplyToReturns", "ApplyToOverhaulOrders", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('retention_period_unit')
    def retention_period_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2]):
            raise ValueError("must be one of enum values (0, 1, 2)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDocumentKind from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDocumentKind from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "UseRestrictedAccess": obj.get("UseRestrictedAccess"),
            "Archive": obj.get("Archive"),
            "UseRetentionPeriod": obj.get("UseRetentionPeriod"),
            "RetentionPeriodUnit": obj.get("RetentionPeriodUnit"),
            "RetentionPeriod": obj.get("RetentionPeriod"),
            "ApplyToWorkflows": obj.get("ApplyToWorkflows"),
            "ApplyToObjects": obj.get("ApplyToObjects"),
            "ApplyToLocations": obj.get("ApplyToLocations"),
            "ApplyToContracts": obj.get("ApplyToContracts"),
            "ApplyToDeprecations": obj.get("ApplyToDeprecations"),
            "ApplyToMovebaskets": obj.get("ApplyToMovebaskets"),
            "ApplyToInspections": obj.get("ApplyToInspections"),
            "ApplyToPurchaseOrders": obj.get("ApplyToPurchaseOrders"),
            "ApplyToProducts": obj.get("ApplyToProducts"),
            "ApplyToRequirements": obj.get("ApplyToRequirements"),
            "ApplyToPersons": obj.get("ApplyToPersons"),
            "ApplyToPacklists": obj.get("ApplyToPacklists"),
            "ApplyToRelations": obj.get("ApplyToRelations"),
            "ApplyToProjects": obj.get("ApplyToProjects"),
            "ApplyToReturns": obj.get("ApplyToReturns"),
            "ApplyToOverhaulOrders": obj.get("ApplyToOverhaulOrders"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


