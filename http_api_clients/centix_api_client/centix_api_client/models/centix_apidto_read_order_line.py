# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadOrderLine(BaseModel):
    """
    CentixAPIDTOReadOrderLine
    """ # noqa: E501
    order_id: Optional[StrictInt] = Field(default=None, alias="OrderID")
    order_line_type: Optional[StrictInt] = Field(default=None, description="1 = Sales, 2 = ObjectRental, 4 = Comment", alias="OrderLineType")
    position_no: Optional[StrictInt] = Field(default=None, alias="PositionNo")
    product_id: Optional[StrictInt] = Field(default=None, alias="ProductID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    discount_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="DiscountPCT")
    price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Price")
    vat_id: Optional[StrictInt] = Field(default=None, alias="VatID")
    currency_id: Optional[StrictInt] = Field(default=None, alias="CurrencyID")
    lock_price: Optional[StrictBool] = Field(default=None, alias="LockPrice")
    product_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductPrice")
    is_composed_product: Optional[StrictBool] = Field(default=None, alias="IsComposedProduct")
    invoice_composed_product: Optional[StrictBool] = Field(default=None, alias="InvoiceComposedProduct")
    invoice_parts: Optional[StrictBool] = Field(default=None, alias="InvoiceParts")
    composed_product_parent_id: Optional[StrictInt] = Field(default=None, alias="ComposedProductParentID")
    product_composition_quantity: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductCompositionQuantity")
    product_composition_round_off_method: Optional[StrictInt] = Field(default=None, alias="ProductCompositionRoundOffMethod")
    explode_parts_on_order: Optional[StrictBool] = Field(default=None, alias="ExplodePartsOnOrder")
    explode_parts_on_invoice: Optional[StrictBool] = Field(default=None, alias="ExplodePartsOnInvoice")
    alternate_product_id: Optional[StrictInt] = Field(default=None, alias="AlternateProductID")
    cross_link_product_id: Optional[StrictInt] = Field(default=None, alias="CrossLinkProductID")
    project_id: Optional[StrictInt] = Field(default=None, alias="ProjectID")
    cost_code_id: Optional[StrictInt] = Field(default=None, alias="CostCodeID")
    order_quantity: Optional[StrictInt] = Field(default=None, alias="OrderQuantity")
    unplaced_quantity: Optional[StrictInt] = Field(default=None, alias="UnplacedQuantity")
    pick_quantity: Optional[StrictInt] = Field(default=None, alias="PickQuantity")
    pick_date: Optional[datetime] = Field(default=None, alias="PickDate")
    picked_quantity: Optional[StrictInt] = Field(default=None, alias="PickedQuantity")
    picked_date: Optional[datetime] = Field(default=None, alias="PickedDate")
    pack_quantity: Optional[StrictInt] = Field(default=None, alias="PackQuantity")
    pack_date: Optional[datetime] = Field(default=None, alias="PackDate")
    rental_object_rma_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectRmaQuantity")
    rental_object_rma_date: Optional[datetime] = Field(default=None, alias="RentalObjectRmaDate")
    rental_object_entry_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectEntryQuantity")
    rental_object_entry_date: Optional[datetime] = Field(default=None, alias="RentalObjectEntryDate")
    rental_object_return_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectReturnQuantity")
    rental_object_return_date: Optional[datetime] = Field(default=None, alias="RentalObjectReturnDate")
    rental_object_outstanding_quantity: Optional[StrictInt] = Field(default=None, alias="RentalObjectOutstandingQuantity")
    rented_from: Optional[datetime] = Field(default=None, alias="RentedFrom")
    rented_till: Optional[datetime] = Field(default=None, alias="RentedTill")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    sub_total: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotal")
    expected_deliver_date: Optional[datetime] = Field(default=None, alias="ExpectedDeliverDate")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    product_price_vat_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="ProductPriceVatPct")
    product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="ProductCalcPrice")
    price_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceIncl")
    price_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="PriceVatAmount")
    sub_total_incl: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalIncl")
    sub_total_vat_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalVatAmount")
    is_cancelled: Optional[StrictBool] = Field(default=None, alias="IsCancelled")
    cancel_quantity: Optional[StrictInt] = Field(default=None, alias="CancelQuantity")
    cancel_reason_id: Optional[StrictInt] = Field(default=None, alias="CancelReasonID")
    cancelled_at: Optional[datetime] = Field(default=None, alias="CancelledAt")
    cancelled_by: Optional[StrictStr] = Field(default=None, alias="CancelledBy")
    cancel_comment: Optional[StrictStr] = Field(default=None, alias="CancelComment")
    product_price_currency_id: Optional[StrictInt] = Field(default=None, alias="ProductPriceCurrencyID")
    product_price_exchange_rate: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="ProductPriceExchangeRate")
    cost_category_id: Optional[StrictInt] = Field(default=None, alias="CostCategoryID")
    sub_total_product_calc_price: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="SubTotalProductCalcPrice")
    unit_weight: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="UnitWeight")
    invoice_transport_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceTransportCosts")
    invoice_order_costs: Optional[StrictBool] = Field(default=None, alias="InvoiceOrderCosts")
    is_discount_product: Optional[StrictBool] = Field(default=None, alias="IsDiscountProduct")
    is_order_costs_product: Optional[StrictBool] = Field(default=None, alias="IsOrderCostsProduct")
    is_transport_costs_product: Optional[StrictBool] = Field(default=None, alias="IsTransportCostsProduct")
    free_gift_quantity: Optional[StrictInt] = Field(default=None, alias="FreeGiftQuantity")
    free_gift_type: Optional[StrictInt] = Field(default=None, alias="FreeGiftType")
    is_store_credit: Optional[StrictBool] = Field(default=None, alias="IsStoreCredit")
    store_credit_amount: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="StoreCreditAmount")
    store_credit_currency_id: Optional[StrictInt] = Field(default=None, alias="StoreCreditCurrencyID")
    store_credit_expire: Optional[StrictInt] = Field(default=None, alias="StoreCreditExpire")
    store_credit_expire_unit: Optional[StrictInt] = Field(default=None, alias="StoreCreditExpireUnit")
    product_drop_shipment: Optional[StrictInt] = Field(default=None, description="0 = DontAllowDropShipment, 1 = AllowDropShipment, 2 = DefaultDropShipment, 3 = DropShipmentRequired", alias="ProductDropShipment")
    is_drop_shipment: Optional[StrictBool] = Field(default=None, alias="IsDropShipment")
    dont_allow_partial_delivery: Optional[StrictBool] = Field(default=None, alias="DontAllowPartialDelivery")
    invoiced: Optional[StrictBool] = Field(default=None, alias="Invoiced")
    lock_product_price: Optional[StrictBool] = Field(default=None, alias="LockProductPrice")
    work_flow_id: Optional[StrictInt] = Field(default=None, alias="WorkFlowID")
    suppress_zero: Optional[StrictBool] = Field(default=None, alias="SuppressZero")
    deliver_contact_person_id: Optional[StrictInt] = Field(default=None, alias="DeliverContactPersonID")
    allow_repurchase: Optional[StrictBool] = Field(default=None, alias="AllowRepurchase")
    repurchase_pct: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="Decimal value for a percentage where a value of 0-1 equals 0-100 percent.", alias="RepurchasePCT")
    budget_code_id: Optional[StrictInt] = Field(default=None, alias="BudgetCodeID")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["OrderID", "OrderLineType", "PositionNo", "ProductID", "Descr", "DiscountPCT", "Price", "VatID", "CurrencyID", "LockPrice", "ProductPrice", "IsComposedProduct", "InvoiceComposedProduct", "InvoiceParts", "ComposedProductParentID", "ProductCompositionQuantity", "ProductCompositionRoundOffMethod", "ExplodePartsOnOrder", "ExplodePartsOnInvoice", "AlternateProductID", "CrossLinkProductID", "ProjectID", "CostCodeID", "OrderQuantity", "UnplacedQuantity", "PickQuantity", "PickDate", "PickedQuantity", "PickedDate", "PackQuantity", "PackDate", "RentalObjectRmaQuantity", "RentalObjectRmaDate", "RentalObjectEntryQuantity", "RentalObjectEntryDate", "RentalObjectReturnQuantity", "RentalObjectReturnDate", "RentalObjectOutstandingQuantity", "RentedFrom", "RentedTill", "InternalNote", "ExternalNote", "Reference", "SubTotal", "ExpectedDeliverDate", "ID", "ProductPriceVatPct", "ProductCalcPrice", "PriceIncl", "PriceVatAmount", "SubTotalIncl", "SubTotalVatAmount", "IsCancelled", "CancelQuantity", "CancelReasonID", "CancelledAt", "CancelledBy", "CancelComment", "ProductPriceCurrencyID", "ProductPriceExchangeRate", "CostCategoryID", "SubTotalProductCalcPrice", "UnitWeight", "InvoiceTransportCosts", "InvoiceOrderCosts", "IsDiscountProduct", "IsOrderCostsProduct", "IsTransportCostsProduct", "FreeGiftQuantity", "FreeGiftType", "IsStoreCredit", "StoreCreditAmount", "StoreCreditCurrencyID", "StoreCreditExpire", "StoreCreditExpireUnit", "ProductDropShipment", "IsDropShipment", "DontAllowPartialDelivery", "Invoiced", "LockProductPrice", "WorkFlowID", "SuppressZero", "DeliverContactPersonID", "AllowRepurchase", "RepurchasePCT", "BudgetCodeID", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('order_line_type')
    def order_line_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 4]):
            raise ValueError("must be one of enum values (1, 2, 4)")
        return value

    @field_validator('product_drop_shipment')
    def product_drop_shipment_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrderLine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if position_no (nullable) is None
        # and model_fields_set contains the field
        if self.position_no is None and "position_no" in self.model_fields_set:
            _dict['PositionNo'] = None

        # set to None if product_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_id is None and "product_id" in self.model_fields_set:
            _dict['ProductID'] = None

        # set to None if discount_pct (nullable) is None
        # and model_fields_set contains the field
        if self.discount_pct is None and "discount_pct" in self.model_fields_set:
            _dict['DiscountPCT'] = None

        # set to None if price (nullable) is None
        # and model_fields_set contains the field
        if self.price is None and "price" in self.model_fields_set:
            _dict['Price'] = None

        # set to None if vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.vat_id is None and "vat_id" in self.model_fields_set:
            _dict['VatID'] = None

        # set to None if currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.currency_id is None and "currency_id" in self.model_fields_set:
            _dict['CurrencyID'] = None

        # set to None if product_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_price is None and "product_price" in self.model_fields_set:
            _dict['ProductPrice'] = None

        # set to None if is_composed_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_composed_product is None and "is_composed_product" in self.model_fields_set:
            _dict['IsComposedProduct'] = None

        # set to None if invoice_composed_product (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_composed_product is None and "invoice_composed_product" in self.model_fields_set:
            _dict['InvoiceComposedProduct'] = None

        # set to None if invoice_parts (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_parts is None and "invoice_parts" in self.model_fields_set:
            _dict['InvoiceParts'] = None

        # set to None if composed_product_parent_id (nullable) is None
        # and model_fields_set contains the field
        if self.composed_product_parent_id is None and "composed_product_parent_id" in self.model_fields_set:
            _dict['ComposedProductParentID'] = None

        # set to None if product_composition_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.product_composition_quantity is None and "product_composition_quantity" in self.model_fields_set:
            _dict['ProductCompositionQuantity'] = None

        # set to None if product_composition_round_off_method (nullable) is None
        # and model_fields_set contains the field
        if self.product_composition_round_off_method is None and "product_composition_round_off_method" in self.model_fields_set:
            _dict['ProductCompositionRoundOffMethod'] = None

        # set to None if explode_parts_on_order (nullable) is None
        # and model_fields_set contains the field
        if self.explode_parts_on_order is None and "explode_parts_on_order" in self.model_fields_set:
            _dict['ExplodePartsOnOrder'] = None

        # set to None if explode_parts_on_invoice (nullable) is None
        # and model_fields_set contains the field
        if self.explode_parts_on_invoice is None and "explode_parts_on_invoice" in self.model_fields_set:
            _dict['ExplodePartsOnInvoice'] = None

        # set to None if alternate_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.alternate_product_id is None and "alternate_product_id" in self.model_fields_set:
            _dict['AlternateProductID'] = None

        # set to None if cross_link_product_id (nullable) is None
        # and model_fields_set contains the field
        if self.cross_link_product_id is None and "cross_link_product_id" in self.model_fields_set:
            _dict['CrossLinkProductID'] = None

        # set to None if project_id (nullable) is None
        # and model_fields_set contains the field
        if self.project_id is None and "project_id" in self.model_fields_set:
            _dict['ProjectID'] = None

        # set to None if cost_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_code_id is None and "cost_code_id" in self.model_fields_set:
            _dict['CostCodeID'] = None

        # set to None if order_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.order_quantity is None and "order_quantity" in self.model_fields_set:
            _dict['OrderQuantity'] = None

        # set to None if unplaced_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.unplaced_quantity is None and "unplaced_quantity" in self.model_fields_set:
            _dict['UnplacedQuantity'] = None

        # set to None if pick_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.pick_quantity is None and "pick_quantity" in self.model_fields_set:
            _dict['PickQuantity'] = None

        # set to None if pick_date (nullable) is None
        # and model_fields_set contains the field
        if self.pick_date is None and "pick_date" in self.model_fields_set:
            _dict['PickDate'] = None

        # set to None if picked_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.picked_quantity is None and "picked_quantity" in self.model_fields_set:
            _dict['PickedQuantity'] = None

        # set to None if picked_date (nullable) is None
        # and model_fields_set contains the field
        if self.picked_date is None and "picked_date" in self.model_fields_set:
            _dict['PickedDate'] = None

        # set to None if pack_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.pack_quantity is None and "pack_quantity" in self.model_fields_set:
            _dict['PackQuantity'] = None

        # set to None if pack_date (nullable) is None
        # and model_fields_set contains the field
        if self.pack_date is None and "pack_date" in self.model_fields_set:
            _dict['PackDate'] = None

        # set to None if rental_object_rma_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_rma_quantity is None and "rental_object_rma_quantity" in self.model_fields_set:
            _dict['RentalObjectRmaQuantity'] = None

        # set to None if rental_object_rma_date (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_rma_date is None and "rental_object_rma_date" in self.model_fields_set:
            _dict['RentalObjectRmaDate'] = None

        # set to None if rental_object_entry_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_entry_quantity is None and "rental_object_entry_quantity" in self.model_fields_set:
            _dict['RentalObjectEntryQuantity'] = None

        # set to None if rental_object_entry_date (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_entry_date is None and "rental_object_entry_date" in self.model_fields_set:
            _dict['RentalObjectEntryDate'] = None

        # set to None if rental_object_return_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_return_quantity is None and "rental_object_return_quantity" in self.model_fields_set:
            _dict['RentalObjectReturnQuantity'] = None

        # set to None if rental_object_return_date (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_return_date is None and "rental_object_return_date" in self.model_fields_set:
            _dict['RentalObjectReturnDate'] = None

        # set to None if rental_object_outstanding_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.rental_object_outstanding_quantity is None and "rental_object_outstanding_quantity" in self.model_fields_set:
            _dict['RentalObjectOutstandingQuantity'] = None

        # set to None if rented_from (nullable) is None
        # and model_fields_set contains the field
        if self.rented_from is None and "rented_from" in self.model_fields_set:
            _dict['RentedFrom'] = None

        # set to None if rented_till (nullable) is None
        # and model_fields_set contains the field
        if self.rented_till is None and "rented_till" in self.model_fields_set:
            _dict['RentedTill'] = None

        # set to None if sub_total (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total is None and "sub_total" in self.model_fields_set:
            _dict['SubTotal'] = None

        # set to None if expected_deliver_date (nullable) is None
        # and model_fields_set contains the field
        if self.expected_deliver_date is None and "expected_deliver_date" in self.model_fields_set:
            _dict['ExpectedDeliverDate'] = None

        # set to None if product_price_vat_pct (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_vat_pct is None and "product_price_vat_pct" in self.model_fields_set:
            _dict['ProductPriceVatPct'] = None

        # set to None if product_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.product_calc_price is None and "product_calc_price" in self.model_fields_set:
            _dict['ProductCalcPrice'] = None

        # set to None if price_incl (nullable) is None
        # and model_fields_set contains the field
        if self.price_incl is None and "price_incl" in self.model_fields_set:
            _dict['PriceIncl'] = None

        # set to None if price_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.price_vat_amount is None and "price_vat_amount" in self.model_fields_set:
            _dict['PriceVatAmount'] = None

        # set to None if sub_total_incl (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_incl is None and "sub_total_incl" in self.model_fields_set:
            _dict['SubTotalIncl'] = None

        # set to None if sub_total_vat_amount (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_vat_amount is None and "sub_total_vat_amount" in self.model_fields_set:
            _dict['SubTotalVatAmount'] = None

        # set to None if cancel_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.cancel_quantity is None and "cancel_quantity" in self.model_fields_set:
            _dict['CancelQuantity'] = None

        # set to None if cancel_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.cancel_reason_id is None and "cancel_reason_id" in self.model_fields_set:
            _dict['CancelReasonID'] = None

        # set to None if cancelled_at (nullable) is None
        # and model_fields_set contains the field
        if self.cancelled_at is None and "cancelled_at" in self.model_fields_set:
            _dict['CancelledAt'] = None

        # set to None if product_price_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_currency_id is None and "product_price_currency_id" in self.model_fields_set:
            _dict['ProductPriceCurrencyID'] = None

        # set to None if product_price_exchange_rate (nullable) is None
        # and model_fields_set contains the field
        if self.product_price_exchange_rate is None and "product_price_exchange_rate" in self.model_fields_set:
            _dict['ProductPriceExchangeRate'] = None

        # set to None if cost_category_id (nullable) is None
        # and model_fields_set contains the field
        if self.cost_category_id is None and "cost_category_id" in self.model_fields_set:
            _dict['CostCategoryID'] = None

        # set to None if sub_total_product_calc_price (nullable) is None
        # and model_fields_set contains the field
        if self.sub_total_product_calc_price is None and "sub_total_product_calc_price" in self.model_fields_set:
            _dict['SubTotalProductCalcPrice'] = None

        # set to None if unit_weight (nullable) is None
        # and model_fields_set contains the field
        if self.unit_weight is None and "unit_weight" in self.model_fields_set:
            _dict['UnitWeight'] = None

        # set to None if invoice_transport_costs (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_transport_costs is None and "invoice_transport_costs" in self.model_fields_set:
            _dict['InvoiceTransportCosts'] = None

        # set to None if invoice_order_costs (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_order_costs is None and "invoice_order_costs" in self.model_fields_set:
            _dict['InvoiceOrderCosts'] = None

        # set to None if is_discount_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_discount_product is None and "is_discount_product" in self.model_fields_set:
            _dict['IsDiscountProduct'] = None

        # set to None if is_order_costs_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_order_costs_product is None and "is_order_costs_product" in self.model_fields_set:
            _dict['IsOrderCostsProduct'] = None

        # set to None if is_transport_costs_product (nullable) is None
        # and model_fields_set contains the field
        if self.is_transport_costs_product is None and "is_transport_costs_product" in self.model_fields_set:
            _dict['IsTransportCostsProduct'] = None

        # set to None if free_gift_quantity (nullable) is None
        # and model_fields_set contains the field
        if self.free_gift_quantity is None and "free_gift_quantity" in self.model_fields_set:
            _dict['FreeGiftQuantity'] = None

        # set to None if free_gift_type (nullable) is None
        # and model_fields_set contains the field
        if self.free_gift_type is None and "free_gift_type" in self.model_fields_set:
            _dict['FreeGiftType'] = None

        # set to None if is_store_credit (nullable) is None
        # and model_fields_set contains the field
        if self.is_store_credit is None and "is_store_credit" in self.model_fields_set:
            _dict['IsStoreCredit'] = None

        # set to None if store_credit_amount (nullable) is None
        # and model_fields_set contains the field
        if self.store_credit_amount is None and "store_credit_amount" in self.model_fields_set:
            _dict['StoreCreditAmount'] = None

        # set to None if store_credit_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.store_credit_currency_id is None and "store_credit_currency_id" in self.model_fields_set:
            _dict['StoreCreditCurrencyID'] = None

        # set to None if store_credit_expire (nullable) is None
        # and model_fields_set contains the field
        if self.store_credit_expire is None and "store_credit_expire" in self.model_fields_set:
            _dict['StoreCreditExpire'] = None

        # set to None if store_credit_expire_unit (nullable) is None
        # and model_fields_set contains the field
        if self.store_credit_expire_unit is None and "store_credit_expire_unit" in self.model_fields_set:
            _dict['StoreCreditExpireUnit'] = None

        # set to None if product_drop_shipment (nullable) is None
        # and model_fields_set contains the field
        if self.product_drop_shipment is None and "product_drop_shipment" in self.model_fields_set:
            _dict['ProductDropShipment'] = None

        # set to None if lock_product_price (nullable) is None
        # and model_fields_set contains the field
        if self.lock_product_price is None and "lock_product_price" in self.model_fields_set:
            _dict['LockProductPrice'] = None

        # set to None if work_flow_id (nullable) is None
        # and model_fields_set contains the field
        if self.work_flow_id is None and "work_flow_id" in self.model_fields_set:
            _dict['WorkFlowID'] = None

        # set to None if suppress_zero (nullable) is None
        # and model_fields_set contains the field
        if self.suppress_zero is None and "suppress_zero" in self.model_fields_set:
            _dict['SuppressZero'] = None

        # set to None if deliver_contact_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_contact_person_id is None and "deliver_contact_person_id" in self.model_fields_set:
            _dict['DeliverContactPersonID'] = None

        # set to None if repurchase_pct (nullable) is None
        # and model_fields_set contains the field
        if self.repurchase_pct is None and "repurchase_pct" in self.model_fields_set:
            _dict['RepurchasePCT'] = None

        # set to None if budget_code_id (nullable) is None
        # and model_fields_set contains the field
        if self.budget_code_id is None and "budget_code_id" in self.model_fields_set:
            _dict['BudgetCodeID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadOrderLine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "OrderID": obj.get("OrderID"),
            "OrderLineType": obj.get("OrderLineType"),
            "PositionNo": obj.get("PositionNo"),
            "ProductID": obj.get("ProductID"),
            "Descr": obj.get("Descr"),
            "DiscountPCT": obj.get("DiscountPCT"),
            "Price": obj.get("Price"),
            "VatID": obj.get("VatID"),
            "CurrencyID": obj.get("CurrencyID"),
            "LockPrice": obj.get("LockPrice"),
            "ProductPrice": obj.get("ProductPrice"),
            "IsComposedProduct": obj.get("IsComposedProduct"),
            "InvoiceComposedProduct": obj.get("InvoiceComposedProduct"),
            "InvoiceParts": obj.get("InvoiceParts"),
            "ComposedProductParentID": obj.get("ComposedProductParentID"),
            "ProductCompositionQuantity": obj.get("ProductCompositionQuantity"),
            "ProductCompositionRoundOffMethod": obj.get("ProductCompositionRoundOffMethod"),
            "ExplodePartsOnOrder": obj.get("ExplodePartsOnOrder"),
            "ExplodePartsOnInvoice": obj.get("ExplodePartsOnInvoice"),
            "AlternateProductID": obj.get("AlternateProductID"),
            "CrossLinkProductID": obj.get("CrossLinkProductID"),
            "ProjectID": obj.get("ProjectID"),
            "CostCodeID": obj.get("CostCodeID"),
            "OrderQuantity": obj.get("OrderQuantity"),
            "UnplacedQuantity": obj.get("UnplacedQuantity"),
            "PickQuantity": obj.get("PickQuantity"),
            "PickDate": obj.get("PickDate"),
            "PickedQuantity": obj.get("PickedQuantity"),
            "PickedDate": obj.get("PickedDate"),
            "PackQuantity": obj.get("PackQuantity"),
            "PackDate": obj.get("PackDate"),
            "RentalObjectRmaQuantity": obj.get("RentalObjectRmaQuantity"),
            "RentalObjectRmaDate": obj.get("RentalObjectRmaDate"),
            "RentalObjectEntryQuantity": obj.get("RentalObjectEntryQuantity"),
            "RentalObjectEntryDate": obj.get("RentalObjectEntryDate"),
            "RentalObjectReturnQuantity": obj.get("RentalObjectReturnQuantity"),
            "RentalObjectReturnDate": obj.get("RentalObjectReturnDate"),
            "RentalObjectOutstandingQuantity": obj.get("RentalObjectOutstandingQuantity"),
            "RentedFrom": obj.get("RentedFrom"),
            "RentedTill": obj.get("RentedTill"),
            "InternalNote": obj.get("InternalNote"),
            "ExternalNote": obj.get("ExternalNote"),
            "Reference": obj.get("Reference"),
            "SubTotal": obj.get("SubTotal"),
            "ExpectedDeliverDate": obj.get("ExpectedDeliverDate"),
            "ID": obj.get("ID"),
            "ProductPriceVatPct": obj.get("ProductPriceVatPct"),
            "ProductCalcPrice": obj.get("ProductCalcPrice"),
            "PriceIncl": obj.get("PriceIncl"),
            "PriceVatAmount": obj.get("PriceVatAmount"),
            "SubTotalIncl": obj.get("SubTotalIncl"),
            "SubTotalVatAmount": obj.get("SubTotalVatAmount"),
            "IsCancelled": obj.get("IsCancelled"),
            "CancelQuantity": obj.get("CancelQuantity"),
            "CancelReasonID": obj.get("CancelReasonID"),
            "CancelledAt": obj.get("CancelledAt"),
            "CancelledBy": obj.get("CancelledBy"),
            "CancelComment": obj.get("CancelComment"),
            "ProductPriceCurrencyID": obj.get("ProductPriceCurrencyID"),
            "ProductPriceExchangeRate": obj.get("ProductPriceExchangeRate"),
            "CostCategoryID": obj.get("CostCategoryID"),
            "SubTotalProductCalcPrice": obj.get("SubTotalProductCalcPrice"),
            "UnitWeight": obj.get("UnitWeight"),
            "InvoiceTransportCosts": obj.get("InvoiceTransportCosts"),
            "InvoiceOrderCosts": obj.get("InvoiceOrderCosts"),
            "IsDiscountProduct": obj.get("IsDiscountProduct"),
            "IsOrderCostsProduct": obj.get("IsOrderCostsProduct"),
            "IsTransportCostsProduct": obj.get("IsTransportCostsProduct"),
            "FreeGiftQuantity": obj.get("FreeGiftQuantity"),
            "FreeGiftType": obj.get("FreeGiftType"),
            "IsStoreCredit": obj.get("IsStoreCredit"),
            "StoreCreditAmount": obj.get("StoreCreditAmount"),
            "StoreCreditCurrencyID": obj.get("StoreCreditCurrencyID"),
            "StoreCreditExpire": obj.get("StoreCreditExpire"),
            "StoreCreditExpireUnit": obj.get("StoreCreditExpireUnit"),
            "ProductDropShipment": obj.get("ProductDropShipment"),
            "IsDropShipment": obj.get("IsDropShipment"),
            "DontAllowPartialDelivery": obj.get("DontAllowPartialDelivery"),
            "Invoiced": obj.get("Invoiced"),
            "LockProductPrice": obj.get("LockProductPrice"),
            "WorkFlowID": obj.get("WorkFlowID"),
            "SuppressZero": obj.get("SuppressZero"),
            "DeliverContactPersonID": obj.get("DeliverContactPersonID"),
            "AllowRepurchase": obj.get("AllowRepurchase"),
            "RepurchasePCT": obj.get("RepurchasePCT"),
            "BudgetCodeID": obj.get("BudgetCodeID"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


