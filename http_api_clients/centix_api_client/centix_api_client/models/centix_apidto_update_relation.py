# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from centix_api_client.models.centix_apidto_address import CentixAPIDTOAddress
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOUpdateRelation(BaseModel):
    """
    CentixAPIDTOUpdateRelation
    """ # noqa: E501
    id: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="ID")
    name: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Name")
    name2: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=100)]] = Field(default=None, alias="Name2")
    relation_group_id: Optional[StrictInt] = Field(default=None, alias="RelationGroupID")
    private_person: Optional[StrictBool] = Field(default=None, alias="PrivatePerson")
    relation_status_id: Optional[StrictInt] = Field(alias="RelationStatusID")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    note: Optional[StrictStr] = Field(default=None, alias="Note")
    parent_relation_id: Optional[StrictInt] = Field(default=None, alias="ParentRelationID")
    auto_number_prefix: Optional[StrictStr] = Field(default=None, alias="AutoNumberPrefix")
    phone_number: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="PhoneNumber")
    fax_number: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=50)]] = Field(default=None, alias="FaxNumber")
    email_address: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=320)]] = Field(default=None, alias="EmailAddress")
    website: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=320)]] = Field(default=None, alias="Website")
    main_contact_id: Optional[StrictInt] = Field(default=None, alias="MainContactID")
    vat_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=15)]] = Field(default=None, alias="VatNo")
    debtor_number: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="DebtorNumber")
    creditor_number: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="CreditorNumber")
    block_reason_id: Optional[StrictInt] = Field(default=None, alias="BlockReasonID")
    blocked_by_person_id: Optional[StrictInt] = Field(default=None, alias="BlockedByPersonID")
    blocked_at: Optional[datetime] = Field(default=None, alias="BlockedAt")
    co_c_registration_no: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=20)]] = Field(default=None, alias="CoCRegistrationNo")
    bank_id: Optional[StrictInt] = Field(default=None, alias="BankID")
    iban: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=34)]] = Field(default=None, alias="IBAN")
    invoice_relation_id: Optional[StrictInt] = Field(default=None, alias="InvoiceRelationID")
    payment_condition_id: Optional[StrictInt] = Field(default=None, alias="PaymentConditionID")
    default_payment_method_id: Optional[StrictInt] = Field(default=None, alias="DefaultPaymentMethodID")
    invoice_reference_required: Optional[StrictBool] = Field(default=None, alias="InvoiceReferenceRequired")
    sale_vat_id: Optional[StrictInt] = Field(default=None, alias="SaleVatID")
    default_currency_id: Optional[StrictInt] = Field(default=None, alias="DefaultCurrencyID")
    debtors_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="DebtorsGeneralLedgerID")
    creditors_general_ledger_id: Optional[StrictInt] = Field(default=None, alias="CreditorsGeneralLedgerID")
    invoice_note: Optional[StrictStr] = Field(default=None, alias="InvoiceNote")
    main_address: Optional[CentixAPIDTOAddress] = Field(default=None, alias="MainAddress")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Name", "Name2", "RelationGroupID", "PrivatePerson", "RelationStatusID", "LanguageID", "Note", "ParentRelationID", "AutoNumberPrefix", "PhoneNumber", "FaxNumber", "EmailAddress", "Website", "MainContactID", "VatNo", "DebtorNumber", "CreditorNumber", "BlockReasonID", "BlockedByPersonID", "BlockedAt", "CoCRegistrationNo", "BankID", "IBAN", "InvoiceRelationID", "PaymentConditionID", "DefaultPaymentMethodID", "InvoiceReferenceRequired", "SaleVatID", "DefaultCurrencyID", "DebtorsGeneralLedgerID", "CreditorsGeneralLedgerID", "InvoiceNote", "MainAddress"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateRelation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of main_address
        if self.main_address:
            _dict['MainAddress'] = self.main_address.to_dict()
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if relation_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_group_id is None and "relation_group_id" in self.model_fields_set:
            _dict['RelationGroupID'] = None

        # set to None if relation_status_id (nullable) is None
        # and model_fields_set contains the field
        if self.relation_status_id is None and "relation_status_id" in self.model_fields_set:
            _dict['RelationStatusID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if parent_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.parent_relation_id is None and "parent_relation_id" in self.model_fields_set:
            _dict['ParentRelationID'] = None

        # set to None if main_contact_id (nullable) is None
        # and model_fields_set contains the field
        if self.main_contact_id is None and "main_contact_id" in self.model_fields_set:
            _dict['MainContactID'] = None

        # set to None if block_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.block_reason_id is None and "block_reason_id" in self.model_fields_set:
            _dict['BlockReasonID'] = None

        # set to None if blocked_by_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.blocked_by_person_id is None and "blocked_by_person_id" in self.model_fields_set:
            _dict['BlockedByPersonID'] = None

        # set to None if blocked_at (nullable) is None
        # and model_fields_set contains the field
        if self.blocked_at is None and "blocked_at" in self.model_fields_set:
            _dict['BlockedAt'] = None

        # set to None if bank_id (nullable) is None
        # and model_fields_set contains the field
        if self.bank_id is None and "bank_id" in self.model_fields_set:
            _dict['BankID'] = None

        # set to None if invoice_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_relation_id is None and "invoice_relation_id" in self.model_fields_set:
            _dict['InvoiceRelationID'] = None

        # set to None if payment_condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.payment_condition_id is None and "payment_condition_id" in self.model_fields_set:
            _dict['PaymentConditionID'] = None

        # set to None if default_payment_method_id (nullable) is None
        # and model_fields_set contains the field
        if self.default_payment_method_id is None and "default_payment_method_id" in self.model_fields_set:
            _dict['DefaultPaymentMethodID'] = None

        # set to None if sale_vat_id (nullable) is None
        # and model_fields_set contains the field
        if self.sale_vat_id is None and "sale_vat_id" in self.model_fields_set:
            _dict['SaleVatID'] = None

        # set to None if default_currency_id (nullable) is None
        # and model_fields_set contains the field
        if self.default_currency_id is None and "default_currency_id" in self.model_fields_set:
            _dict['DefaultCurrencyID'] = None

        # set to None if debtors_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.debtors_general_ledger_id is None and "debtors_general_ledger_id" in self.model_fields_set:
            _dict['DebtorsGeneralLedgerID'] = None

        # set to None if creditors_general_ledger_id (nullable) is None
        # and model_fields_set contains the field
        if self.creditors_general_ledger_id is None and "creditors_general_ledger_id" in self.model_fields_set:
            _dict['CreditorsGeneralLedgerID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOUpdateRelation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Name": obj.get("Name"),
            "Name2": obj.get("Name2"),
            "RelationGroupID": obj.get("RelationGroupID"),
            "PrivatePerson": obj.get("PrivatePerson"),
            "RelationStatusID": obj.get("RelationStatusID"),
            "LanguageID": obj.get("LanguageID"),
            "Note": obj.get("Note"),
            "ParentRelationID": obj.get("ParentRelationID"),
            "AutoNumberPrefix": obj.get("AutoNumberPrefix"),
            "PhoneNumber": obj.get("PhoneNumber"),
            "FaxNumber": obj.get("FaxNumber"),
            "EmailAddress": obj.get("EmailAddress"),
            "Website": obj.get("Website"),
            "MainContactID": obj.get("MainContactID"),
            "VatNo": obj.get("VatNo"),
            "DebtorNumber": obj.get("DebtorNumber"),
            "CreditorNumber": obj.get("CreditorNumber"),
            "BlockReasonID": obj.get("BlockReasonID"),
            "BlockedByPersonID": obj.get("BlockedByPersonID"),
            "BlockedAt": obj.get("BlockedAt"),
            "CoCRegistrationNo": obj.get("CoCRegistrationNo"),
            "BankID": obj.get("BankID"),
            "IBAN": obj.get("IBAN"),
            "InvoiceRelationID": obj.get("InvoiceRelationID"),
            "PaymentConditionID": obj.get("PaymentConditionID"),
            "DefaultPaymentMethodID": obj.get("DefaultPaymentMethodID"),
            "InvoiceReferenceRequired": obj.get("InvoiceReferenceRequired"),
            "SaleVatID": obj.get("SaleVatID"),
            "DefaultCurrencyID": obj.get("DefaultCurrencyID"),
            "DebtorsGeneralLedgerID": obj.get("DebtorsGeneralLedgerID"),
            "CreditorsGeneralLedgerID": obj.get("CreditorsGeneralLedgerID"),
            "InvoiceNote": obj.get("InvoiceNote"),
            "MainAddress": CentixAPIDTOAddress.from_dict(obj["MainAddress"]) if obj.get("MainAddress") is not None else None
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


