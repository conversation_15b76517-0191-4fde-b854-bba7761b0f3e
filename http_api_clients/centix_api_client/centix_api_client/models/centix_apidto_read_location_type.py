# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadLocationType(BaseModel):
    """
    CentixAPIDTOReadLocationType
    """ # noqa: E501
    location_group_id: Optional[StrictInt] = Field(default=None, alias="LocationGroupID")
    can_be_root: Optional[StrictBool] = Field(default=None, alias="CanBeRoot")
    unique_object_id: Optional[StrictBool] = Field(default=None, alias="UniqueObjectID")
    has_address: Optional[StrictBool] = Field(default=None, alias="HasAddress")
    location_kind: Optional[StrictInt] = Field(default=None, description="0 = Default, 1 = Person, 2 = Project", alias="LocationKind")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["LocationGroupID", "CanBeRoot", "UniqueObjectID", "HasAddress", "LocationKind", "ID", "Descr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('location_kind')
    def location_kind_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2]):
            raise ValueError("must be one of enum values (0, 1, 2)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadLocationType from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if location_group_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_group_id is None and "location_group_id" in self.model_fields_set:
            _dict['LocationGroupID'] = None

        # set to None if unique_object_id (nullable) is None
        # and model_fields_set contains the field
        if self.unique_object_id is None and "unique_object_id" in self.model_fields_set:
            _dict['UniqueObjectID'] = None

        # set to None if has_address (nullable) is None
        # and model_fields_set contains the field
        if self.has_address is None and "has_address" in self.model_fields_set:
            _dict['HasAddress'] = None

        # set to None if location_kind (nullable) is None
        # and model_fields_set contains the field
        if self.location_kind is None and "location_kind" in self.model_fields_set:
            _dict['LocationKind'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadLocationType from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "LocationGroupID": obj.get("LocationGroupID"),
            "CanBeRoot": obj.get("CanBeRoot"),
            "UniqueObjectID": obj.get("UniqueObjectID"),
            "HasAddress": obj.get("HasAddress"),
            "LocationKind": obj.get("LocationKind"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


