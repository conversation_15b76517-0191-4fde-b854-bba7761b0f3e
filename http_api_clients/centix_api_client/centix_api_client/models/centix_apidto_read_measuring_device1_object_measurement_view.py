# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView(BaseModel):
    """
    CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView
    """ # noqa: E501
    measuring_device1_object_measurement_schedule_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1ObjectMeasurementScheduleID")
    measuring_device1_object_measurement_function_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1ObjectMeasurementFunctionID")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    measuring_device1_measurement_function_id: Optional[StrictInt] = Field(default=None, alias="MeasuringDevice1MeasurementFunctionID")
    result_date: Optional[datetime] = Field(default=None, alias="ResultDate")
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    value: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, alias="Value")
    quantity_unit_abbreviation: Optional[StrictStr] = Field(default=None, alias="QuantityUnit_Abbreviation")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["MeasuringDevice1ObjectMeasurementScheduleID", "MeasuringDevice1ObjectMeasurementFunctionID", "ObjectID", "MeasuringDevice1MeasurementFunctionID", "ResultDate", "ID", "Descr", "Value", "QuantityUnit_Abbreviation", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if measuring_device1_object_measurement_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_object_measurement_schedule_id is None and "measuring_device1_object_measurement_schedule_id" in self.model_fields_set:
            _dict['MeasuringDevice1ObjectMeasurementScheduleID'] = None

        # set to None if measuring_device1_object_measurement_function_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_object_measurement_function_id is None and "measuring_device1_object_measurement_function_id" in self.model_fields_set:
            _dict['MeasuringDevice1ObjectMeasurementFunctionID'] = None

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if measuring_device1_measurement_function_id (nullable) is None
        # and model_fields_set contains the field
        if self.measuring_device1_measurement_function_id is None and "measuring_device1_measurement_function_id" in self.model_fields_set:
            _dict['MeasuringDevice1MeasurementFunctionID'] = None

        # set to None if result_date (nullable) is None
        # and model_fields_set contains the field
        if self.result_date is None and "result_date" in self.model_fields_set:
            _dict['ResultDate'] = None

        # set to None if value (nullable) is None
        # and model_fields_set contains the field
        if self.value is None and "value" in self.model_fields_set:
            _dict['Value'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "MeasuringDevice1ObjectMeasurementScheduleID": obj.get("MeasuringDevice1ObjectMeasurementScheduleID"),
            "MeasuringDevice1ObjectMeasurementFunctionID": obj.get("MeasuringDevice1ObjectMeasurementFunctionID"),
            "ObjectID": obj.get("ObjectID"),
            "MeasuringDevice1MeasurementFunctionID": obj.get("MeasuringDevice1MeasurementFunctionID"),
            "ResultDate": obj.get("ResultDate"),
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "Value": obj.get("Value"),
            "QuantityUnit_Abbreviation": obj.get("QuantityUnit_Abbreviation"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


