# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectLocation(BaseModel):
    """
    CentixAPIDTOReadObjectLocation
    """ # noqa: E501
    location_id: Optional[StrictInt] = Field(default=None, alias="LocationID")
    lock_comment: Optional[StrictStr] = Field(default=None, alias="LockComment")
    locked: Optional[StrictBool] = Field(default=None, alias="Locked")
    lock_key: Optional[StrictInt] = Field(default=None, alias="LockKey")
    lock_reason_id: Optional[StrictInt] = Field(default=None, alias="LockReasonID")
    lock_user_id: Optional[StrictInt] = Field(default=None, alias="LockUserID")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    quantity: Optional[StrictInt] = Field(default=None, alias="Quantity")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["LocationID", "LockComment", "Locked", "LockKey", "LockReasonID", "LockUserID", "ObjectID", "Quantity", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectLocation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if lock_key (nullable) is None
        # and model_fields_set contains the field
        if self.lock_key is None and "lock_key" in self.model_fields_set:
            _dict['LockKey'] = None

        # set to None if lock_reason_id (nullable) is None
        # and model_fields_set contains the field
        if self.lock_reason_id is None and "lock_reason_id" in self.model_fields_set:
            _dict['LockReasonID'] = None

        # set to None if lock_user_id (nullable) is None
        # and model_fields_set contains the field
        if self.lock_user_id is None and "lock_user_id" in self.model_fields_set:
            _dict['LockUserID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectLocation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "LocationID": obj.get("LocationID"),
            "LockComment": obj.get("LockComment"),
            "Locked": obj.get("Locked"),
            "LockKey": obj.get("LockKey"),
            "LockReasonID": obj.get("LockReasonID"),
            "LockUserID": obj.get("LockUserID"),
            "ObjectID": obj.get("ObjectID"),
            "Quantity": obj.get("Quantity"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


