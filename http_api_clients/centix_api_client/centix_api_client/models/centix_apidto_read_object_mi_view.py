# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadObjectMIView(BaseModel):
    """
    CentixAPIDTOReadObjectMIView
    """ # noqa: E501
    mi_id: Optional[StrictStr] = Field(default=None, alias="MI_ID")
    mi_status: Optional[StrictInt] = Field(default=None, description="0 = Open, 1 = Finished, 2 = Waiting, 3 = ReOpened", alias="MI_Status")
    mi_result: Optional[StrictInt] = Field(default=None, description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable", alias="MI_Result")
    mi_result_date: Optional[datetime] = Field(default=None, alias="MI_ResultDate")
    object_id: Optional[StrictStr] = Field(default=None, alias="Object_ID")
    object_descr: Optional[StrictStr] = Field(default=None, alias="Object_Descr")
    object_object_owner_id: Optional[StrictInt] = Field(default=None, alias="Object_ObjectOwnerID")
    object_object_owner_id: Optional[StrictStr] = Field(default=None, alias="Object_ObjectOwner_ID")
    object_object_owner_name: Optional[StrictStr] = Field(default=None, alias="Object_ObjectOwner_Name")
    after_condition_id: Optional[StrictInt] = Field(default=None, alias="AfterConditionID")
    before_condition_id: Optional[StrictInt] = Field(default=None, alias="BeforeConditionID")
    external_note: Optional[StrictStr] = Field(default=None, alias="ExternalNote")
    ignore_in_object00: Optional[StrictBool] = Field(default=None, alias="IgnoreInObject00")
    inspection_person_id: Optional[StrictInt] = Field(default=None, alias="InspectionPersonID")
    inspection_relation_id: Optional[StrictInt] = Field(default=None, alias="InspectionRelationID")
    internal_note: Optional[StrictStr] = Field(default=None, alias="InternalNote")
    location_id: Optional[StrictInt] = Field(default=None, alias="LocationID")
    mi_interval_counted: Optional[StrictBool] = Field(default=None, alias="MIIntervalCounted")
    mi_valid_until_date: Optional[datetime] = Field(default=None, alias="MIValidUntilDate")
    next_run: Optional[datetime] = Field(default=None, alias="NextRun")
    no_result_available: Optional[StrictBool] = Field(default=None, alias="NoResultAvailable")
    object_id: Optional[StrictInt] = Field(default=None, alias="ObjectID")
    object_mi_schedule_id: Optional[StrictInt] = Field(default=None, alias="ObjectMIScheduleID")
    object_mi_schedule_next_run: Optional[datetime] = Field(default=None, alias="ObjectMIScheduleNextRun")
    result: Optional[StrictInt] = Field(default=None, description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable", alias="Result")
    result_date: Optional[datetime] = Field(default=None, alias="ResultDate")
    result_note: Optional[StrictStr] = Field(default=None, alias="ResultNote")
    skip_object_in_mi_answer: Optional[StrictBool] = Field(default=None, alias="SkipObjectInMIAnswer")
    visit_counted: Optional[StrictBool] = Field(default=None, alias="VisitCounted")
    location_relation_id: Optional[StrictInt] = Field(default=None, alias="LocationRelationID")
    location_relation_descr: Optional[StrictStr] = Field(default=None, alias="LocationRelationDescr")
    location_descr: Optional[StrictStr] = Field(default=None, alias="LocationDescr")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["MI_ID", "MI_Status", "MI_Result", "MI_ResultDate", "Object_ID", "Object_Descr", "Object_ObjectOwnerID", "Object_ObjectOwner_ID", "Object_ObjectOwner_Name", "AfterConditionID", "BeforeConditionID", "ExternalNote", "IgnoreInObject00", "InspectionPersonID", "InspectionRelationID", "InternalNote", "LocationID", "MIIntervalCounted", "MIValidUntilDate", "NextRun", "NoResultAvailable", "ObjectID", "ObjectMIScheduleID", "ObjectMIScheduleNextRun", "Result", "ResultDate", "ResultNote", "SkipObjectInMIAnswer", "VisitCounted", "LocationRelationID", "LocationRelationDescr", "LocationDescr", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('mi_status')
    def mi_status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    @field_validator('mi_result')
    def mi_result_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    @field_validator('result')
    def result_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3]):
            raise ValueError("must be one of enum values (0, 1, 2, 3)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectMIView from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if mi_result (nullable) is None
        # and model_fields_set contains the field
        if self.mi_result is None and "mi_result" in self.model_fields_set:
            _dict['MI_Result'] = None

        # set to None if mi_result_date (nullable) is None
        # and model_fields_set contains the field
        if self.mi_result_date is None and "mi_result_date" in self.model_fields_set:
            _dict['MI_ResultDate'] = None

        # set to None if after_condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.after_condition_id is None and "after_condition_id" in self.model_fields_set:
            _dict['AfterConditionID'] = None

        # set to None if before_condition_id (nullable) is None
        # and model_fields_set contains the field
        if self.before_condition_id is None and "before_condition_id" in self.model_fields_set:
            _dict['BeforeConditionID'] = None

        # set to None if ignore_in_object00 (nullable) is None
        # and model_fields_set contains the field
        if self.ignore_in_object00 is None and "ignore_in_object00" in self.model_fields_set:
            _dict['IgnoreInObject00'] = None

        # set to None if inspection_person_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_person_id is None and "inspection_person_id" in self.model_fields_set:
            _dict['InspectionPersonID'] = None

        # set to None if inspection_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.inspection_relation_id is None and "inspection_relation_id" in self.model_fields_set:
            _dict['InspectionRelationID'] = None

        # set to None if location_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_id is None and "location_id" in self.model_fields_set:
            _dict['LocationID'] = None

        # set to None if mi_interval_counted (nullable) is None
        # and model_fields_set contains the field
        if self.mi_interval_counted is None and "mi_interval_counted" in self.model_fields_set:
            _dict['MIIntervalCounted'] = None

        # set to None if mi_valid_until_date (nullable) is None
        # and model_fields_set contains the field
        if self.mi_valid_until_date is None and "mi_valid_until_date" in self.model_fields_set:
            _dict['MIValidUntilDate'] = None

        # set to None if next_run (nullable) is None
        # and model_fields_set contains the field
        if self.next_run is None and "next_run" in self.model_fields_set:
            _dict['NextRun'] = None

        # set to None if no_result_available (nullable) is None
        # and model_fields_set contains the field
        if self.no_result_available is None and "no_result_available" in self.model_fields_set:
            _dict['NoResultAvailable'] = None

        # set to None if object_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_id is None and "object_id" in self.model_fields_set:
            _dict['ObjectID'] = None

        # set to None if object_mi_schedule_id (nullable) is None
        # and model_fields_set contains the field
        if self.object_mi_schedule_id is None and "object_mi_schedule_id" in self.model_fields_set:
            _dict['ObjectMIScheduleID'] = None

        # set to None if object_mi_schedule_next_run (nullable) is None
        # and model_fields_set contains the field
        if self.object_mi_schedule_next_run is None and "object_mi_schedule_next_run" in self.model_fields_set:
            _dict['ObjectMIScheduleNextRun'] = None

        # set to None if result (nullable) is None
        # and model_fields_set contains the field
        if self.result is None and "result" in self.model_fields_set:
            _dict['Result'] = None

        # set to None if result_date (nullable) is None
        # and model_fields_set contains the field
        if self.result_date is None and "result_date" in self.model_fields_set:
            _dict['ResultDate'] = None

        # set to None if skip_object_in_mi_answer (nullable) is None
        # and model_fields_set contains the field
        if self.skip_object_in_mi_answer is None and "skip_object_in_mi_answer" in self.model_fields_set:
            _dict['SkipObjectInMIAnswer'] = None

        # set to None if visit_counted (nullable) is None
        # and model_fields_set contains the field
        if self.visit_counted is None and "visit_counted" in self.model_fields_set:
            _dict['VisitCounted'] = None

        # set to None if location_relation_id (nullable) is None
        # and model_fields_set contains the field
        if self.location_relation_id is None and "location_relation_id" in self.model_fields_set:
            _dict['LocationRelationID'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadObjectMIView from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "MI_ID": obj.get("MI_ID"),
            "MI_Status": obj.get("MI_Status"),
            "MI_Result": obj.get("MI_Result"),
            "MI_ResultDate": obj.get("MI_ResultDate"),
            "Object_ID": obj.get("Object_ID"),
            "Object_Descr": obj.get("Object_Descr"),
            "Object_ObjectOwnerID": obj.get("Object_ObjectOwnerID"),
            "Object_ObjectOwner_ID": obj.get("Object_ObjectOwner_ID"),
            "Object_ObjectOwner_Name": obj.get("Object_ObjectOwner_Name"),
            "AfterConditionID": obj.get("AfterConditionID"),
            "BeforeConditionID": obj.get("BeforeConditionID"),
            "ExternalNote": obj.get("ExternalNote"),
            "IgnoreInObject00": obj.get("IgnoreInObject00"),
            "InspectionPersonID": obj.get("InspectionPersonID"),
            "InspectionRelationID": obj.get("InspectionRelationID"),
            "InternalNote": obj.get("InternalNote"),
            "LocationID": obj.get("LocationID"),
            "MIIntervalCounted": obj.get("MIIntervalCounted"),
            "MIValidUntilDate": obj.get("MIValidUntilDate"),
            "NextRun": obj.get("NextRun"),
            "NoResultAvailable": obj.get("NoResultAvailable"),
            "ObjectID": obj.get("ObjectID"),
            "ObjectMIScheduleID": obj.get("ObjectMIScheduleID"),
            "ObjectMIScheduleNextRun": obj.get("ObjectMIScheduleNextRun"),
            "Result": obj.get("Result"),
            "ResultDate": obj.get("ResultDate"),
            "ResultNote": obj.get("ResultNote"),
            "SkipObjectInMIAnswer": obj.get("SkipObjectInMIAnswer"),
            "VisitCounted": obj.get("VisitCounted"),
            "LocationRelationID": obj.get("LocationRelationID"),
            "LocationRelationDescr": obj.get("LocationRelationDescr"),
            "LocationDescr": obj.get("LocationDescr"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


