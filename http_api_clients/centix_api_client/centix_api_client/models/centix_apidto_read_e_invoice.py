# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadEInvoice(BaseModel):
    """
    CentixAPIDTOReadEInvoice
    """ # noqa: E501
    invoice_type: Optional[StrictInt] = Field(default=None, description="0 = WorkingTicket, 1 = Contract, 2 = Order, 3 = WorkFlowItem, 4 = Invoice", alias="InvoiceType")
    administration_code: Optional[StrictStr] = Field(default=None, alias="AdministrationCode")
    administration_name: Optional[StrictStr] = Field(default=None, alias="AdministrationName")
    reference: Optional[StrictStr] = Field(default=None, alias="Reference")
    description: Optional[StrictStr] = Field(default=None, alias="Description")
    selection_code: Optional[StrictStr] = Field(default=None, alias="SelectionCode")
    debtor_code: Optional[StrictStr] = Field(default=None, alias="DebtorCode")
    debtor_name: Optional[StrictStr] = Field(default=None, alias="DebtorName")
    payment_condition_code: Optional[StrictStr] = Field(default=None, alias="PaymentConditionCode")
    order_date: Optional[datetime] = Field(default=None, alias="OrderDate")
    deliver_date: Optional[datetime] = Field(default=None, alias="DeliverDate")
    project_code: Optional[StrictStr] = Field(default=None, alias="ProjectCode")
    currency_code: Optional[StrictStr] = Field(default=None, alias="CurrencyCode")
    ware_house_code: Optional[StrictStr] = Field(default=None, alias="WareHouseCode")
    resource_code: Optional[StrictStr] = Field(default=None, alias="ResourceCode")
    assigned_to_person_personnel_no: Optional[StrictStr] = Field(default=None, alias="AssignedToPersonPersonnelNo")
    assigned_to_person_code: Optional[StrictStr] = Field(default=None, alias="AssignedToPersonCode")
    assigned_to_person_name: Optional[StrictStr] = Field(default=None, alias="AssignedToPersonName")
    deliver_to_code: Optional[StrictStr] = Field(default=None, alias="DeliverToCode")
    deliver_to_name: Optional[StrictStr] = Field(default=None, alias="DeliverToName")
    deliver_address_full: Optional[StrictStr] = Field(default=None, alias="DeliverAddressFull")
    contract_id: Optional[StrictStr] = Field(default=None, alias="Contract_ID")
    order_id: Optional[StrictStr] = Field(default=None, alias="Order_ID")
    working_ticket_id: Optional[StrictStr] = Field(default=None, alias="WorkingTicket_ID")
    work_flow_id: Optional[StrictStr] = Field(default=None, alias="WorkFlow_ID")
    process_state: Optional[StrictInt] = Field(default=None, description="0 = None, 1 = Ignored, 2 = ValidationError, 3 = Failed, 4 = OK", alias="ProcessState")
    result_note: Optional[StrictStr] = Field(default=None, alias="ResultNote")
    processed_on: Optional[datetime] = Field(default=None, alias="ProcessedOn")
    processed_by: Optional[StrictStr] = Field(default=None, alias="ProcessedBy")
    invoice_id: Optional[StrictStr] = Field(default=None, alias="Invoice_ID")
    invoice_date: Optional[datetime] = Field(default=None, alias="InvoiceDate")
    cost_code: Optional[StrictStr] = Field(default=None, alias="CostCode")
    cost_category: Optional[StrictStr] = Field(default=None, alias="CostCategory")
    payment_reference: Optional[StrictStr] = Field(default=None, alias="PaymentReference")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["InvoiceType", "AdministrationCode", "AdministrationName", "Reference", "Description", "SelectionCode", "DebtorCode", "DebtorName", "PaymentConditionCode", "OrderDate", "DeliverDate", "ProjectCode", "CurrencyCode", "WareHouseCode", "ResourceCode", "AssignedToPersonPersonnelNo", "AssignedToPersonCode", "AssignedToPersonName", "DeliverToCode", "DeliverToName", "DeliverAddressFull", "Contract_ID", "Order_ID", "WorkingTicket_ID", "WorkFlow_ID", "ProcessState", "ResultNote", "ProcessedOn", "ProcessedBy", "Invoice_ID", "InvoiceDate", "CostCode", "CostCategory", "PaymentReference", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('invoice_type')
    def invoice_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4)")
        return value

    @field_validator('process_state')
    def process_state_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2, 3, 4]):
            raise ValueError("must be one of enum values (0, 1, 2, 3, 4)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadEInvoice from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if order_date (nullable) is None
        # and model_fields_set contains the field
        if self.order_date is None and "order_date" in self.model_fields_set:
            _dict['OrderDate'] = None

        # set to None if deliver_date (nullable) is None
        # and model_fields_set contains the field
        if self.deliver_date is None and "deliver_date" in self.model_fields_set:
            _dict['DeliverDate'] = None

        # set to None if processed_on (nullable) is None
        # and model_fields_set contains the field
        if self.processed_on is None and "processed_on" in self.model_fields_set:
            _dict['ProcessedOn'] = None

        # set to None if invoice_date (nullable) is None
        # and model_fields_set contains the field
        if self.invoice_date is None and "invoice_date" in self.model_fields_set:
            _dict['InvoiceDate'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadEInvoice from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "InvoiceType": obj.get("InvoiceType"),
            "AdministrationCode": obj.get("AdministrationCode"),
            "AdministrationName": obj.get("AdministrationName"),
            "Reference": obj.get("Reference"),
            "Description": obj.get("Description"),
            "SelectionCode": obj.get("SelectionCode"),
            "DebtorCode": obj.get("DebtorCode"),
            "DebtorName": obj.get("DebtorName"),
            "PaymentConditionCode": obj.get("PaymentConditionCode"),
            "OrderDate": obj.get("OrderDate"),
            "DeliverDate": obj.get("DeliverDate"),
            "ProjectCode": obj.get("ProjectCode"),
            "CurrencyCode": obj.get("CurrencyCode"),
            "WareHouseCode": obj.get("WareHouseCode"),
            "ResourceCode": obj.get("ResourceCode"),
            "AssignedToPersonPersonnelNo": obj.get("AssignedToPersonPersonnelNo"),
            "AssignedToPersonCode": obj.get("AssignedToPersonCode"),
            "AssignedToPersonName": obj.get("AssignedToPersonName"),
            "DeliverToCode": obj.get("DeliverToCode"),
            "DeliverToName": obj.get("DeliverToName"),
            "DeliverAddressFull": obj.get("DeliverAddressFull"),
            "Contract_ID": obj.get("Contract_ID"),
            "Order_ID": obj.get("Order_ID"),
            "WorkingTicket_ID": obj.get("WorkingTicket_ID"),
            "WorkFlow_ID": obj.get("WorkFlow_ID"),
            "ProcessState": obj.get("ProcessState"),
            "ResultNote": obj.get("ResultNote"),
            "ProcessedOn": obj.get("ProcessedOn"),
            "ProcessedBy": obj.get("ProcessedBy"),
            "Invoice_ID": obj.get("Invoice_ID"),
            "InvoiceDate": obj.get("InvoiceDate"),
            "CostCode": obj.get("CostCode"),
            "CostCategory": obj.get("CostCategory"),
            "PaymentReference": obj.get("PaymentReference"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


