# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadDocument(BaseModel):
    """
    CentixAPIDTOReadDocument
    """ # noqa: E501
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    document_kind_id: Optional[StrictInt] = Field(default=None, alias="DocumentKindID")
    language_id: Optional[StrictInt] = Field(default=None, alias="LanguageID")
    comment: Optional[StrictStr] = Field(default=None, alias="Comment")
    expire_date: Optional[datetime] = Field(default=None, alias="ExpireDate")
    archive: Optional[StrictBool] = Field(default=None, alias="Archive")
    archived_at: Optional[datetime] = Field(default=None, alias="ArchivedAt")
    file_uri_type: Optional[StrictInt] = Field(default=None, description="0 = URL, 1 = UNC_PATH, 2 = RELATIVE_PATH", alias="FileUriType")
    file_name: Optional[StrictStr] = Field(default=None, alias="FileName")
    file_extension: Optional[StrictStr] = Field(default=None, alias="FileExtension")
    file_checksum: Optional[StrictStr] = Field(default=None, alias="FileChecksum")
    file_size: Optional[StrictInt] = Field(default=None, alias="FileSize")
    auto_rendered_report: Optional[StrictBool] = Field(default=None, alias="AutoRenderedReport")
    document_handler_url: Optional[StrictStr] = Field(default=None, alias="DocumentHandlerURL")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Descr", "DocumentKindID", "LanguageID", "Comment", "ExpireDate", "Archive", "ArchivedAt", "FileUriType", "FileName", "FileExtension", "FileChecksum", "FileSize", "AutoRenderedReport", "DocumentHandlerURL", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('file_uri_type')
    def file_uri_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([0, 1, 2]):
            raise ValueError("must be one of enum values (0, 1, 2)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDocument from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if document_kind_id (nullable) is None
        # and model_fields_set contains the field
        if self.document_kind_id is None and "document_kind_id" in self.model_fields_set:
            _dict['DocumentKindID'] = None

        # set to None if language_id (nullable) is None
        # and model_fields_set contains the field
        if self.language_id is None and "language_id" in self.model_fields_set:
            _dict['LanguageID'] = None

        # set to None if expire_date (nullable) is None
        # and model_fields_set contains the field
        if self.expire_date is None and "expire_date" in self.model_fields_set:
            _dict['ExpireDate'] = None

        # set to None if archived_at (nullable) is None
        # and model_fields_set contains the field
        if self.archived_at is None and "archived_at" in self.model_fields_set:
            _dict['ArchivedAt'] = None

        # set to None if file_uri_type (nullable) is None
        # and model_fields_set contains the field
        if self.file_uri_type is None and "file_uri_type" in self.model_fields_set:
            _dict['FileUriType'] = None

        # set to None if file_size (nullable) is None
        # and model_fields_set contains the field
        if self.file_size is None and "file_size" in self.model_fields_set:
            _dict['FileSize'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadDocument from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "DocumentKindID": obj.get("DocumentKindID"),
            "LanguageID": obj.get("LanguageID"),
            "Comment": obj.get("Comment"),
            "ExpireDate": obj.get("ExpireDate"),
            "Archive": obj.get("Archive"),
            "ArchivedAt": obj.get("ArchivedAt"),
            "FileUriType": obj.get("FileUriType"),
            "FileName": obj.get("FileName"),
            "FileExtension": obj.get("FileExtension"),
            "FileChecksum": obj.get("FileChecksum"),
            "FileSize": obj.get("FileSize"),
            "AutoRenderedReport": obj.get("AutoRenderedReport"),
            "DocumentHandlerURL": obj.get("DocumentHandlerURL"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


