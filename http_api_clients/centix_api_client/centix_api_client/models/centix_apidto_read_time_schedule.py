# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class CentixAPIDTOReadTimeSchedule(BaseModel):
    """
    CentixAPIDTOReadTimeSchedule
    """ # noqa: E501
    id: Optional[StrictStr] = Field(default=None, alias="ID")
    descr: Optional[StrictStr] = Field(default=None, alias="Descr")
    schedule_type: Optional[StrictInt] = Field(default=None, description="1 = Daily, 2 = Weekly, 3 = Monthly, 4 = Free", alias="ScheduleType")
    occurs_once: Optional[StrictBool] = Field(default=None, alias="OccursOnce")
    occurs_once_at: Optional[datetime] = Field(default=None, alias="OccursOnceAt")
    occurs_every: Optional[StrictInt] = Field(default=None, alias="OccursEvery")
    occurs_every_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="OccursEveryUnit")
    occurs_begin_time: Optional[datetime] = Field(default=None, alias="OccursBeginTime")
    occurs_end_time: Optional[datetime] = Field(default=None, alias="OccursEndTime")
    weekly_on_monday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnMonday")
    weekly_on_tuesday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnTuesday")
    weekly_on_wednesday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnWednesday")
    weekly_on_thursday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnThursday")
    weekly_on_friday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnFriday")
    weekly_on_saturday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnSaturday")
    weekly_on_sunday: Optional[StrictBool] = Field(default=None, alias="WeeklyOnSunday")
    monthly_on_week_day: Optional[StrictBool] = Field(default=None, alias="MonthlyOnWeekDay")
    monthly_on_week_day_no: Optional[StrictInt] = Field(default=None, alias="MonthlyOnWeekDayNo")
    monthly_on_week_day_index: Optional[StrictInt] = Field(default=None, alias="MonthlyOnWeekDayIndex")
    monthly_on_first_day: Optional[StrictBool] = Field(default=None, alias="MonthlyOnFirstDay")
    monthly_on_last_day: Optional[StrictBool] = Field(default=None, alias="MonthlyOnLastDay")
    calc_next_run_use_before_plan_date_offset: Optional[StrictBool] = Field(default=None, alias="CalcNextRunUseBeforePlanDateOffset")
    calc_next_run_before_plan_date_offset: Optional[StrictInt] = Field(default=None, alias="CalcNextRunBeforePlanDateOffset")
    calc_next_run_before_plan_date_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="CalcNextRunBeforePlanDateUnit")
    calc_next_run_use_after_plan_date_offset: Optional[StrictBool] = Field(default=None, alias="CalcNextRunUseAfterPlanDateOffset")
    calc_next_run_after_plan_date_offset: Optional[StrictInt] = Field(default=None, alias="CalcNextRunAfterPlanDateOffset")
    calc_next_run_after_plan_date_unit: Optional[StrictInt] = Field(default=None, description="1 = Minutes, 2 = Hours, 3 = Days, 4 = Weeks, 5 = Months, 6 = Quarters, 7 = Years, 8 = HalfYears", alias="CalcNextRunAfterPlanDateUnit")
    calc_next_run_use_plan_date_same_month: Optional[StrictBool] = Field(default=None, alias="CalcNextRunUsePlanDateSameMonth")
    auto_id: Optional[StrictInt] = Field(default=None, alias="AutoID")
    created_by: Optional[StrictStr] = Field(default=None, alias="CreatedBy")
    modified_by: Optional[StrictStr] = Field(default=None, alias="ModifiedBy")
    create_date: Optional[datetime] = Field(default=None, alias="CreateDate")
    time_stamp: Optional[datetime] = Field(default=None, alias="TimeStamp")
    additional_properties: Dict[str, Any] = {}
    __properties: ClassVar[List[str]] = ["ID", "Descr", "ScheduleType", "OccursOnce", "OccursOnceAt", "OccursEvery", "OccursEveryUnit", "OccursBeginTime", "OccursEndTime", "WeeklyOnMonday", "WeeklyOnTuesday", "WeeklyOnWednesday", "WeeklyOnThursday", "WeeklyOnFriday", "WeeklyOnSaturday", "WeeklyOnSunday", "MonthlyOnWeekDay", "MonthlyOnWeekDayNo", "MonthlyOnWeekDayIndex", "MonthlyOnFirstDay", "MonthlyOnLastDay", "CalcNextRunUseBeforePlanDateOffset", "CalcNextRunBeforePlanDateOffset", "CalcNextRunBeforePlanDateUnit", "CalcNextRunUseAfterPlanDateOffset", "CalcNextRunAfterPlanDateOffset", "CalcNextRunAfterPlanDateUnit", "CalcNextRunUsePlanDateSameMonth", "AutoID", "CreatedBy", "ModifiedBy", "CreateDate", "TimeStamp"]

    @field_validator('schedule_type')
    def schedule_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4]):
            raise ValueError("must be one of enum values (1, 2, 3, 4)")
        return value

    @field_validator('occurs_every_unit')
    def occurs_every_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    @field_validator('calc_next_run_before_plan_date_unit')
    def calc_next_run_before_plan_date_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    @field_validator('calc_next_run_after_plan_date_unit')
    def calc_next_run_after_plan_date_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set([1, 2, 3, 4, 5, 6, 7, 8]):
            raise ValueError("must be one of enum values (1, 2, 3, 4, 5, 6, 7, 8)")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadTimeSchedule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * Fields in `self.additional_properties` are added to the output dict.
        """
        excluded_fields: Set[str] = set([
            "additional_properties",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # puts key-value pairs in additional_properties in the top level
        if self.additional_properties is not None:
            for _key, _value in self.additional_properties.items():
                _dict[_key] = _value

        # set to None if schedule_type (nullable) is None
        # and model_fields_set contains the field
        if self.schedule_type is None and "schedule_type" in self.model_fields_set:
            _dict['ScheduleType'] = None

        # set to None if occurs_once (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_once is None and "occurs_once" in self.model_fields_set:
            _dict['OccursOnce'] = None

        # set to None if occurs_once_at (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_once_at is None and "occurs_once_at" in self.model_fields_set:
            _dict['OccursOnceAt'] = None

        # set to None if occurs_every (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_every is None and "occurs_every" in self.model_fields_set:
            _dict['OccursEvery'] = None

        # set to None if occurs_every_unit (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_every_unit is None and "occurs_every_unit" in self.model_fields_set:
            _dict['OccursEveryUnit'] = None

        # set to None if occurs_begin_time (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_begin_time is None and "occurs_begin_time" in self.model_fields_set:
            _dict['OccursBeginTime'] = None

        # set to None if occurs_end_time (nullable) is None
        # and model_fields_set contains the field
        if self.occurs_end_time is None and "occurs_end_time" in self.model_fields_set:
            _dict['OccursEndTime'] = None

        # set to None if weekly_on_monday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_monday is None and "weekly_on_monday" in self.model_fields_set:
            _dict['WeeklyOnMonday'] = None

        # set to None if weekly_on_tuesday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_tuesday is None and "weekly_on_tuesday" in self.model_fields_set:
            _dict['WeeklyOnTuesday'] = None

        # set to None if weekly_on_wednesday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_wednesday is None and "weekly_on_wednesday" in self.model_fields_set:
            _dict['WeeklyOnWednesday'] = None

        # set to None if weekly_on_thursday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_thursday is None and "weekly_on_thursday" in self.model_fields_set:
            _dict['WeeklyOnThursday'] = None

        # set to None if weekly_on_friday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_friday is None and "weekly_on_friday" in self.model_fields_set:
            _dict['WeeklyOnFriday'] = None

        # set to None if weekly_on_saturday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_saturday is None and "weekly_on_saturday" in self.model_fields_set:
            _dict['WeeklyOnSaturday'] = None

        # set to None if weekly_on_sunday (nullable) is None
        # and model_fields_set contains the field
        if self.weekly_on_sunday is None and "weekly_on_sunday" in self.model_fields_set:
            _dict['WeeklyOnSunday'] = None

        # set to None if monthly_on_week_day (nullable) is None
        # and model_fields_set contains the field
        if self.monthly_on_week_day is None and "monthly_on_week_day" in self.model_fields_set:
            _dict['MonthlyOnWeekDay'] = None

        # set to None if monthly_on_week_day_no (nullable) is None
        # and model_fields_set contains the field
        if self.monthly_on_week_day_no is None and "monthly_on_week_day_no" in self.model_fields_set:
            _dict['MonthlyOnWeekDayNo'] = None

        # set to None if monthly_on_week_day_index (nullable) is None
        # and model_fields_set contains the field
        if self.monthly_on_week_day_index is None and "monthly_on_week_day_index" in self.model_fields_set:
            _dict['MonthlyOnWeekDayIndex'] = None

        # set to None if monthly_on_first_day (nullable) is None
        # and model_fields_set contains the field
        if self.monthly_on_first_day is None and "monthly_on_first_day" in self.model_fields_set:
            _dict['MonthlyOnFirstDay'] = None

        # set to None if monthly_on_last_day (nullable) is None
        # and model_fields_set contains the field
        if self.monthly_on_last_day is None and "monthly_on_last_day" in self.model_fields_set:
            _dict['MonthlyOnLastDay'] = None

        # set to None if calc_next_run_use_before_plan_date_offset (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_use_before_plan_date_offset is None and "calc_next_run_use_before_plan_date_offset" in self.model_fields_set:
            _dict['CalcNextRunUseBeforePlanDateOffset'] = None

        # set to None if calc_next_run_before_plan_date_offset (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_before_plan_date_offset is None and "calc_next_run_before_plan_date_offset" in self.model_fields_set:
            _dict['CalcNextRunBeforePlanDateOffset'] = None

        # set to None if calc_next_run_before_plan_date_unit (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_before_plan_date_unit is None and "calc_next_run_before_plan_date_unit" in self.model_fields_set:
            _dict['CalcNextRunBeforePlanDateUnit'] = None

        # set to None if calc_next_run_use_after_plan_date_offset (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_use_after_plan_date_offset is None and "calc_next_run_use_after_plan_date_offset" in self.model_fields_set:
            _dict['CalcNextRunUseAfterPlanDateOffset'] = None

        # set to None if calc_next_run_after_plan_date_offset (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_after_plan_date_offset is None and "calc_next_run_after_plan_date_offset" in self.model_fields_set:
            _dict['CalcNextRunAfterPlanDateOffset'] = None

        # set to None if calc_next_run_after_plan_date_unit (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_after_plan_date_unit is None and "calc_next_run_after_plan_date_unit" in self.model_fields_set:
            _dict['CalcNextRunAfterPlanDateUnit'] = None

        # set to None if calc_next_run_use_plan_date_same_month (nullable) is None
        # and model_fields_set contains the field
        if self.calc_next_run_use_plan_date_same_month is None and "calc_next_run_use_plan_date_same_month" in self.model_fields_set:
            _dict['CalcNextRunUsePlanDateSameMonth'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of CentixAPIDTOReadTimeSchedule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "ID": obj.get("ID"),
            "Descr": obj.get("Descr"),
            "ScheduleType": obj.get("ScheduleType"),
            "OccursOnce": obj.get("OccursOnce"),
            "OccursOnceAt": obj.get("OccursOnceAt"),
            "OccursEvery": obj.get("OccursEvery"),
            "OccursEveryUnit": obj.get("OccursEveryUnit"),
            "OccursBeginTime": obj.get("OccursBeginTime"),
            "OccursEndTime": obj.get("OccursEndTime"),
            "WeeklyOnMonday": obj.get("WeeklyOnMonday"),
            "WeeklyOnTuesday": obj.get("WeeklyOnTuesday"),
            "WeeklyOnWednesday": obj.get("WeeklyOnWednesday"),
            "WeeklyOnThursday": obj.get("WeeklyOnThursday"),
            "WeeklyOnFriday": obj.get("WeeklyOnFriday"),
            "WeeklyOnSaturday": obj.get("WeeklyOnSaturday"),
            "WeeklyOnSunday": obj.get("WeeklyOnSunday"),
            "MonthlyOnWeekDay": obj.get("MonthlyOnWeekDay"),
            "MonthlyOnWeekDayNo": obj.get("MonthlyOnWeekDayNo"),
            "MonthlyOnWeekDayIndex": obj.get("MonthlyOnWeekDayIndex"),
            "MonthlyOnFirstDay": obj.get("MonthlyOnFirstDay"),
            "MonthlyOnLastDay": obj.get("MonthlyOnLastDay"),
            "CalcNextRunUseBeforePlanDateOffset": obj.get("CalcNextRunUseBeforePlanDateOffset"),
            "CalcNextRunBeforePlanDateOffset": obj.get("CalcNextRunBeforePlanDateOffset"),
            "CalcNextRunBeforePlanDateUnit": obj.get("CalcNextRunBeforePlanDateUnit"),
            "CalcNextRunUseAfterPlanDateOffset": obj.get("CalcNextRunUseAfterPlanDateOffset"),
            "CalcNextRunAfterPlanDateOffset": obj.get("CalcNextRunAfterPlanDateOffset"),
            "CalcNextRunAfterPlanDateUnit": obj.get("CalcNextRunAfterPlanDateUnit"),
            "CalcNextRunUsePlanDateSameMonth": obj.get("CalcNextRunUsePlanDateSameMonth"),
            "AutoID": obj.get("AutoID"),
            "CreatedBy": obj.get("CreatedBy"),
            "ModifiedBy": obj.get("ModifiedBy"),
            "CreateDate": obj.get("CreateDate"),
            "TimeStamp": obj.get("TimeStamp")
        })
        # store additional fields in additional_properties
        for _key in obj.keys():
            if _key not in cls.__properties:
                _obj.additional_properties[_key] = obj.get(_key)

        return _obj


