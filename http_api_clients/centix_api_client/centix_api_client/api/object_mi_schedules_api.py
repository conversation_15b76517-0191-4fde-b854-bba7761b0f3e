# coding: utf-8

"""
    Centix API 1.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from datetime import date
from pydantic import Field, StrictBool, StrictBytes, StrictInt, StrictStr, field_validator
from typing import Any, Dict, Optional, Tuple, Union
from typing_extensions import Annotated
from centix_api_client.models.centix_api_core_list_result_centix_apidto_read_object_mi_schedule import CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule
from centix_api_client.models.centix_apidto_create_object_mi_schedule import CentixAPIDTOCreateObjectMISchedule
from centix_api_client.models.centix_apidto_read_object_mi import CentixAPIDTOReadObjectMI
from centix_api_client.models.centix_apidto_read_object_mi_schedule import CentixAPIDTOReadObjectMISchedule

from centix_api_client.api_client import ApiClient, RequestSerialized
from centix_api_client.api_response import ApiResponse
from centix_api_client.rest import RESTResponseType


class ObjectMISchedulesApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    def object_mi_schedules_create(
        self,
        item: Annotated[CentixAPIDTOCreateObjectMISchedule, Field(description="The item.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> CentixAPIDTOReadObjectMISchedule:
        """Creates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param item: The item. (required)
        :type item: CentixAPIDTOCreateObjectMISchedule
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_create_serialize(
            item=item,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationResult",
            '403': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_create_with_http_info(
        self,
        item: Annotated[CentixAPIDTOCreateObjectMISchedule, Field(description="The item.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[CentixAPIDTOReadObjectMISchedule]:
        """Creates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param item: The item. (required)
        :type item: CentixAPIDTOCreateObjectMISchedule
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_create_serialize(
            item=item,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationResult",
            '403': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_create_without_preload_content(
        self,
        item: Annotated[CentixAPIDTOCreateObjectMISchedule, Field(description="The item.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Creates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param item: The item. (required)
        :type item: CentixAPIDTOCreateObjectMISchedule
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_create_serialize(
            item=item,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationResult",
            '403': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_create_serialize(
        self,
        item,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        # process the form parameters
        # process the body parameter
        if item is not None:
            _body_params = item


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/v1/objectmischedules',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    def object_mi_schedules_delete_with_history(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        with_history: Annotated[Optional[StrictBool], Field(description="if set to true [with history].")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> None:
        """Deletes the specified autoid.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param with_history: if set to true [with history].
        :type with_history: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_delete_with_history_serialize(
            autoid=autoid,
            with_history=with_history,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': None,
            '403': None,
            '404': None,
            '500': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_delete_with_history_with_http_info(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        with_history: Annotated[Optional[StrictBool], Field(description="if set to true [with history].")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[None]:
        """Deletes the specified autoid.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param with_history: if set to true [with history].
        :type with_history: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_delete_with_history_serialize(
            autoid=autoid,
            with_history=with_history,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': None,
            '403': None,
            '404': None,
            '500': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_delete_with_history_without_preload_content(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        with_history: Annotated[Optional[StrictBool], Field(description="if set to true [with history].")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Deletes the specified autoid.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param with_history: if set to true [with history].
        :type with_history: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_delete_with_history_serialize(
            autoid=autoid,
            with_history=with_history,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': None,
            '403': None,
            '404': None,
            '500': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_delete_with_history_serialize(
        self,
        autoid,
        with_history,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        if autoid is not None:
            _path_params['autoid'] = autoid
        # process the query parameters
        if with_history is not None:
            
            _query_params.append(('withHistory', with_history))
            
        # process the header parameters
        # process the form parameters
        # process the body parameter




        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='DELETE',
            resource_path='/v1/objectmischedules/{autoid}',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    def object_mi_schedules_get(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> CentixAPIDTOReadObjectMISchedule:
        """Gets the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_get_serialize(
            autoid=autoid,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_get_with_http_info(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[CentixAPIDTOReadObjectMISchedule]:
        """Gets the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_get_serialize(
            autoid=autoid,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_get_without_preload_content(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Gets the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_get_serialize(
            autoid=autoid,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_get_serialize(
        self,
        autoid,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        if autoid is not None:
            _path_params['autoid'] = autoid
        # process the query parameters
        # process the header parameters
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/v1/objectmischedules/{autoid}',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    def object_mi_schedules_inspection(
        self,
        autoid: Annotated[StrictInt, Field(description="AutoID of the Object MI Schedule")],
        document: Union[StrictBytes, StrictStr, Tuple[StrictStr, StrictBytes]],
        result: Annotated[Optional[StrictInt], Field(description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable")] = None,
        result_date: Optional[date] = None,
        result_note: Optional[StrictStr] = None,
        internal_note: Optional[StrictStr] = None,
        external_note: Optional[StrictStr] = None,
        inspection_relation_id: Optional[StrictInt] = None,
        inspection_person_id: Optional[StrictInt] = None,
        location_id: Optional[StrictInt] = None,
        after_condition_id: Optional[StrictInt] = None,
        before_condition_id: Optional[StrictInt] = None,
        override_open_inspection: Optional[StrictBool] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> CentixAPIDTOReadObjectMI:
        """Create new inspection with external certificate for Object MI Schedule

        <strong>Scopes:</strong><br><ul><li>objectmis:write</li></ul><br>

        :param autoid: AutoID of the Object MI Schedule (required)
        :type autoid: int
        :param document: (required)
        :type document: bytearray
        :param result: 0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable
        :type result: int
        :param result_date:
        :type result_date: date
        :param result_note:
        :type result_note: str
        :param internal_note:
        :type internal_note: str
        :param external_note:
        :type external_note: str
        :param inspection_relation_id:
        :type inspection_relation_id: int
        :param inspection_person_id:
        :type inspection_person_id: int
        :param location_id:
        :type location_id: int
        :param after_condition_id:
        :type after_condition_id: int
        :param before_condition_id:
        :type before_condition_id: int
        :param override_open_inspection:
        :type override_open_inspection: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_inspection_serialize(
            autoid=autoid,
            document=document,
            result=result,
            result_date=result_date,
            result_note=result_note,
            internal_note=internal_note,
            external_note=external_note,
            inspection_relation_id=inspection_relation_id,
            inspection_person_id=inspection_person_id,
            location_id=location_id,
            after_condition_id=after_condition_id,
            before_condition_id=before_condition_id,
            override_open_inspection=override_open_inspection,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMI",
            '400': "CentixAPIValidationResult",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_inspection_with_http_info(
        self,
        autoid: Annotated[StrictInt, Field(description="AutoID of the Object MI Schedule")],
        document: Union[StrictBytes, StrictStr, Tuple[StrictStr, StrictBytes]],
        result: Annotated[Optional[StrictInt], Field(description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable")] = None,
        result_date: Optional[date] = None,
        result_note: Optional[StrictStr] = None,
        internal_note: Optional[StrictStr] = None,
        external_note: Optional[StrictStr] = None,
        inspection_relation_id: Optional[StrictInt] = None,
        inspection_person_id: Optional[StrictInt] = None,
        location_id: Optional[StrictInt] = None,
        after_condition_id: Optional[StrictInt] = None,
        before_condition_id: Optional[StrictInt] = None,
        override_open_inspection: Optional[StrictBool] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[CentixAPIDTOReadObjectMI]:
        """Create new inspection with external certificate for Object MI Schedule

        <strong>Scopes:</strong><br><ul><li>objectmis:write</li></ul><br>

        :param autoid: AutoID of the Object MI Schedule (required)
        :type autoid: int
        :param document: (required)
        :type document: bytearray
        :param result: 0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable
        :type result: int
        :param result_date:
        :type result_date: date
        :param result_note:
        :type result_note: str
        :param internal_note:
        :type internal_note: str
        :param external_note:
        :type external_note: str
        :param inspection_relation_id:
        :type inspection_relation_id: int
        :param inspection_person_id:
        :type inspection_person_id: int
        :param location_id:
        :type location_id: int
        :param after_condition_id:
        :type after_condition_id: int
        :param before_condition_id:
        :type before_condition_id: int
        :param override_open_inspection:
        :type override_open_inspection: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_inspection_serialize(
            autoid=autoid,
            document=document,
            result=result,
            result_date=result_date,
            result_note=result_note,
            internal_note=internal_note,
            external_note=external_note,
            inspection_relation_id=inspection_relation_id,
            inspection_person_id=inspection_person_id,
            location_id=location_id,
            after_condition_id=after_condition_id,
            before_condition_id=before_condition_id,
            override_open_inspection=override_open_inspection,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMI",
            '400': "CentixAPIValidationResult",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_inspection_without_preload_content(
        self,
        autoid: Annotated[StrictInt, Field(description="AutoID of the Object MI Schedule")],
        document: Union[StrictBytes, StrictStr, Tuple[StrictStr, StrictBytes]],
        result: Annotated[Optional[StrictInt], Field(description="0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable")] = None,
        result_date: Optional[date] = None,
        result_note: Optional[StrictStr] = None,
        internal_note: Optional[StrictStr] = None,
        external_note: Optional[StrictStr] = None,
        inspection_relation_id: Optional[StrictInt] = None,
        inspection_person_id: Optional[StrictInt] = None,
        location_id: Optional[StrictInt] = None,
        after_condition_id: Optional[StrictInt] = None,
        before_condition_id: Optional[StrictInt] = None,
        override_open_inspection: Optional[StrictBool] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Create new inspection with external certificate for Object MI Schedule

        <strong>Scopes:</strong><br><ul><li>objectmis:write</li></ul><br>

        :param autoid: AutoID of the Object MI Schedule (required)
        :type autoid: int
        :param document: (required)
        :type document: bytearray
        :param result: 0 = NoAnswer, 1 = Ok, 2 = Failed, 3 = NotApplicable
        :type result: int
        :param result_date:
        :type result_date: date
        :param result_note:
        :type result_note: str
        :param internal_note:
        :type internal_note: str
        :param external_note:
        :type external_note: str
        :param inspection_relation_id:
        :type inspection_relation_id: int
        :param inspection_person_id:
        :type inspection_person_id: int
        :param location_id:
        :type location_id: int
        :param after_condition_id:
        :type after_condition_id: int
        :param before_condition_id:
        :type before_condition_id: int
        :param override_open_inspection:
        :type override_open_inspection: bool
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_inspection_serialize(
            autoid=autoid,
            document=document,
            result=result,
            result_date=result_date,
            result_note=result_note,
            internal_note=internal_note,
            external_note=external_note,
            inspection_relation_id=inspection_relation_id,
            inspection_person_id=inspection_person_id,
            location_id=location_id,
            after_condition_id=after_condition_id,
            before_condition_id=before_condition_id,
            override_open_inspection=override_open_inspection,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '201': "CentixAPIDTOReadObjectMI",
            '400': "CentixAPIValidationResult",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_inspection_serialize(
        self,
        autoid,
        document,
        result,
        result_date,
        result_note,
        internal_note,
        external_note,
        inspection_relation_id,
        inspection_person_id,
        location_id,
        after_condition_id,
        before_condition_id,
        override_open_inspection,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        if autoid is not None:
            _path_params['autoid'] = autoid
        # process the query parameters
        # process the header parameters
        # process the form parameters
        if document is not None:
            _files['Document'] = document
        if result is not None:
            _form_params.append(('Result', result))
        if result_date is not None:
            _form_params.append(('ResultDate', result_date))
        if result_note is not None:
            _form_params.append(('ResultNote', result_note))
        if internal_note is not None:
            _form_params.append(('InternalNote', internal_note))
        if external_note is not None:
            _form_params.append(('ExternalNote', external_note))
        if inspection_relation_id is not None:
            _form_params.append(('InspectionRelationID', inspection_relation_id))
        if inspection_person_id is not None:
            _form_params.append(('InspectionPersonID', inspection_person_id))
        if location_id is not None:
            _form_params.append(('LocationID', location_id))
        if after_condition_id is not None:
            _form_params.append(('AfterConditionID', after_condition_id))
        if before_condition_id is not None:
            _form_params.append(('BeforeConditionID', before_condition_id))
        if override_open_inspection is not None:
            _form_params.append(('OverrideOpenInspection', override_open_inspection))
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'multipart/form-data'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/v1/objectmischedules/{autoid}/inspection',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    def object_mi_schedules_o_data_list(
        self,
        filter: Annotated[Optional[StrictStr], Field(description="Filter the results using OData syntax.")] = None,
        orderby: Annotated[Optional[StrictStr], Field(description="Order the results using OData syntax.")] = None,
        skip: Annotated[Optional[StrictInt], Field(description="The number of results to skip.")] = None,
        top: Annotated[Optional[StrictInt], Field(description="The number of results to return.")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule:
        """Get a list of object mi schedules

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param filter: Filter the results using OData syntax.
        :type filter: str
        :param orderby: Order the results using OData syntax.
        :type orderby: str
        :param skip: The number of results to skip.
        :type skip: int
        :param top: The number of results to return.
        :type top: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_o_data_list_serialize(
            filter=filter,
            orderby=orderby,
            skip=skip,
            top=top,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationError",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_o_data_list_with_http_info(
        self,
        filter: Annotated[Optional[StrictStr], Field(description="Filter the results using OData syntax.")] = None,
        orderby: Annotated[Optional[StrictStr], Field(description="Order the results using OData syntax.")] = None,
        skip: Annotated[Optional[StrictInt], Field(description="The number of results to skip.")] = None,
        top: Annotated[Optional[StrictInt], Field(description="The number of results to return.")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule]:
        """Get a list of object mi schedules

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param filter: Filter the results using OData syntax.
        :type filter: str
        :param orderby: Order the results using OData syntax.
        :type orderby: str
        :param skip: The number of results to skip.
        :type skip: int
        :param top: The number of results to return.
        :type top: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_o_data_list_serialize(
            filter=filter,
            orderby=orderby,
            skip=skip,
            top=top,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationError",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_o_data_list_without_preload_content(
        self,
        filter: Annotated[Optional[StrictStr], Field(description="Filter the results using OData syntax.")] = None,
        orderby: Annotated[Optional[StrictStr], Field(description="Order the results using OData syntax.")] = None,
        skip: Annotated[Optional[StrictInt], Field(description="The number of results to skip.")] = None,
        top: Annotated[Optional[StrictInt], Field(description="The number of results to return.")] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Get a list of object mi schedules

        <strong>Scopes:</strong><br><ul><li>objectmischedules:read</li></ul><br>

        :param filter: Filter the results using OData syntax.
        :type filter: str
        :param orderby: Order the results using OData syntax.
        :type orderby: str
        :param skip: The number of results to skip.
        :type skip: int
        :param top: The number of results to return.
        :type top: int
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_o_data_list_serialize(
            filter=filter,
            orderby=orderby,
            skip=skip,
            top=top,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule",
            '400': "CentixAPIValidationError",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_o_data_list_serialize(
        self,
        filter,
        orderby,
        skip,
        top,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        if filter is not None:
            
            _query_params.append(('$filter', filter))
            
        if orderby is not None:
            
            _query_params.append(('$orderby', orderby))
            
        if skip is not None:
            
            _query_params.append(('$skip', skip))
            
        if top is not None:
            
            _query_params.append(('$top', top))
            
        # process the header parameters
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/v1/objectmischedules',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    def object_mi_schedules_patch(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        update_properties: Annotated[Dict[str, Dict[str, Any]], Field(description="The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive]")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> CentixAPIDTOReadObjectMISchedule:
        """Updates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param update_properties: The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive] (required)
        :type update_properties: Dict[str, object]
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_patch_serialize(
            autoid=autoid,
            update_properties=update_properties,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def object_mi_schedules_patch_with_http_info(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        update_properties: Annotated[Dict[str, Dict[str, Any]], Field(description="The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive]")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[CentixAPIDTOReadObjectMISchedule]:
        """Updates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param update_properties: The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive] (required)
        :type update_properties: Dict[str, object]
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_patch_serialize(
            autoid=autoid,
            update_properties=update_properties,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def object_mi_schedules_patch_without_preload_content(
        self,
        autoid: Annotated[StrictInt, Field(description="The autoid.")],
        update_properties: Annotated[Dict[str, Dict[str, Any]], Field(description="The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive]")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Updates the specified item.

        <strong>Scopes:</strong><br><ul><li>objectmischedules:write</li></ul><br>

        :param autoid: The autoid. (required)
        :type autoid: int
        :param update_properties: The update properties. Available properties to modify:               [TimeScheduleID, InspectionRelationID, InspectionPersonID, StartAt, EndAt, MIValidUntilDate, NextRun, InActive] (required)
        :type update_properties: Dict[str, object]
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._object_mi_schedules_patch_serialize(
            autoid=autoid,
            update_properties=update_properties,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "CentixAPIDTOReadObjectMISchedule",
            '403': None,
            '404': None,
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _object_mi_schedules_patch_serialize(
        self,
        autoid,
        update_properties,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        if autoid is not None:
            _path_params['autoid'] = autoid
        # process the query parameters
        # process the header parameters
        # process the form parameters
        # process the body parameter
        if update_properties is not None:
            _body_params = update_properties


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
            'oauth2'
        ]

        return self.api_client.param_serialize(
            method='PATCH',
            resource_path='/v1/objectmischedules/{autoid}',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


