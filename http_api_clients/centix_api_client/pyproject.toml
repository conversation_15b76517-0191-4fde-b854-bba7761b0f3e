[tool.poetry]
name = "centix_api_client"
version = "1.0.0"
description = "Centix API 1.0"
authors = ["OpenAPI Generator Community <<EMAIL>>"]
license = "NoLicense"
readme = "README.md"
repository = "https://github.com/GIT_USER_ID/GIT_REPO_ID"
keywords = ["OpenAPI", "OpenAPI-Generator", "Centix API 1.0"]
include = ["centix_api_client/py.typed"]

[tool.poetry.dependencies]
python = "^3.9"

urllib3 = ">= 1.0.0, < 3.0.0"
python-dateutil = ">= 2.8.2"
pydantic = ">= 2"
typing-extensions = ">= 4.7.1"

[tool.poetry.dev-dependencies]
pytest = ">= 7.2.1"
pytest-cov = ">= 2.8.1"
tox = ">= 3.9.0"
flake8 = ">= 4.0.0"
types-python-dateutil = ">= *********"
mypy = ">= 1.5"


[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.pylint.'MESSAGES CONTROL']
extension-pkg-whitelist = "pydantic"

[tool.mypy]
files = [
  "centix_api_client",
  #"test",  # auto-generated tests
  "tests", # hand-written tests
]
# TODO: enable "strict" once all these individual checks are passing
# strict = true

# List from: https://mypy.readthedocs.io/en/stable/existing_code.html#introduce-stricter-options
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true

## Getting these passing should be easy
strict_equality = true
extra_checks = true

## Strongly recommend enabling this one as soon as you can
check_untyped_defs = true

## These shouldn't be too much additional work, but may be tricky to
## get passing if you use a lot of untyped libraries
disallow_subclassing_any = true
disallow_untyped_decorators = true
disallow_any_generics = true

### These next few are various gradations of forcing use of type annotations
#disallow_untyped_calls = true
#disallow_incomplete_defs = true
#disallow_untyped_defs = true
#
### This one isn't too hard to get passing, but return on investment is lower
#no_implicit_reexport = true
#
### This one can be tricky to get passing if you use a lot of untyped libraries
#warn_return_any = true

[[tool.mypy.overrides]]
module = [
  "centix_api_client.configuration",
]
warn_unused_ignores = true
strict_equality = true
extra_checks = true
check_untyped_defs = true
disallow_subclassing_any = true
disallow_untyped_decorators = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_reexport = true
warn_return_any = true
