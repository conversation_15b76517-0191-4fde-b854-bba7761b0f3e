# centix-api-client
No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: v1
- Package version: 1.0.0
- Generator version: 7.13.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.9+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import centix_api_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import centix_api_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import centix_api_client
from centix_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://holmatro-dev.centix.com/api
# See configuration.py for a list of all supported configuration parameters.
configuration = centix_api_client.Configuration(
    host = "https://holmatro-dev.centix.com/api"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

configuration.access_token = os.environ["ACCESS_TOKEN"]


# Enter a context with an instance of the API client
with centix_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = centix_api_client.AbsenceKindApi(api_client)
    autoid = 56 # int | The autoid.

    try:
        # Gets the specified item.
        api_response = api_instance.absence_kind_get(autoid)
        print("The response of AbsenceKindApi->absence_kind_get:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling AbsenceKindApi->absence_kind_get: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://holmatro-dev.centix.com/api*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AbsenceKindApi* | [**absence_kind_get**](docs/AbsenceKindApi.md#absence_kind_get) | **GET** /v1/absencekinds/{autoid} | Gets the specified item.
*AbsenceKindApi* | [**absence_kind_o_data_list**](docs/AbsenceKindApi.md#absence_kind_o_data_list) | **GET** /v1/absencekinds | Get a list of absencekinds
*AbsenceRegistrationApi* | [**absence_registration_create**](docs/AbsenceRegistrationApi.md#absence_registration_create) | **POST** /v1/absenceregistrations | Creates the specified absence registration.
*AbsenceRegistrationApi* | [**absence_registration_delete**](docs/AbsenceRegistrationApi.md#absence_registration_delete) | **DELETE** /v1/absenceregistrations/{autoid} | Deletes the specified absence registration.
*AbsenceRegistrationApi* | [**absence_registration_get**](docs/AbsenceRegistrationApi.md#absence_registration_get) | **GET** /v1/absenceregistrations/{autoid} | Gets the specified absence registration.
*AbsenceRegistrationApi* | [**absence_registration_o_data_list**](docs/AbsenceRegistrationApi.md#absence_registration_o_data_list) | **GET** /v1/absenceregistrations | Get a list of absence registration
*AbsenceRegistrationApi* | [**absence_registration_update**](docs/AbsenceRegistrationApi.md#absence_registration_update) | **PUT** /v1/absenceregistrations/{autoid} | Updates the specified absence registration.
*AdministrationsApi* | [**administrations_get**](docs/AdministrationsApi.md#administrations_get) | **GET** /v1/administrations/{autoid} | Gets the specified item.
*AdministrationsApi* | [**administrations_get_list**](docs/AdministrationsApi.md#administrations_get_list) | **GET** /v1/administrations | Gets the list.
*BlockReasonsApi* | [**block_reasons_get**](docs/BlockReasonsApi.md#block_reasons_get) | **GET** /v1/blockreasons/{autoid} | Gets the specified item.
*BlockReasonsApi* | [**block_reasons_get_list**](docs/BlockReasonsApi.md#block_reasons_get_list) | **GET** /v1/blockreasons | Gets the list.
*BrandTypeDefaultMISchedulesApi* | [**brand_type_default_mi_schedules_create**](docs/BrandTypeDefaultMISchedulesApi.md#brand_type_default_mi_schedules_create) | **POST** /v1/brandtypedefaultmischedules | Creates the specified item.
*BrandTypeDefaultMISchedulesApi* | [**brand_type_default_mi_schedules_delete**](docs/BrandTypeDefaultMISchedulesApi.md#brand_type_default_mi_schedules_delete) | **DELETE** /v1/brandtypedefaultmischedules/{autoid} | Deletes the specified item.
*BrandTypeDefaultMISchedulesApi* | [**brand_type_default_mi_schedules_get**](docs/BrandTypeDefaultMISchedulesApi.md#brand_type_default_mi_schedules_get) | **GET** /v1/brandtypedefaultmischedules/{autoid} | Gets the specified item.
*BrandTypeDefaultMISchedulesApi* | [**brand_type_default_mi_schedules_o_data_list**](docs/BrandTypeDefaultMISchedulesApi.md#brand_type_default_mi_schedules_o_data_list) | **GET** /v1/brandtypedefaultmischedules | Get a list of brandtypedefaultmischedules
*BrandTypeDefaultMISchedulesApi* | [**brand_type_default_mi_schedules_update**](docs/BrandTypeDefaultMISchedulesApi.md#brand_type_default_mi_schedules_update) | **PUT** /v1/brandtypedefaultmischedules/{autoid} | Updates the specified item.
*BrandTypeRevisionsApi* | [**brand_type_revisions_create**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_create) | **POST** /v1/brandtyperevisions | Creates the specified item.
*BrandTypeRevisionsApi* | [**brand_type_revisions_delete**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_delete) | **DELETE** /v1/brandtyperevisions/{autoid} | Deletes the specified item.
*BrandTypeRevisionsApi* | [**brand_type_revisions_get**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_get) | **GET** /v1/brandtyperevisions/{autoid} | Gets the specified item.
*BrandTypeRevisionsApi* | [**brand_type_revisions_get_list**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_get_list) | **GET** /v1/brandtyperevisions | Gets the list.
*BrandTypeRevisionsApi* | [**brand_type_revisions_update**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_update) | **PUT** /v1/brandtyperevisions/{autoid} | Updates the specified item.
*BrandTypesApi* | [**brand_types_create**](docs/BrandTypesApi.md#brand_types_create) | **POST** /v1/brandtypes | Creates the specified item.
*BrandTypesApi* | [**brand_types_default_mi_schedules**](docs/BrandTypesApi.md#brand_types_default_mi_schedules) | **GET** /v1/brandtypes/{brandtypeid}/defaultmischedules | Get a list of default mi schedules for the specified item.
*BrandTypesApi* | [**brand_types_delete**](docs/BrandTypesApi.md#brand_types_delete) | **DELETE** /v1/brandtypes/{autoid} | Deletes the specified item.
*BrandTypesApi* | [**brand_types_get**](docs/BrandTypesApi.md#brand_types_get) | **GET** /v1/brandtypes/{autoid} | Gets the specified item.
*BrandTypesApi* | [**brand_types_get_list**](docs/BrandTypesApi.md#brand_types_get_list) | **GET** /v1/brandtypes | Gets the list.
*BrandTypesApi* | [**brand_types_update**](docs/BrandTypesApi.md#brand_types_update) | **PUT** /v1/brandtypes/{autoid} | Updates the specified item.
*BrandsApi* | [**brands_create**](docs/BrandsApi.md#brands_create) | **POST** /v1/brands | Creates the specified item.
*BrandsApi* | [**brands_delete**](docs/BrandsApi.md#brands_delete) | **DELETE** /v1/brands/{autoid} | Deletes the specified item.
*BrandsApi* | [**brands_get**](docs/BrandsApi.md#brands_get) | **GET** /v1/brands/{autoid} | Gets the specified item.
*BrandsApi* | [**brands_get_list**](docs/BrandsApi.md#brands_get_list) | **GET** /v1/brands | Gets the list.
*BrandsApi* | [**brands_o_data_list**](docs/BrandsApi.md#brands_o_data_list) | **GET** /v1/brands/searchbrandtyperevisions | The Brand, Type and Revision models are flattened into a single model
*BrandsApi* | [**brands_update**](docs/BrandsApi.md#brands_update) | **PUT** /v1/brands/{autoid} | Updates the specified item.
*BudgetCodesApi* | [**budget_codes_create**](docs/BudgetCodesApi.md#budget_codes_create) | **POST** /v1/budgetcodes | Creates the specified item.
*BudgetCodesApi* | [**budget_codes_delete**](docs/BudgetCodesApi.md#budget_codes_delete) | **DELETE** /v1/budgetcodes/{autoid} | Deletes the specified item.
*BudgetCodesApi* | [**budget_codes_get**](docs/BudgetCodesApi.md#budget_codes_get) | **GET** /v1/budgetcodes/{autoid} | Gets the specified item.
*BudgetCodesApi* | [**budget_codes_get_list**](docs/BudgetCodesApi.md#budget_codes_get_list) | **GET** /v1/budgetcodes | Gets the list.
*BudgetCodesApi* | [**budget_codes_update**](docs/BudgetCodesApi.md#budget_codes_update) | **PUT** /v1/budgetcodes/{autoid} | Updates the specified item.
*ConditionsApi* | [**conditions_get**](docs/ConditionsApi.md#conditions_get) | **GET** /v1/conditions/{autoid} | Gets the specified item.
*ConditionsApi* | [**conditions_get_list**](docs/ConditionsApi.md#conditions_get_list) | **GET** /v1/conditions | Gets the list.
*CostCategoriesApi* | [**cost_categories_create**](docs/CostCategoriesApi.md#cost_categories_create) | **POST** /v1/costcategories | Creates the specified item.
*CostCategoriesApi* | [**cost_categories_delete**](docs/CostCategoriesApi.md#cost_categories_delete) | **DELETE** /v1/costcategories/{autoid} | Deletes the specified item.
*CostCategoriesApi* | [**cost_categories_get**](docs/CostCategoriesApi.md#cost_categories_get) | **GET** /v1/costcategories/{autoid} | Gets the specified item.
*CostCategoriesApi* | [**cost_categories_get_list**](docs/CostCategoriesApi.md#cost_categories_get_list) | **GET** /v1/costcategories | Gets the list.
*CostCategoriesApi* | [**cost_categories_update**](docs/CostCategoriesApi.md#cost_categories_update) | **PUT** /v1/costcategories/{autoid} | Updates the specified item.
*CostCodesApi* | [**cost_codes_create**](docs/CostCodesApi.md#cost_codes_create) | **POST** /v1/costcodes | Creates the specified item.
*CostCodesApi* | [**cost_codes_delete**](docs/CostCodesApi.md#cost_codes_delete) | **DELETE** /v1/costcodes/{autoid} | Deletes the specified item.
*CostCodesApi* | [**cost_codes_get**](docs/CostCodesApi.md#cost_codes_get) | **GET** /v1/costcodes/{autoid} | Gets the specified item.
*CostCodesApi* | [**cost_codes_get_list**](docs/CostCodesApi.md#cost_codes_get_list) | **GET** /v1/costcodes | Gets the list.
*CostCodesApi* | [**cost_codes_update**](docs/CostCodesApi.md#cost_codes_update) | **PUT** /v1/costcodes/{autoid} | Updates the specified item.
*CountriesApi* | [**countries_get**](docs/CountriesApi.md#countries_get) | **GET** /v1/countries/{autoid} | Gets the specified item.
*CountriesApi* | [**countries_get_list**](docs/CountriesApi.md#countries_get_list) | **GET** /v1/countries | Gets the list.
*CountryStateCountiesApi* | [**country_state_counties_get**](docs/CountryStateCountiesApi.md#country_state_counties_get) | **GET** /v1/countrystatecounties/{autoid} | Gets the specified item.
*CountryStateCountiesApi* | [**country_state_counties_get_list**](docs/CountryStateCountiesApi.md#country_state_counties_get_list) | **GET** /v1/countrystatecounties | Gets the list.
*CountryStatesApi* | [**country_states_get**](docs/CountryStatesApi.md#country_states_get) | **GET** /v1/countrystates/{autoid} | Gets the specified item.
*CountryStatesApi* | [**country_states_get_list**](docs/CountryStatesApi.md#country_states_get_list) | **GET** /v1/countrystates | Gets the list.
*CurrenciesApi* | [**currencies_get**](docs/CurrenciesApi.md#currencies_get) | **GET** /v1/currencies/{autoid} | Gets the specified item.
*CurrenciesApi* | [**currencies_get_list**](docs/CurrenciesApi.md#currencies_get_list) | **GET** /v1/currencies | Gets the list.
*DeviceApi* | [**device_get**](docs/DeviceApi.md#device_get) | **GET** /v1/Devices/{autoid} | Gets the specified device.
*DeviceApi* | [**device_get_device_location_events_o_data_list**](docs/DeviceApi.md#device_get_device_location_events_o_data_list) | **GET** /v1/Devices/DeviceLocationEvents | Get a list of device location events.
*DeviceApi* | [**device_get_latest_location_event**](docs/DeviceApi.md#device_get_latest_location_event) | **GET** /v1/Devices/{autoid}/LatestLocationEvent | Get the latest location event for autoid.
*DeviceApi* | [**device_o_data_list**](docs/DeviceApi.md#device_o_data_list) | **GET** /v1/Devices | Get a list of devices
*DocumentKindApi* | [**document_kind_get**](docs/DocumentKindApi.md#document_kind_get) | **GET** /v1/documentkinds/{autoid} | Gets the specified item.
*DocumentKindApi* | [**document_kind_get_list**](docs/DocumentKindApi.md#document_kind_get_list) | **GET** /v1/documentkinds | Gets the list.
*DocumentsApi* | [**documents_create**](docs/DocumentsApi.md#documents_create) | **POST** /v1/documents/from-url | Creates the specified document from URL.
*DocumentsApi* | [**documents_create_from_file**](docs/DocumentsApi.md#documents_create_from_file) | **POST** /v1/documents/from-file | Creates the specified document from file.
*DocumentsApi* | [**documents_delete**](docs/DocumentsApi.md#documents_delete) | **DELETE** /v1/documents/{autoid} | Deletes the specified document.
*DocumentsApi* | [**documents_get**](docs/DocumentsApi.md#documents_get) | **GET** /v1/documents/{autoid} | Gets the specified document.
*DocumentsApi* | [**documents_o_data_list**](docs/DocumentsApi.md#documents_o_data_list) | **GET** /v1/documents | Get a list of documents
*DocumentsApi* | [**documents_update**](docs/DocumentsApi.md#documents_update) | **PUT** /v1/documents/{autoid} | 
*EInvoiceLinesApi* | [**e_invoice_lines_o_data_list**](docs/EInvoiceLinesApi.md#e_invoice_lines_o_data_list) | **GET** /v1/einvoicelines | Get a list of e-invoice lines
*EInvoicesApi* | [**e_invoices_get**](docs/EInvoicesApi.md#e_invoices_get) | **GET** /v1/einvoices/{autoid} | Gets the specified item.
*EInvoicesApi* | [**e_invoices_get_e_invoice_lines**](docs/EInvoicesApi.md#e_invoices_get_e_invoice_lines) | **GET** /v1/einvoices/{autoid}/einvoicelines | Get a list of e-invoice lines for the specified e-invoice.
*EInvoicesApi* | [**e_invoices_o_data_list**](docs/EInvoicesApi.md#e_invoices_o_data_list) | **GET** /v1/einvoices | Get a list of e-invoices
*FixedAssetGroupsApi* | [**fixed_asset_groups_get**](docs/FixedAssetGroupsApi.md#fixed_asset_groups_get) | **GET** /v1/fixedassetgroups/{autoid} | Gets the specified item.
*FixedAssetGroupsApi* | [**fixed_asset_groups_get_list**](docs/FixedAssetGroupsApi.md#fixed_asset_groups_get_list) | **GET** /v1/fixedassetgroups | Gets the list.
*GeneralLedgersApi* | [**general_ledgers_get**](docs/GeneralLedgersApi.md#general_ledgers_get) | **GET** /v1/generalledgers/{autoid} | Gets the specified item.
*GeneralLedgersApi* | [**general_ledgers_get_list**](docs/GeneralLedgersApi.md#general_ledgers_get_list) | **GET** /v1/generalledgers | Gets the list.
*HSCodesApi* | [**h_s_codes_get**](docs/HSCodesApi.md#h_s_codes_get) | **GET** /v1/hscodes/{autoid} | Gets the specified item.
*HSCodesApi* | [**h_s_codes_get_list**](docs/HSCodesApi.md#h_s_codes_get_list) | **GET** /v1/hscodes | Gets the list.
*InvoiceLinesApi* | [**invoice_lines_o_data_list**](docs/InvoiceLinesApi.md#invoice_lines_o_data_list) | **GET** /v1/invoicelines | Get a list of invoice lines
*InvoicesApi* | [**invoices_get**](docs/InvoicesApi.md#invoices_get) | **GET** /v1/invoices/{autoid} | Gets the specified item.
*InvoicesApi* | [**invoices_get_invoice_lines**](docs/InvoicesApi.md#invoices_get_invoice_lines) | **GET** /v1/invoices/{autoid}/invoicelines | Get a list of invoice lines for the specified invoice.
*InvoicesApi* | [**invoices_o_data_list**](docs/InvoicesApi.md#invoices_o_data_list) | **GET** /v1/invoices | Get a list of invoices
*JournalsApi* | [**journals_get**](docs/JournalsApi.md#journals_get) | **GET** /v1/journals/{autoid} | Gets the specified item.
*JournalsApi* | [**journals_o_data_list**](docs/JournalsApi.md#journals_o_data_list) | **GET** /v1/journals | Get a list of journals
*JournalsApi* | [**journals_process**](docs/JournalsApi.md#journals_process) | **POST** /v1/journals/{autoid}/process | Processes the specified journal.
*LanguagesApi* | [**languages_get**](docs/LanguagesApi.md#languages_get) | **GET** /v1/languages/{autoid} | Gets the specified item.
*LanguagesApi* | [**languages_get_list**](docs/LanguagesApi.md#languages_get_list) | **GET** /v1/languages | Gets the list.
*LocationGroupsApi* | [**location_groups_get**](docs/LocationGroupsApi.md#location_groups_get) | **GET** /v1/locationgroups/{autoid} | Gets the specified item.
*LocationGroupsApi* | [**location_groups_get_list**](docs/LocationGroupsApi.md#location_groups_get_list) | **GET** /v1/locationgroups | Gets the list.
*LocationStatusApi* | [**location_status_get**](docs/LocationStatusApi.md#location_status_get) | **GET** /v1/locationstatuses/{autoid} | Gets the specified item.
*LocationStatusApi* | [**location_status_get_list**](docs/LocationStatusApi.md#location_status_get_list) | **GET** /v1/locationstatuses | Gets the list.
*LocationTypesApi* | [**location_types_get**](docs/LocationTypesApi.md#location_types_get) | **GET** /v1/locationtypes/{autoid} | Gets the specified item.
*LocationTypesApi* | [**location_types_get_list**](docs/LocationTypesApi.md#location_types_get_list) | **GET** /v1/locationtypes | Gets the list.
*LocationUsersApi* | [**location_users_create**](docs/LocationUsersApi.md#location_users_create) | **POST** /v1/locationusers | Creates the specified item.
*LocationUsersApi* | [**location_users_delete**](docs/LocationUsersApi.md#location_users_delete) | **DELETE** /v1/locationusers/{autoid} | Deletes the specified item.
*LocationUsersApi* | [**location_users_get**](docs/LocationUsersApi.md#location_users_get) | **GET** /v1/locationusers/{autoid} | Gets the specified item.
*LocationUsersApi* | [**location_users_o_data_list**](docs/LocationUsersApi.md#location_users_o_data_list) | **GET** /v1/locationusers | 
*LocationsApi* | [**locations_create**](docs/LocationsApi.md#locations_create) | **POST** /v1/locations | Creates the specified item.
*LocationsApi* | [**locations_delete**](docs/LocationsApi.md#locations_delete) | **DELETE** /v1/locations/{autoid} | Deletes the specified item.
*LocationsApi* | [**locations_get**](docs/LocationsApi.md#locations_get) | **GET** /v1/locations/{autoid} | Gets the specified item.
*LocationsApi* | [**locations_get_image**](docs/LocationsApi.md#locations_get_image) | **GET** /v1/locations/{autoid}/image | Gets the image.
*LocationsApi* | [**locations_o_data_list**](docs/LocationsApi.md#locations_o_data_list) | **GET** /v1/locations | Get a list of locations
*LocationsApi* | [**locations_remove_images**](docs/LocationsApi.md#locations_remove_images) | **DELETE** /v1/locations/{autoid}/image | Removes the image.
*LocationsApi* | [**locations_set_images**](docs/LocationsApi.md#locations_set_images) | **PUT** /v1/locations/{autoid}/image | Updates the image.
*LocationsApi* | [**locations_update**](docs/LocationsApi.md#locations_update) | **PUT** /v1/locations/{autoid} | Updates the specified item.
*MIApi* | [**m_i_get**](docs/MIApi.md#m_i_get) | **GET** /v1/mi/{autoid} | Gets the specified item.
*MIApi* | [**m_i_objects**](docs/MIApi.md#m_i_objects) | **GET** /v1/mi/{miid}/objects | Get a list of object mis for the specified item.
*MIApi* | [**m_io_data_list**](docs/MIApi.md#m_io_data_list) | **GET** /v1/mi | Get a list of mis
*MIPlansApi* | [**m_i_plans_get**](docs/MIPlansApi.md#m_i_plans_get) | **GET** /v1/miplans/{autoid} | Gets the specified item.
*MIPlansApi* | [**m_i_plans_get_list**](docs/MIPlansApi.md#m_i_plans_get_list) | **GET** /v1/miplans | Gets the list.
*MeasuringDevice1MeasurementsApi* | [**measuring_device1_measurements_functions**](docs/MeasuringDevice1MeasurementsApi.md#measuring_device1_measurements_functions) | **GET** /v1/measuringdevice1measurements/{autoid}/functions | Get a list of functions for the specified measuringdevice1measurement.
*MeasuringDevice1MeasurementsApi* | [**measuring_device1_measurements_get**](docs/MeasuringDevice1MeasurementsApi.md#measuring_device1_measurements_get) | **GET** /v1/measuringdevice1measurements/{autoid} | Gets the specified item.
*MeasuringDevice1MeasurementsApi* | [**measuring_device1_measurements_get_list**](docs/MeasuringDevice1MeasurementsApi.md#measuring_device1_measurements_get_list) | **GET** /v1/measuringdevice1measurements | Gets the list.
*NoRelationSignatureReasonApi* | [**no_relation_signature_reason_get**](docs/NoRelationSignatureReasonApi.md#no_relation_signature_reason_get) | **GET** /v1/norelationsignaturereasons/{autoid} | Gets the specified item.
*NoRelationSignatureReasonApi* | [**no_relation_signature_reason_get_list**](docs/NoRelationSignatureReasonApi.md#no_relation_signature_reason_get_list) | **GET** /v1/norelationsignaturereasons | Gets the list.
*ObjectCodesApi* | [**object_codes_create**](docs/ObjectCodesApi.md#object_codes_create) | **POST** /v1/objectcodes | Creates the specified object code.
*ObjectCodesApi* | [**object_codes_delete**](docs/ObjectCodesApi.md#object_codes_delete) | **DELETE** /v1/objectcodes/{id} | Deletes the specified object code.
*ObjectCodesApi* | [**object_codes_get**](docs/ObjectCodesApi.md#object_codes_get) | **GET** /v1/objectcodes/{id} | Gets the specified object code.
*ObjectCodesApi* | [**object_codes_o_data_list**](docs/ObjectCodesApi.md#object_codes_o_data_list) | **GET** /v1/objectcodes | Gets the object codes.
*ObjectCodesApi* | [**object_codes_update**](docs/ObjectCodesApi.md#object_codes_update) | **PUT** /v1/objectcodes/{id} | Updates the specified object code.
*ObjectGroupsApi* | [**object_groups_get**](docs/ObjectGroupsApi.md#object_groups_get) | **GET** /v1/objectgroups/{autoid} | Gets the specified item.
*ObjectGroupsApi* | [**object_groups_get_list**](docs/ObjectGroupsApi.md#object_groups_get_list) | **GET** /v1/objectgroups | Gets the list.
*ObjectLocationLockReasonsApi* | [**object_location_lock_reasons_get**](docs/ObjectLocationLockReasonsApi.md#object_location_lock_reasons_get) | **GET** /v1/objectlocationlockreasons/{autoid} | Gets the specified item.
*ObjectLocationLockReasonsApi* | [**object_location_lock_reasons_get_list**](docs/ObjectLocationLockReasonsApi.md#object_location_lock_reasons_get_list) | **GET** /v1/objectlocationlockreasons | Gets the list.
*ObjectLocationLogsApi* | [**object_location_logs_get**](docs/ObjectLocationLogsApi.md#object_location_logs_get) | **GET** /v1/objectlocationlogs/{autoid} | Gets the specified item.
*ObjectLocationLogsApi* | [**object_location_logs_o_data_list**](docs/ObjectLocationLogsApi.md#object_location_logs_o_data_list) | **GET** /v1/objectlocationlogs | Get a list of objectlocationlogs
*ObjectLocationsApi* | [**object_locations_o_data_list**](docs/ObjectLocationsApi.md#object_locations_o_data_list) | **GET** /v1/objectlocations | Get a list of objectlocations
*ObjectMISchedulesApi* | [**object_mi_schedules_create**](docs/ObjectMISchedulesApi.md#object_mi_schedules_create) | **POST** /v1/objectmischedules | Creates the specified item.
*ObjectMISchedulesApi* | [**object_mi_schedules_delete_with_history**](docs/ObjectMISchedulesApi.md#object_mi_schedules_delete_with_history) | **DELETE** /v1/objectmischedules/{autoid} | Deletes the specified autoid.
*ObjectMISchedulesApi* | [**object_mi_schedules_get**](docs/ObjectMISchedulesApi.md#object_mi_schedules_get) | **GET** /v1/objectmischedules/{autoid} | Gets the specified item.
*ObjectMISchedulesApi* | [**object_mi_schedules_inspection**](docs/ObjectMISchedulesApi.md#object_mi_schedules_inspection) | **POST** /v1/objectmischedules/{autoid}/inspection | Create new inspection with external certificate for Object MI Schedule
*ObjectMISchedulesApi* | [**object_mi_schedules_o_data_list**](docs/ObjectMISchedulesApi.md#object_mi_schedules_o_data_list) | **GET** /v1/objectmischedules | Get a list of object mi schedules
*ObjectMISchedulesApi* | [**object_mi_schedules_patch**](docs/ObjectMISchedulesApi.md#object_mi_schedules_patch) | **PATCH** /v1/objectmischedules/{autoid} | Updates the specified item.
*ObjectMIsApi* | [**object_mis_get**](docs/ObjectMIsApi.md#object_mis_get) | **GET** /v1/objectmis/{autoid} | Gets the specified item.
*ObjectMIsApi* | [**object_mis_get_certificate**](docs/ObjectMIsApi.md#object_mis_get_certificate) | **GET** /v1/objectmis/{autoid}/certificate | Gets the certificate.
*ObjectMIsApi* | [**object_mis_o_data_list**](docs/ObjectMIsApi.md#object_mis_o_data_list) | **GET** /v1/objectmis | Get a list of object mis
*ObjectMeterScheduleViewsApi* | [**object_meter_schedule_views_o_data_list**](docs/ObjectMeterScheduleViewsApi.md#object_meter_schedule_views_o_data_list) | **GET** /v1/objectmeterscheduleviews | Get a list of object meter schedule views (schedule and last measurement and function value combined)
*ObjectMeterSchedulesApi* | [**object_meter_schedules_create_object_measurement**](docs/ObjectMeterSchedulesApi.md#object_meter_schedules_create_object_measurement) | **POST** /v1/objectmeterschedules/objectmeasurements | Creates a new object measurement
*ObjectMeterSchedulesApi* | [**object_meter_schedules_delete**](docs/ObjectMeterSchedulesApi.md#object_meter_schedules_delete) | **DELETE** /v1/objectmeterschedules/objectmeasurements/{autoid} | Deletes the specified object measurement.
*ObjectMeterSchedulesApi* | [**object_meter_schedules_get**](docs/ObjectMeterSchedulesApi.md#object_meter_schedules_get) | **GET** /v1/objectmeterschedules/{autoid} | Gets the specified item.
*ObjectMeterSchedulesApi* | [**object_meter_schedules_measurement_o_data_list**](docs/ObjectMeterSchedulesApi.md#object_meter_schedules_measurement_o_data_list) | **GET** /v1/objectmeterschedules/objectmeasurements | Get a list of object meter schedule object measurements and functions
*ObjectMeterSchedulesApi* | [**object_meter_schedules_o_data_list**](docs/ObjectMeterSchedulesApi.md#object_meter_schedules_o_data_list) | **GET** /v1/objectmeterschedules | Get a list of object meter schedules
*ObjectStatusesApi* | [**object_statuses_get**](docs/ObjectStatusesApi.md#object_statuses_get) | **GET** /v1/objectstatuses/{autoid} | Gets the specified item.
*ObjectStatusesApi* | [**object_statuses_get_list**](docs/ObjectStatusesApi.md#object_statuses_get_list) | **GET** /v1/objectstatuses | Gets the list.
*ObjectTypesApi* | [**object_types_get**](docs/ObjectTypesApi.md#object_types_get) | **GET** /v1/objecttypes/{autoid} | Gets the specified item.
*ObjectTypesApi* | [**object_types_get_list**](docs/ObjectTypesApi.md#object_types_get_list) | **GET** /v1/objecttypes | Gets the list.
*ObjectTypesApi* | [**object_types_link_document**](docs/ObjectTypesApi.md#object_types_link_document) | **POST** /v1/objecttypes/{autoid}/documents/{documentid} | Links the given document to the given objecttype
*ObjectTypesApi* | [**object_types_o_data_list_documents**](docs/ObjectTypesApi.md#object_types_o_data_list_documents) | **GET** /v1/objecttypes/{autoid}/documents | Get a list of the linked documents
*ObjectTypesApi* | [**object_types_properties**](docs/ObjectTypesApi.md#object_types_properties) | **GET** /v1/objecttypes/{objecttypeid}/properties | Get a list of properties for the specified item.
*ObjectTypesApi* | [**object_types_unlink_document**](docs/ObjectTypesApi.md#object_types_unlink_document) | **DELETE** /v1/objecttypes/{autoid}/documents/{documentid} | Unlinks the given document from the given objecttype
*ObjectTypesDefaultMISchedulesApi* | [**object_types_default_mi_schedules_create**](docs/ObjectTypesDefaultMISchedulesApi.md#object_types_default_mi_schedules_create) | **POST** /v1/objecttypedefaultmischedules | Creates the specified item.
*ObjectTypesDefaultMISchedulesApi* | [**object_types_default_mi_schedules_delete**](docs/ObjectTypesDefaultMISchedulesApi.md#object_types_default_mi_schedules_delete) | **DELETE** /v1/objecttypedefaultmischedules/{autoid} | Deletes the specified item.
*ObjectTypesDefaultMISchedulesApi* | [**object_types_default_mi_schedules_get**](docs/ObjectTypesDefaultMISchedulesApi.md#object_types_default_mi_schedules_get) | **GET** /v1/objecttypedefaultmischedules/{autoid} | Gets the specified item.
*ObjectTypesDefaultMISchedulesApi* | [**object_types_default_mi_schedules_o_data_list**](docs/ObjectTypesDefaultMISchedulesApi.md#object_types_default_mi_schedules_o_data_list) | **GET** /v1/objecttypedefaultmischedules | Get a list of objecttypedefaultmischedules
*ObjectTypesDefaultMISchedulesApi* | [**object_types_default_mi_schedules_update**](docs/ObjectTypesDefaultMISchedulesApi.md#object_types_default_mi_schedules_update) | **PUT** /v1/objecttypedefaultmischedules/{autoid} | Updates the specified item.
*ObjectsApi* | [**objects_create**](docs/ObjectsApi.md#objects_create) | **POST** /v1/objects | Creates the specified item.
*ObjectsApi* | [**objects_delete**](docs/ObjectsApi.md#objects_delete) | **DELETE** /v1/objects/{autoid} | Deletes the specified item.
*ObjectsApi* | [**objects_get**](docs/ObjectsApi.md#objects_get) | **GET** /v1/objects/{autoid} | Gets the specified item.
*ObjectsApi* | [**objects_get_image**](docs/ObjectsApi.md#objects_get_image) | **GET** /v1/objects/{autoid}/image | Gets the image.
*ObjectsApi* | [**objects_get_sticker_link**](docs/ObjectsApi.md#objects_get_sticker_link) | **GET** /v1/objects/{autoid}/stickerlink | Gets the sticker QR code link
*ObjectsApi* | [**objects_link_document**](docs/ObjectsApi.md#objects_link_document) | **POST** /v1/objects/{autoid}/documents/{documentid} | Links the given document to the given object
*ObjectsApi* | [**objects_move**](docs/ObjectsApi.md#objects_move) | **POST** /v1/objects/move | The SourceObjectLocationID may only remain empty if the object has not been placed on a location yet
*ObjectsApi* | [**objects_o_data_list**](docs/ObjectsApi.md#objects_o_data_list) | **GET** /v1/objects | Get a list of objects
*ObjectsApi* | [**objects_o_data_list_documents**](docs/ObjectsApi.md#objects_o_data_list_documents) | **GET** /v1/objects/{autoid}/documents | Get a list of the linked documents
*ObjectsApi* | [**objects_o_data_list_event_logs**](docs/ObjectsApi.md#objects_o_data_list_event_logs) | **GET** /v1/objects/eventlogs | Get a list of eventlogs of all objects
*ObjectsApi* | [**objects_o_data_list_event_logs_from_object**](docs/ObjectsApi.md#objects_o_data_list_event_logs_from_object) | **GET** /v1/objects/{autoid}/eventlogs | Get a list of eventlogs of the specified object
*ObjectsApi* | [**objects_patch**](docs/ObjectsApi.md#objects_patch) | **PATCH** /v1/objects/{autoid}/properties | Updates the specified item.
*ObjectsApi* | [**objects_remove_images**](docs/ObjectsApi.md#objects_remove_images) | **DELETE** /v1/objects/{autoid}/image | Removes the image.
*ObjectsApi* | [**objects_set_images**](docs/ObjectsApi.md#objects_set_images) | **PUT** /v1/objects/{autoid}/image | Updates the image.
*ObjectsApi* | [**objects_unlink_document**](docs/ObjectsApi.md#objects_unlink_document) | **DELETE** /v1/objects/{autoid}/documents/{documentid} | Unlinks the given document from the given object
*ObjectsApi* | [**objects_update**](docs/ObjectsApi.md#objects_update) | **PUT** /v1/objects/{autoid} | Updates the specified item.
*OrderLineRentalLogApi* | [**order_line_rental_log_o_data_list**](docs/OrderLineRentalLogApi.md#order_line_rental_log_o_data_list) | **GET** /v1/orderlinerentallogs | Get a list of order line rental logs
*OrderLinesApi* | [**order_lines_get_order_line_rental_logs_by_order_line**](docs/OrderLinesApi.md#order_lines_get_order_line_rental_logs_by_order_line) | **GET** /v1/orderlines/{autoid}/orderlinerentallogs | Get a list of order line rental logs for the specified orderline.
*OrderLinesApi* | [**order_lines_o_data_list**](docs/OrderLinesApi.md#order_lines_o_data_list) | **GET** /v1/orderlines | Get a list of order lines
*OrdersApi* | [**orders_create**](docs/OrdersApi.md#orders_create) | **POST** /v1/orders | 
*OrdersApi* | [**orders_delete**](docs/OrdersApi.md#orders_delete) | **DELETE** /v1/orders/{autoid} | Deletes the specified order.
*OrdersApi* | [**orders_get**](docs/OrdersApi.md#orders_get) | **GET** /v1/orders/{autoid} | Gets the specified item.
*OrdersApi* | [**orders_get_order_line_rental_logs_by_order**](docs/OrdersApi.md#orders_get_order_line_rental_logs_by_order) | **GET** /v1/orders/{autoid}/orderlinerentallogs | Get a list of order line rental logs for the specified order.
*OrdersApi* | [**orders_get_order_line_rental_logs_by_order_and_order_line**](docs/OrdersApi.md#orders_get_order_line_rental_logs_by_order_and_order_line) | **GET** /v1/orders/{autoid}/orderline/{orderlineid}/orderlinerentallog | Get a list of order line rental logs for the specified order and orderline.
*OrdersApi* | [**orders_get_order_lines**](docs/OrdersApi.md#orders_get_order_lines) | **GET** /v1/orders/{autoid}/orderlines | Get a list of order lines for the specified order.
*OrdersApi* | [**orders_o_data_list**](docs/OrdersApi.md#orders_o_data_list) | **GET** /v1/orders | 
*PackListsApi* | [**pack_lists_get**](docs/PackListsApi.md#pack_lists_get) | **GET** /v1/packlists/{autoid} | Gets the specified item.
*PackListsApi* | [**pack_lists_o_data_list**](docs/PackListsApi.md#pack_lists_o_data_list) | **GET** /v1/packlists | 
*PackListsApi* | [**pack_lists_pick_and_pack_order_lines**](docs/PackListsApi.md#pack_lists_pick_and_pack_order_lines) | **POST** /v1/packlists/PickAndPackOrderLines | Creates a picklist and marks the supplied orderlines as picked in Centix.   Afterwards a packlist is automatically created.
*PaymentConditionsApi* | [**payment_conditions_get**](docs/PaymentConditionsApi.md#payment_conditions_get) | **GET** /v1/paymentsconditions/{autoid} | Gets the specified item.
*PaymentConditionsApi* | [**payment_conditions_get_list**](docs/PaymentConditionsApi.md#payment_conditions_get_list) | **GET** /v1/paymentsconditions | Gets the list.
*PersonStatusesApi* | [**person_statuses_get**](docs/PersonStatusesApi.md#person_statuses_get) | **GET** /v1/personstatuses/{autoid} | Gets the specified item.
*PersonStatusesApi* | [**person_statuses_get_list**](docs/PersonStatusesApi.md#person_statuses_get_list) | **GET** /v1/personstatuses | Gets the list.
*PersonsApi* | [**persons_add_person_to_relation**](docs/PersonsApi.md#persons_add_person_to_relation) | **PUT** /v1/persons/{personid}/relations/{relationid} | Adds the person to the relation.
*PersonsApi* | [**persons_create**](docs/PersonsApi.md#persons_create) | **POST** /v1/persons | Creates the specified item.
*PersonsApi* | [**persons_delete**](docs/PersonsApi.md#persons_delete) | **DELETE** /v1/persons/{autoid} | Deletes the specified item.
*PersonsApi* | [**persons_get**](docs/PersonsApi.md#persons_get) | **GET** /v1/persons/{autoid} | Gets the specified item.
*PersonsApi* | [**persons_get_signature**](docs/PersonsApi.md#persons_get_signature) | **GET** /v1/persons/{personid}/signature | Gets the signature.
*PersonsApi* | [**persons_o_data_list**](docs/PersonsApi.md#persons_o_data_list) | **GET** /v1/persons | Get a list of persons
*PersonsApi* | [**persons_relations_for_person_o_data_list**](docs/PersonsApi.md#persons_relations_for_person_o_data_list) | **GET** /v1/persons/{personid}/relations | Get a list of relations for person
*PersonsApi* | [**persons_remove_person_from_relation**](docs/PersonsApi.md#persons_remove_person_from_relation) | **DELETE** /v1/persons/{personid}/relations/{relationid} | Removes the person from the relation.
*PersonsApi* | [**persons_remove_signature**](docs/PersonsApi.md#persons_remove_signature) | **DELETE** /v1/persons/{personid}/signature | Removes the signature.
*PersonsApi* | [**persons_update**](docs/PersonsApi.md#persons_update) | **PUT** /v1/persons/{autoid} | Updates the specified item.
*PersonsApi* | [**persons_update_signature**](docs/PersonsApi.md#persons_update_signature) | **PUT** /v1/persons/{personid}/signature | Updates the signature.
*PlanBlockApi* | [**plan_block_get**](docs/PlanBlockApi.md#plan_block_get) | **GET** /v1/planblocks/{autoid} | Gets the specified item.
*PlanBlockApi* | [**plan_block_get_list**](docs/PlanBlockApi.md#plan_block_get_list) | **GET** /v1/planblocks | Gets the list.
*ProductCategoriesApi* | [**product_categories_get**](docs/ProductCategoriesApi.md#product_categories_get) | **GET** /v1/productcategories/{autoid} | Gets the specified item.
*ProductCategoriesApi* | [**product_categories_get_list**](docs/ProductCategoriesApi.md#product_categories_get_list) | **GET** /v1/productcategories | Gets the list.
*ProductGroupsApi* | [**product_groups_get**](docs/ProductGroupsApi.md#product_groups_get) | **GET** /v1/productgroups/{autoid} | Gets the specified item.
*ProductGroupsApi* | [**product_groups_get_list**](docs/ProductGroupsApi.md#product_groups_get_list) | **GET** /v1/productgroups | Gets the list.
*ProductLinesApi* | [**product_lines_get**](docs/ProductLinesApi.md#product_lines_get) | **GET** /v1/productlines/{autoid} | Gets the specified item.
*ProductLinesApi* | [**product_lines_get_list**](docs/ProductLinesApi.md#product_lines_get_list) | **GET** /v1/productlines | Gets the list.
*ProductStatusesApi* | [**product_statuses_get**](docs/ProductStatusesApi.md#product_statuses_get) | **GET** /v1/productstatuses/{autoid} | Gets the specified item.
*ProductStatusesApi* | [**product_statuses_get_list**](docs/ProductStatusesApi.md#product_statuses_get_list) | **GET** /v1/productstatuses | Gets the list.
*ProductSuppliersApi* | [**product_suppliers_create**](docs/ProductSuppliersApi.md#product_suppliers_create) | **POST** /v1/productsuppliers | Creates the specified item.
*ProductSuppliersApi* | [**product_suppliers_delete**](docs/ProductSuppliersApi.md#product_suppliers_delete) | **DELETE** /v1/productsuppliers/{autoid} | Deletes the specified item.
*ProductSuppliersApi* | [**product_suppliers_get**](docs/ProductSuppliersApi.md#product_suppliers_get) | **GET** /v1/productsuppliers/{autoid} | Gets the specified item.
*ProductSuppliersApi* | [**product_suppliers_o_data_list**](docs/ProductSuppliersApi.md#product_suppliers_o_data_list) | **GET** /v1/productsuppliers | Get a list of products
*ProductSuppliersApi* | [**product_suppliers_update**](docs/ProductSuppliersApi.md#product_suppliers_update) | **PUT** /v1/productsuppliers/{autoid} | Updates the specified item.
*ProductUnitsApi* | [**product_units_get**](docs/ProductUnitsApi.md#product_units_get) | **GET** /v1/productunits/{autoid} | Gets the specified item.
*ProductUnitsApi* | [**product_units_get_list**](docs/ProductUnitsApi.md#product_units_get_list) | **GET** /v1/productunits | Gets the list.
*ProductWarehousesApi* | [**product_warehouses_create**](docs/ProductWarehousesApi.md#product_warehouses_create) | **POST** /v1/productwarehouses | Creates the specified item.
*ProductWarehousesApi* | [**product_warehouses_delete**](docs/ProductWarehousesApi.md#product_warehouses_delete) | **DELETE** /v1/productwarehouses/{autoid} | Deletes the specified item.
*ProductWarehousesApi* | [**product_warehouses_get**](docs/ProductWarehousesApi.md#product_warehouses_get) | **GET** /v1/productwarehouses/{autoid} | Gets the specified item.
*ProductWarehousesApi* | [**product_warehouses_o_data_list**](docs/ProductWarehousesApi.md#product_warehouses_o_data_list) | **GET** /v1/productwarehouses | Get a list of product warehouses
*ProductWarehousesApi* | [**product_warehouses_update**](docs/ProductWarehousesApi.md#product_warehouses_update) | **PUT** /v1/productwarehouses/{autoid} | Updates the specified item.
*ProductsApi* | [**rental_products_create**](docs/ProductsApi.md#rental_products_create) | **POST** /v1/products/rental | Creates the specified item.
*ProductsApi* | [**rental_products_delete**](docs/ProductsApi.md#rental_products_delete) | **DELETE** /v1/products/rental/{autoid} | Deletes the specified item.
*ProductsApi* | [**rental_products_get**](docs/ProductsApi.md#rental_products_get) | **GET** /v1/products/rental/{autoid} | Gets the specified item.
*ProductsApi* | [**rental_products_get_composed**](docs/ProductsApi.md#rental_products_get_composed) | **GET** /v1/products/rental/{autoid}/composed | Gets the composed products for the specified item.
*ProductsApi* | [**rental_products_get_image**](docs/ProductsApi.md#rental_products_get_image) | **GET** /v1/products/rental/{autoid}/image | Gets the image.
*ProductsApi* | [**rental_products_link_document**](docs/ProductsApi.md#rental_products_link_document) | **POST** /v1/products/rental/{autoid}/documents/{documentid} | Links the given document to the given rental product
*ProductsApi* | [**rental_products_o_data_list**](docs/ProductsApi.md#rental_products_o_data_list) | **GET** /v1/products/rental | Get a list of products
*ProductsApi* | [**rental_products_o_data_list_documents**](docs/ProductsApi.md#rental_products_o_data_list_documents) | **GET** /v1/products/rental/{autoid}/documents | Get a list of the linked documents
*ProductsApi* | [**rental_products_patch**](docs/ProductsApi.md#rental_products_patch) | **PATCH** /v1/products/rental/{autoid}/properties | Updates the specified item.
*ProductsApi* | [**rental_products_patch_translated_properties**](docs/ProductsApi.md#rental_products_patch_translated_properties) | **PATCH** /v1/products/rental/{autoid}/translatedproperties | Updates the translated properties.
*ProductsApi* | [**rental_products_remove_images**](docs/ProductsApi.md#rental_products_remove_images) | **DELETE** /v1/products/rental/{autoid}/image | Removes the image.
*ProductsApi* | [**rental_products_set_images**](docs/ProductsApi.md#rental_products_set_images) | **PUT** /v1/products/rental/{autoid}/image | Updates the image.
*ProductsApi* | [**rental_products_unlink_document**](docs/ProductsApi.md#rental_products_unlink_document) | **DELETE** /v1/products/rental/{autoid}/documents/{documentid} | Unlinks the given document from the given rental product
*ProductsApi* | [**rental_products_update**](docs/ProductsApi.md#rental_products_update) | **PUT** /v1/products/rental/{autoid} | Updates the specified item.
*ProductsApi* | [**sales_products_create**](docs/ProductsApi.md#sales_products_create) | **POST** /v1/products/sales | Creates the specified item.
*ProductsApi* | [**sales_products_delete**](docs/ProductsApi.md#sales_products_delete) | **DELETE** /v1/products/sales/{autoid} | Deletes the specified item.
*ProductsApi* | [**sales_products_get**](docs/ProductsApi.md#sales_products_get) | **GET** /v1/products/sales/{autoid} | Gets the specified item.
*ProductsApi* | [**sales_products_get_composed**](docs/ProductsApi.md#sales_products_get_composed) | **GET** /v1/products/sales/{autoid}/composed | Gets the composed products for the specified item.
*ProductsApi* | [**sales_products_get_image**](docs/ProductsApi.md#sales_products_get_image) | **GET** /v1/products/sales/{autoid}/image | Gets the image.
*ProductsApi* | [**sales_products_link_document**](docs/ProductsApi.md#sales_products_link_document) | **POST** /v1/products/sales/{autoid}/documents/{documentid} | Links the given document to the given sales product
*ProductsApi* | [**sales_products_o_data_list**](docs/ProductsApi.md#sales_products_o_data_list) | **GET** /v1/products/sales | Get a list of products
*ProductsApi* | [**sales_products_o_data_list_documents**](docs/ProductsApi.md#sales_products_o_data_list_documents) | **GET** /v1/products/sales/{autoid}/documents | Get a list of the linked documents
*ProductsApi* | [**sales_products_patch**](docs/ProductsApi.md#sales_products_patch) | **PATCH** /v1/products/sales/{autoid}/properties | Updates the specified item.
*ProductsApi* | [**sales_products_patch_translated_properties**](docs/ProductsApi.md#sales_products_patch_translated_properties) | **PATCH** /v1/products/sales/{autoid}/translatedproperties | Updates the translated properties.
*ProductsApi* | [**sales_products_remove_images**](docs/ProductsApi.md#sales_products_remove_images) | **DELETE** /v1/products/sales/{autoid}/image | Removes the images.
*ProductsApi* | [**sales_products_set_images**](docs/ProductsApi.md#sales_products_set_images) | **PUT** /v1/products/sales/{autoid}/image | Sets the images.
*ProductsApi* | [**sales_products_unlink_document**](docs/ProductsApi.md#sales_products_unlink_document) | **DELETE** /v1/products/sales/{autoid}/documents/{documentid} | Unlinks the given document from the given sales product
*ProductsApi* | [**sales_products_update**](docs/ProductsApi.md#sales_products_update) | **PUT** /v1/products/sales/{autoid} | Updates the specified item.
*ProjectApi* | [**project_get**](docs/ProjectApi.md#project_get) | **GET** /v1/projects/{autoid} | Gets the specified item.
*ProjectApi* | [**project_o_data_list**](docs/ProjectApi.md#project_o_data_list) | **GET** /v1/projects | Get a list of projects
*ProjectStatusApi* | [**project_status_get**](docs/ProjectStatusApi.md#project_status_get) | **GET** /v1/projectstatuses/{autoid} | Gets the specified item.
*ProjectStatusApi* | [**project_status_get_list**](docs/ProjectStatusApi.md#project_status_get_list) | **GET** /v1/projectstatuses | Gets the list.
*ProjectTypeApi* | [**project_type_get**](docs/ProjectTypeApi.md#project_type_get) | **GET** /v1/projecttypes/{autoid} | Gets the specified item.
*ProjectTypeApi* | [**project_type_get_list**](docs/ProjectTypeApi.md#project_type_get_list) | **GET** /v1/projecttypes | Gets the list.
*ReasonApi* | [**reason_get**](docs/ReasonApi.md#reason_get) | **GET** /v1/reasons/{autoid} | Gets the specified item.
*ReasonApi* | [**reason_get_list**](docs/ReasonApi.md#reason_get_list) | **GET** /v1/reasons | Gets the list.
*RegionsApi* | [**regions_get**](docs/RegionsApi.md#regions_get) | **GET** /v1/regions/{autoid} | Gets the specified item.
*RegionsApi* | [**regions_get_list**](docs/RegionsApi.md#regions_get_list) | **GET** /v1/regions | Gets the list.
*RelationGroupsApi* | [**relation_groups_get**](docs/RelationGroupsApi.md#relation_groups_get) | **GET** /v1/relationgroups/{autoid} | Gets the specified item.
*RelationGroupsApi* | [**relation_groups_get_list**](docs/RelationGroupsApi.md#relation_groups_get_list) | **GET** /v1/relationgroups | Gets the list.
*RelationStatusApi* | [**relation_status_get**](docs/RelationStatusApi.md#relation_status_get) | **GET** /v1/relationstatuses/{autoid} | Gets the specified item.
*RelationStatusApi* | [**relation_status_get_list**](docs/RelationStatusApi.md#relation_status_get_list) | **GET** /v1/relationstatuses | Gets the list.
*RelationsApi* | [**relations_create**](docs/RelationsApi.md#relations_create) | **POST** /v1/relations | Creates the specified item.
*RelationsApi* | [**relations_delete**](docs/RelationsApi.md#relations_delete) | **DELETE** /v1/relations/{autoid} | Deletes the specified item.
*RelationsApi* | [**relations_get**](docs/RelationsApi.md#relations_get) | **GET** /v1/relations/{autoid} | Gets the specified item.
*RelationsApi* | [**relations_get_logo**](docs/RelationsApi.md#relations_get_logo) | **GET** /v1/relations/{relationid}/logo | Gets the logo.
*RelationsApi* | [**relations_o_data_list**](docs/RelationsApi.md#relations_o_data_list) | **GET** /v1/relations | Get a list of relations
*RelationsApi* | [**relations_persons_for_relation_o_data_list**](docs/RelationsApi.md#relations_persons_for_relation_o_data_list) | **GET** /v1/relations/{relationid}/contactpersons | Get a list of persons for relation
*RelationsApi* | [**relations_remove_logo**](docs/RelationsApi.md#relations_remove_logo) | **DELETE** /v1/relations/{relationid}/logo | Removes the logo.
*RelationsApi* | [**relations_update**](docs/RelationsApi.md#relations_update) | **PUT** /v1/relations/{autoid} | Updates the specified item.
*RelationsApi* | [**relations_update_logo**](docs/RelationsApi.md#relations_update_logo) | **PUT** /v1/relations/{relationid}/logo | Updates the logo.
*StockIndicationsApi* | [**stock_indications_get**](docs/StockIndicationsApi.md#stock_indications_get) | **GET** /v1/stockindications/{autoid} | Gets the specified item.
*StockIndicationsApi* | [**stock_indications_get_list**](docs/StockIndicationsApi.md#stock_indications_get_list) | **GET** /v1/stockindications | Gets the list.
*TeamsApi* | [**teams_get**](docs/TeamsApi.md#teams_get) | **GET** /v1/teams/{autoid} | Gets the specified item.
*TeamsApi* | [**teams_get_list**](docs/TeamsApi.md#teams_get_list) | **GET** /v1/teams | Gets the list.
*TimeSchedulesApi* | [**time_schedules_get**](docs/TimeSchedulesApi.md#time_schedules_get) | **GET** /v1/timeschedules/{autoid} | Gets the specified item.
*TimeSchedulesApi* | [**time_schedules_get_list**](docs/TimeSchedulesApi.md#time_schedules_get_list) | **GET** /v1/timeschedules | Gets the list.
*TitlesApi* | [**titles_get**](docs/TitlesApi.md#titles_get) | **GET** /v1/titles/{autoid} | Gets the specified item.
*TitlesApi* | [**titles_get_list**](docs/TitlesApi.md#titles_get_list) | **GET** /v1/titles | Gets the list.
*TransportMethodApi* | [**transport_method_get**](docs/TransportMethodApi.md#transport_method_get) | **GET** /v1/transportmethods/{autoid} | Gets the specified item.
*TransportMethodApi* | [**transport_method_o_data_list**](docs/TransportMethodApi.md#transport_method_o_data_list) | **GET** /v1/transportmethods | Get a list of transport methods
*UsersApi* | [**users_get**](docs/UsersApi.md#users_get) | **GET** /v1/users/{autoid} | Gets the specified item.
*UsersApi* | [**users_o_data_list**](docs/UsersApi.md#users_o_data_list) | **GET** /v1/users | Get a list of users
*VatsApi* | [**vats_get**](docs/VatsApi.md#vats_get) | **GET** /v1/vats/{autoid} | Gets the specified item.
*VatsApi* | [**vats_get_list**](docs/VatsApi.md#vats_get_list) | **GET** /v1/vats | Gets the list.
*WarehousePickzonesApi* | [**warehouse_pickzones_get**](docs/WarehousePickzonesApi.md#warehouse_pickzones_get) | **GET** /v1/warehousepickzones/{autoid} | Gets the specified item.
*WarehousePickzonesApi* | [**warehouse_pickzones_get_list**](docs/WarehousePickzonesApi.md#warehouse_pickzones_get_list) | **GET** /v1/warehousepickzones | Gets the list.
*WarehousesApi* | [**warehouses_get**](docs/WarehousesApi.md#warehouses_get) | **GET** /v1/warehouses/{autoid} | Gets the specified item.
*WarehousesApi* | [**warehouses_get_list**](docs/WarehousesApi.md#warehouses_get_list) | **GET** /v1/warehouses | Gets the list.
*WorkFlowGroupsApi* | [**work_flow_groups_get**](docs/WorkFlowGroupsApi.md#work_flow_groups_get) | **GET** /v1/workflowgroups/{autoid} | Gets the specified item.
*WorkFlowGroupsApi* | [**work_flow_groups_get_list**](docs/WorkFlowGroupsApi.md#work_flow_groups_get_list) | **GET** /v1/workflowgroups | Gets the list.
*WorkFlowStatusApi* | [**work_flow_status_get**](docs/WorkFlowStatusApi.md#work_flow_status_get) | **GET** /v1/workflowstatuses/{autoid} | Gets the specified item.
*WorkFlowStatusApi* | [**work_flow_status_get_list**](docs/WorkFlowStatusApi.md#work_flow_status_get_list) | **GET** /v1/workflowstatuses | Gets the list.
*WorkFlowSubGroupsApi* | [**work_flow_sub_groups_get**](docs/WorkFlowSubGroupsApi.md#work_flow_sub_groups_get) | **GET** /v1/workflowsubgroups/{autoid} | Gets the specified item.
*WorkFlowSubGroupsApi* | [**work_flow_sub_groups_get_list**](docs/WorkFlowSubGroupsApi.md#work_flow_sub_groups_get_list) | **GET** /v1/workflowsubgroups | Gets the list.
*WorkflowsApi* | [**workflows_create**](docs/WorkflowsApi.md#workflows_create) | **POST** /v1/workflows | Creates the specified item.
*WorkflowsApi* | [**workflows_delete**](docs/WorkflowsApi.md#workflows_delete) | **DELETE** /v1/workflows/{autoid} | Deletes the specified item.
*WorkflowsApi* | [**workflows_get**](docs/WorkflowsApi.md#workflows_get) | **GET** /v1/workflows/{autoid} | Gets the specified item.
*WorkflowsApi* | [**workflows_hour_registrations**](docs/WorkflowsApi.md#workflows_hour_registrations) | **GET** /v1/workflows/{workflowid}/hourregistrations | Get a list of hour registrations for the specified item.
*WorkflowsApi* | [**workflows_o_data_list**](docs/WorkflowsApi.md#workflows_o_data_list) | **GET** /v1/workflows | Get a list of workflow items
*WorkflowsApi* | [**workflows_patch**](docs/WorkflowsApi.md#workflows_patch) | **PATCH** /v1/workflows/{autoid}/properties | Updates the specified item.
*WorkflowsApi* | [**workflows_product_realizations**](docs/WorkflowsApi.md#workflows_product_realizations) | **GET** /v1/workflows/{workflowid}/productrealizations | Get a list of product realizations for the specified item.
*WorkflowsApi* | [**workflows_status_logs**](docs/WorkflowsApi.md#workflows_status_logs) | **GET** /v1/workflows/{workflowid}/statuslogs | Get a list of workflow status logs for the specified item.
*WorkflowsApi* | [**workflows_update**](docs/WorkflowsApi.md#workflows_update) | **PUT** /v1/workflows/{autoid} | Updates the specified item.
*WorkingTicketApi* | [**working_ticket_get**](docs/WorkingTicketApi.md#working_ticket_get) | **GET** /v1/workingtickets/{autoid} | Gets the specified item.
*WorkingTicketApi* | [**working_ticket_hour_registrations**](docs/WorkingTicketApi.md#working_ticket_hour_registrations) | **GET** /v1/workingtickets/{workingticketid}/hourregistrations | Get a list of hour registrations for the specified item.
*WorkingTicketApi* | [**working_ticket_o_data_list**](docs/WorkingTicketApi.md#working_ticket_o_data_list) | **GET** /v1/workingtickets | Get a list of working tickets
*WorkingTicketApi* | [**working_ticket_process**](docs/WorkingTicketApi.md#working_ticket_process) | **PUT** /v1/workingtickets/{workingticketid}/process/{personid} | 
*WorkingTicketApi* | [**working_ticket_product_realizations**](docs/WorkingTicketApi.md#working_ticket_product_realizations) | **GET** /v1/workingtickets/{workingticketid}/productrealizations | Get a list of product realizations for the specified item.
*WorkingTicketApi* | [**working_ticket_workflows**](docs/WorkingTicketApi.md#working_ticket_workflows) | **GET** /v1/workingtickets/{workingticketid}/workflows | Get a list of workflow items for the specified item.
*WorkingTicketStatusApi* | [**working_ticket_status_get**](docs/WorkingTicketStatusApi.md#working_ticket_status_get) | **GET** /v1/workingticketstatuses/{autoid} | Gets the specified item.
*WorkingTicketStatusApi* | [**working_ticket_status_get_list**](docs/WorkingTicketStatusApi.md#working_ticket_status_get_list) | **GET** /v1/workingticketstatuses | Gets the list.


## Documentation For Models

 - [CentixAPICoreListResultCentixAPIDTOReadAbsenceRegistration](docs/CentixAPICoreListResultCentixAPIDTOReadAbsenceRegistration.md)
 - [CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule](docs/CentixAPICoreListResultCentixAPIDTOReadBrandTypeDefaultMISchedule.md)
 - [CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevisionQuery](docs/CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevisionQuery.md)
 - [CentixAPICoreListResultCentixAPIDTOReadDevice](docs/CentixAPICoreListResultCentixAPIDTOReadDevice.md)
 - [CentixAPICoreListResultCentixAPIDTOReadDeviceEvent](docs/CentixAPICoreListResultCentixAPIDTOReadDeviceEvent.md)
 - [CentixAPICoreListResultCentixAPIDTOReadDocument](docs/CentixAPICoreListResultCentixAPIDTOReadDocument.md)
 - [CentixAPICoreListResultCentixAPIDTOReadEInvoice](docs/CentixAPICoreListResultCentixAPIDTOReadEInvoice.md)
 - [CentixAPICoreListResultCentixAPIDTOReadEInvoiceLine](docs/CentixAPICoreListResultCentixAPIDTOReadEInvoiceLine.md)
 - [CentixAPICoreListResultCentixAPIDTOReadInvoice](docs/CentixAPICoreListResultCentixAPIDTOReadInvoice.md)
 - [CentixAPICoreListResultCentixAPIDTOReadInvoiceLine](docs/CentixAPICoreListResultCentixAPIDTOReadInvoiceLine.md)
 - [CentixAPICoreListResultCentixAPIDTOReadJournalQuery](docs/CentixAPICoreListResultCentixAPIDTOReadJournalQuery.md)
 - [CentixAPICoreListResultCentixAPIDTOReadLocation](docs/CentixAPICoreListResultCentixAPIDTOReadLocation.md)
 - [CentixAPICoreListResultCentixAPIDTOReadLocationUser](docs/CentixAPICoreListResultCentixAPIDTOReadLocationUser.md)
 - [CentixAPICoreListResultCentixAPIDTOReadMI](docs/CentixAPICoreListResultCentixAPIDTOReadMI.md)
 - [CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule](docs/CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule.md)
 - [CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementView](docs/CentixAPICoreListResultCentixAPIDTOReadMeasuringDevice1ObjectMeasurementView.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObject](docs/CentixAPICoreListResultCentixAPIDTOReadObject.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectCode](docs/CentixAPICoreListResultCentixAPIDTOReadObjectCode.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectEventLog](docs/CentixAPICoreListResultCentixAPIDTOReadObjectEventLog.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectLocation](docs/CentixAPICoreListResultCentixAPIDTOReadObjectLocation.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectLocationLog](docs/CentixAPICoreListResultCentixAPIDTOReadObjectLocationLog.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule](docs/CentixAPICoreListResultCentixAPIDTOReadObjectMISchedule.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectMIView](docs/CentixAPICoreListResultCentixAPIDTOReadObjectMIView.md)
 - [CentixAPICoreListResultCentixAPIDTOReadObjectTypeDefaultMISchedule](docs/CentixAPICoreListResultCentixAPIDTOReadObjectTypeDefaultMISchedule.md)
 - [CentixAPICoreListResultCentixAPIDTOReadOrderLine](docs/CentixAPICoreListResultCentixAPIDTOReadOrderLine.md)
 - [CentixAPICoreListResultCentixAPIDTOReadOrderLineRentalLog](docs/CentixAPICoreListResultCentixAPIDTOReadOrderLineRentalLog.md)
 - [CentixAPICoreListResultCentixAPIDTOReadOrderQuery](docs/CentixAPICoreListResultCentixAPIDTOReadOrderQuery.md)
 - [CentixAPICoreListResultCentixAPIDTOReadPackListQuery](docs/CentixAPICoreListResultCentixAPIDTOReadPackListQuery.md)
 - [CentixAPICoreListResultCentixAPIDTOReadPerson](docs/CentixAPICoreListResultCentixAPIDTOReadPerson.md)
 - [CentixAPICoreListResultCentixAPIDTOReadProductSupplier](docs/CentixAPICoreListResultCentixAPIDTOReadProductSupplier.md)
 - [CentixAPICoreListResultCentixAPIDTOReadProductWarehouse](docs/CentixAPICoreListResultCentixAPIDTOReadProductWarehouse.md)
 - [CentixAPICoreListResultCentixAPIDTOReadRelation](docs/CentixAPICoreListResultCentixAPIDTOReadRelation.md)
 - [CentixAPICoreListResultCentixAPIDTOReadRentalProduct](docs/CentixAPICoreListResultCentixAPIDTOReadRentalProduct.md)
 - [CentixAPICoreListResultCentixAPIDTOReadSalesProduct](docs/CentixAPICoreListResultCentixAPIDTOReadSalesProduct.md)
 - [CentixAPICoreListResultCentixAPIDTOReadUser](docs/CentixAPICoreListResultCentixAPIDTOReadUser.md)
 - [CentixAPICoreListResultCentixAPIDTOReadWorkflow](docs/CentixAPICoreListResultCentixAPIDTOReadWorkflow.md)
 - [CentixAPICoreListResultCentixAPIDTOReadWorkingTicket](docs/CentixAPICoreListResultCentixAPIDTOReadWorkingTicket.md)
 - [CentixAPIDTOAddress](docs/CentixAPIDTOAddress.md)
 - [CentixAPIDTOContentLanguageItem](docs/CentixAPIDTOContentLanguageItem.md)
 - [CentixAPIDTOCreateAbsenceRegistration](docs/CentixAPIDTOCreateAbsenceRegistration.md)
 - [CentixAPIDTOCreateBrand](docs/CentixAPIDTOCreateBrand.md)
 - [CentixAPIDTOCreateBrandType](docs/CentixAPIDTOCreateBrandType.md)
 - [CentixAPIDTOCreateBrandTypeDefaultMISchedule](docs/CentixAPIDTOCreateBrandTypeDefaultMISchedule.md)
 - [CentixAPIDTOCreateBrandTypeRevision](docs/CentixAPIDTOCreateBrandTypeRevision.md)
 - [CentixAPIDTOCreateBudgetCode](docs/CentixAPIDTOCreateBudgetCode.md)
 - [CentixAPIDTOCreateCostCategory](docs/CentixAPIDTOCreateCostCategory.md)
 - [CentixAPIDTOCreateCostCode](docs/CentixAPIDTOCreateCostCode.md)
 - [CentixAPIDTOCreateDocumentFromUrl](docs/CentixAPIDTOCreateDocumentFromUrl.md)
 - [CentixAPIDTOCreateLocation](docs/CentixAPIDTOCreateLocation.md)
 - [CentixAPIDTOCreateLocationUser](docs/CentixAPIDTOCreateLocationUser.md)
 - [CentixAPIDTOCreateObject](docs/CentixAPIDTOCreateObject.md)
 - [CentixAPIDTOCreateObjectCode](docs/CentixAPIDTOCreateObjectCode.md)
 - [CentixAPIDTOCreateObjectMISchedule](docs/CentixAPIDTOCreateObjectMISchedule.md)
 - [CentixAPIDTOCreateObjectMeasurement](docs/CentixAPIDTOCreateObjectMeasurement.md)
 - [CentixAPIDTOCreateObjectMeasurementFunction](docs/CentixAPIDTOCreateObjectMeasurementFunction.md)
 - [CentixAPIDTOCreateObjectTypeDefaultMISchedule](docs/CentixAPIDTOCreateObjectTypeDefaultMISchedule.md)
 - [CentixAPIDTOCreateOrder](docs/CentixAPIDTOCreateOrder.md)
 - [CentixAPIDTOCreateOrderLine](docs/CentixAPIDTOCreateOrderLine.md)
 - [CentixAPIDTOCreatePerson](docs/CentixAPIDTOCreatePerson.md)
 - [CentixAPIDTOCreateProductSupplier](docs/CentixAPIDTOCreateProductSupplier.md)
 - [CentixAPIDTOCreateProductWarehouse](docs/CentixAPIDTOCreateProductWarehouse.md)
 - [CentixAPIDTOCreateRelation](docs/CentixAPIDTOCreateRelation.md)
 - [CentixAPIDTOCreateRentalProduct](docs/CentixAPIDTOCreateRentalProduct.md)
 - [CentixAPIDTOCreateSalesProduct](docs/CentixAPIDTOCreateSalesProduct.md)
 - [CentixAPIDTOCreateWorkflow](docs/CentixAPIDTOCreateWorkflow.md)
 - [CentixAPIDTOExecutePickAndPackOrderlines](docs/CentixAPIDTOExecutePickAndPackOrderlines.md)
 - [CentixAPIDTOFullReadObject](docs/CentixAPIDTOFullReadObject.md)
 - [CentixAPIDTOFullReadRentalProduct](docs/CentixAPIDTOFullReadRentalProduct.md)
 - [CentixAPIDTOFullReadSalesProduct](docs/CentixAPIDTOFullReadSalesProduct.md)
 - [CentixAPIDTOMoveObject](docs/CentixAPIDTOMoveObject.md)
 - [CentixAPIDTOPackListCreatedResult](docs/CentixAPIDTOPackListCreatedResult.md)
 - [CentixAPIDTOPickAndPackOrderline](docs/CentixAPIDTOPickAndPackOrderline.md)
 - [CentixAPIDTOProcessJournal](docs/CentixAPIDTOProcessJournal.md)
 - [CentixAPIDTOReadAbsenceKind](docs/CentixAPIDTOReadAbsenceKind.md)
 - [CentixAPIDTOReadAbsenceRegistration](docs/CentixAPIDTOReadAbsenceRegistration.md)
 - [CentixAPIDTOReadAdministration](docs/CentixAPIDTOReadAdministration.md)
 - [CentixAPIDTOReadArticleRealization](docs/CentixAPIDTOReadArticleRealization.md)
 - [CentixAPIDTOReadBlockReason](docs/CentixAPIDTOReadBlockReason.md)
 - [CentixAPIDTOReadBrand](docs/CentixAPIDTOReadBrand.md)
 - [CentixAPIDTOReadBrandType](docs/CentixAPIDTOReadBrandType.md)
 - [CentixAPIDTOReadBrandTypeDefaultMISchedule](docs/CentixAPIDTOReadBrandTypeDefaultMISchedule.md)
 - [CentixAPIDTOReadBrandTypeRevision](docs/CentixAPIDTOReadBrandTypeRevision.md)
 - [CentixAPIDTOReadBrandTypeRevisionQuery](docs/CentixAPIDTOReadBrandTypeRevisionQuery.md)
 - [CentixAPIDTOReadBudgetCode](docs/CentixAPIDTOReadBudgetCode.md)
 - [CentixAPIDTOReadComposedProduct](docs/CentixAPIDTOReadComposedProduct.md)
 - [CentixAPIDTOReadComposedProductPart](docs/CentixAPIDTOReadComposedProductPart.md)
 - [CentixAPIDTOReadCondition](docs/CentixAPIDTOReadCondition.md)
 - [CentixAPIDTOReadCostCategory](docs/CentixAPIDTOReadCostCategory.md)
 - [CentixAPIDTOReadCostCode](docs/CentixAPIDTOReadCostCode.md)
 - [CentixAPIDTOReadCountry](docs/CentixAPIDTOReadCountry.md)
 - [CentixAPIDTOReadCountryState](docs/CentixAPIDTOReadCountryState.md)
 - [CentixAPIDTOReadCountryStateCounty](docs/CentixAPIDTOReadCountryStateCounty.md)
 - [CentixAPIDTOReadCurrency](docs/CentixAPIDTOReadCurrency.md)
 - [CentixAPIDTOReadDevice](docs/CentixAPIDTOReadDevice.md)
 - [CentixAPIDTOReadDeviceEvent](docs/CentixAPIDTOReadDeviceEvent.md)
 - [CentixAPIDTOReadDocument](docs/CentixAPIDTOReadDocument.md)
 - [CentixAPIDTOReadDocumentKind](docs/CentixAPIDTOReadDocumentKind.md)
 - [CentixAPIDTOReadEInvoice](docs/CentixAPIDTOReadEInvoice.md)
 - [CentixAPIDTOReadEInvoiceLine](docs/CentixAPIDTOReadEInvoiceLine.md)
 - [CentixAPIDTOReadFieldDictionary](docs/CentixAPIDTOReadFieldDictionary.md)
 - [CentixAPIDTOReadFixedAssetGroup](docs/CentixAPIDTOReadFixedAssetGroup.md)
 - [CentixAPIDTOReadGeneralLedger](docs/CentixAPIDTOReadGeneralLedger.md)
 - [CentixAPIDTOReadHSCode](docs/CentixAPIDTOReadHSCode.md)
 - [CentixAPIDTOReadHourRegistration](docs/CentixAPIDTOReadHourRegistration.md)
 - [CentixAPIDTOReadInvoice](docs/CentixAPIDTOReadInvoice.md)
 - [CentixAPIDTOReadInvoiceLine](docs/CentixAPIDTOReadInvoiceLine.md)
 - [CentixAPIDTOReadJournal](docs/CentixAPIDTOReadJournal.md)
 - [CentixAPIDTOReadJournalLine](docs/CentixAPIDTOReadJournalLine.md)
 - [CentixAPIDTOReadJournalQuery](docs/CentixAPIDTOReadJournalQuery.md)
 - [CentixAPIDTOReadLanguage](docs/CentixAPIDTOReadLanguage.md)
 - [CentixAPIDTOReadLocation](docs/CentixAPIDTOReadLocation.md)
 - [CentixAPIDTOReadLocationGroup](docs/CentixAPIDTOReadLocationGroup.md)
 - [CentixAPIDTOReadLocationStatus](docs/CentixAPIDTOReadLocationStatus.md)
 - [CentixAPIDTOReadLocationType](docs/CentixAPIDTOReadLocationType.md)
 - [CentixAPIDTOReadLocationUser](docs/CentixAPIDTOReadLocationUser.md)
 - [CentixAPIDTOReadLockReason](docs/CentixAPIDTOReadLockReason.md)
 - [CentixAPIDTOReadMI](docs/CentixAPIDTOReadMI.md)
 - [CentixAPIDTOReadMIPlan](docs/CentixAPIDTOReadMIPlan.md)
 - [CentixAPIDTOReadMeasuringDevice1Measurement](docs/CentixAPIDTOReadMeasuringDevice1Measurement.md)
 - [CentixAPIDTOReadMeasuringDevice1MeasurementFunction](docs/CentixAPIDTOReadMeasuringDevice1MeasurementFunction.md)
 - [CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule](docs/CentixAPIDTOReadMeasuringDevice1ObjectMeasurementSchedule.md)
 - [CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView](docs/CentixAPIDTOReadMeasuringDevice1ObjectMeasurementView.md)
 - [CentixAPIDTOReadNoSignatureReason](docs/CentixAPIDTOReadNoSignatureReason.md)
 - [CentixAPIDTOReadObject](docs/CentixAPIDTOReadObject.md)
 - [CentixAPIDTOReadObjectCode](docs/CentixAPIDTOReadObjectCode.md)
 - [CentixAPIDTOReadObjectEventLog](docs/CentixAPIDTOReadObjectEventLog.md)
 - [CentixAPIDTOReadObjectGroup](docs/CentixAPIDTOReadObjectGroup.md)
 - [CentixAPIDTOReadObjectLocation](docs/CentixAPIDTOReadObjectLocation.md)
 - [CentixAPIDTOReadObjectLocationLog](docs/CentixAPIDTOReadObjectLocationLog.md)
 - [CentixAPIDTOReadObjectMI](docs/CentixAPIDTOReadObjectMI.md)
 - [CentixAPIDTOReadObjectMISchedule](docs/CentixAPIDTOReadObjectMISchedule.md)
 - [CentixAPIDTOReadObjectMIView](docs/CentixAPIDTOReadObjectMIView.md)
 - [CentixAPIDTOReadObjectStatus](docs/CentixAPIDTOReadObjectStatus.md)
 - [CentixAPIDTOReadObjectType](docs/CentixAPIDTOReadObjectType.md)
 - [CentixAPIDTOReadObjectTypeDefaultMISchedule](docs/CentixAPIDTOReadObjectTypeDefaultMISchedule.md)
 - [CentixAPIDTOReadOrder](docs/CentixAPIDTOReadOrder.md)
 - [CentixAPIDTOReadOrderLine](docs/CentixAPIDTOReadOrderLine.md)
 - [CentixAPIDTOReadOrderLineRentalLog](docs/CentixAPIDTOReadOrderLineRentalLog.md)
 - [CentixAPIDTOReadOrderQuery](docs/CentixAPIDTOReadOrderQuery.md)
 - [CentixAPIDTOReadPackList](docs/CentixAPIDTOReadPackList.md)
 - [CentixAPIDTOReadPackListItem](docs/CentixAPIDTOReadPackListItem.md)
 - [CentixAPIDTOReadPackListItemObject](docs/CentixAPIDTOReadPackListItemObject.md)
 - [CentixAPIDTOReadPackListQuery](docs/CentixAPIDTOReadPackListQuery.md)
 - [CentixAPIDTOReadPaymentCondition](docs/CentixAPIDTOReadPaymentCondition.md)
 - [CentixAPIDTOReadPerson](docs/CentixAPIDTOReadPerson.md)
 - [CentixAPIDTOReadPersonStatus](docs/CentixAPIDTOReadPersonStatus.md)
 - [CentixAPIDTOReadPlanBlock](docs/CentixAPIDTOReadPlanBlock.md)
 - [CentixAPIDTOReadProductCategory](docs/CentixAPIDTOReadProductCategory.md)
 - [CentixAPIDTOReadProductGroup](docs/CentixAPIDTOReadProductGroup.md)
 - [CentixAPIDTOReadProductLine](docs/CentixAPIDTOReadProductLine.md)
 - [CentixAPIDTOReadProductStatus](docs/CentixAPIDTOReadProductStatus.md)
 - [CentixAPIDTOReadProductSupplier](docs/CentixAPIDTOReadProductSupplier.md)
 - [CentixAPIDTOReadProductUnit](docs/CentixAPIDTOReadProductUnit.md)
 - [CentixAPIDTOReadProductWarehouse](docs/CentixAPIDTOReadProductWarehouse.md)
 - [CentixAPIDTOReadProject](docs/CentixAPIDTOReadProject.md)
 - [CentixAPIDTOReadProjectStatus](docs/CentixAPIDTOReadProjectStatus.md)
 - [CentixAPIDTOReadProjectType](docs/CentixAPIDTOReadProjectType.md)
 - [CentixAPIDTOReadReason](docs/CentixAPIDTOReadReason.md)
 - [CentixAPIDTOReadRegion](docs/CentixAPIDTOReadRegion.md)
 - [CentixAPIDTOReadRelation](docs/CentixAPIDTOReadRelation.md)
 - [CentixAPIDTOReadRelationGroup](docs/CentixAPIDTOReadRelationGroup.md)
 - [CentixAPIDTOReadRelationStatus](docs/CentixAPIDTOReadRelationStatus.md)
 - [CentixAPIDTOReadRentalProduct](docs/CentixAPIDTOReadRentalProduct.md)
 - [CentixAPIDTOReadSalesProduct](docs/CentixAPIDTOReadSalesProduct.md)
 - [CentixAPIDTOReadStockIndication](docs/CentixAPIDTOReadStockIndication.md)
 - [CentixAPIDTOReadTeam](docs/CentixAPIDTOReadTeam.md)
 - [CentixAPIDTOReadTimeSchedule](docs/CentixAPIDTOReadTimeSchedule.md)
 - [CentixAPIDTOReadTitle](docs/CentixAPIDTOReadTitle.md)
 - [CentixAPIDTOReadTransportMethod](docs/CentixAPIDTOReadTransportMethod.md)
 - [CentixAPIDTOReadUser](docs/CentixAPIDTOReadUser.md)
 - [CentixAPIDTOReadVat](docs/CentixAPIDTOReadVat.md)
 - [CentixAPIDTOReadWarehouse](docs/CentixAPIDTOReadWarehouse.md)
 - [CentixAPIDTOReadWarehousePickzone](docs/CentixAPIDTOReadWarehousePickzone.md)
 - [CentixAPIDTOReadWorkFlowGroup](docs/CentixAPIDTOReadWorkFlowGroup.md)
 - [CentixAPIDTOReadWorkFlowStatus](docs/CentixAPIDTOReadWorkFlowStatus.md)
 - [CentixAPIDTOReadWorkFlowStatusLog](docs/CentixAPIDTOReadWorkFlowStatusLog.md)
 - [CentixAPIDTOReadWorkFlowSubGroup](docs/CentixAPIDTOReadWorkFlowSubGroup.md)
 - [CentixAPIDTOReadWorkflow](docs/CentixAPIDTOReadWorkflow.md)
 - [CentixAPIDTOReadWorkingTicket](docs/CentixAPIDTOReadWorkingTicket.md)
 - [CentixAPIDTOReadWorkingTicketStatus](docs/CentixAPIDTOReadWorkingTicketStatus.md)
 - [CentixAPIDTORentalRate](docs/CentixAPIDTORentalRate.md)
 - [CentixAPIDTOUpdateAbsenceRegistration](docs/CentixAPIDTOUpdateAbsenceRegistration.md)
 - [CentixAPIDTOUpdateBrand](docs/CentixAPIDTOUpdateBrand.md)
 - [CentixAPIDTOUpdateBrandType](docs/CentixAPIDTOUpdateBrandType.md)
 - [CentixAPIDTOUpdateBrandTypeDefaultMISchedule](docs/CentixAPIDTOUpdateBrandTypeDefaultMISchedule.md)
 - [CentixAPIDTOUpdateBrandTypeRevision](docs/CentixAPIDTOUpdateBrandTypeRevision.md)
 - [CentixAPIDTOUpdateBudgetCode](docs/CentixAPIDTOUpdateBudgetCode.md)
 - [CentixAPIDTOUpdateCostCategory](docs/CentixAPIDTOUpdateCostCategory.md)
 - [CentixAPIDTOUpdateCostCode](docs/CentixAPIDTOUpdateCostCode.md)
 - [CentixAPIDTOUpdateDocument](docs/CentixAPIDTOUpdateDocument.md)
 - [CentixAPIDTOUpdateLocation](docs/CentixAPIDTOUpdateLocation.md)
 - [CentixAPIDTOUpdateObject](docs/CentixAPIDTOUpdateObject.md)
 - [CentixAPIDTOUpdateObjectCode](docs/CentixAPIDTOUpdateObjectCode.md)
 - [CentixAPIDTOUpdateObjectTypeDefaultMISchedule](docs/CentixAPIDTOUpdateObjectTypeDefaultMISchedule.md)
 - [CentixAPIDTOUpdatePerson](docs/CentixAPIDTOUpdatePerson.md)
 - [CentixAPIDTOUpdateProductSupplier](docs/CentixAPIDTOUpdateProductSupplier.md)
 - [CentixAPIDTOUpdateProductWarehouse](docs/CentixAPIDTOUpdateProductWarehouse.md)
 - [CentixAPIDTOUpdateRelation](docs/CentixAPIDTOUpdateRelation.md)
 - [CentixAPIDTOUpdateRentalProduct](docs/CentixAPIDTOUpdateRentalProduct.md)
 - [CentixAPIDTOUpdateSalesProduct](docs/CentixAPIDTOUpdateSalesProduct.md)
 - [CentixAPIDTOUpdateWorkflow](docs/CentixAPIDTOUpdateWorkflow.md)
 - [CentixAPIValidationError](docs/CentixAPIValidationError.md)
 - [CentixAPIValidationResult](docs/CentixAPIValidationResult.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization


Authentication schemes defined for the API:
<a id="oauth2"></a>
### oauth2

- **Type**: OAuth
- **Flow**: application
- **Authorization URL**: 
- **Scopes**: N/A


## Author




