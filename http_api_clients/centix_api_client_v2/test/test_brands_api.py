# coding: utf-8

"""
    Centix API 2.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v2
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from centix_api_client_v2.api.brands_api import BrandsApi


class TestBrandsApi(unittest.TestCase):
    """BrandsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = BrandsApi()

    def tearDown(self) -> None:
        pass

    def test_brands_o_data_list(self) -> None:
        """Test case for brands_o_data_list

        Get a list of brands
        """
        pass


if __name__ == '__main__':
    unittest.main()
