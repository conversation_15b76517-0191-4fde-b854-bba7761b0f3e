# coding: utf-8

"""
    Centix API 2.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v2
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from centix_api_client_v2.api.brand_type_revisions_api import BrandTypeRevisionsApi


class TestBrandTypeRevisionsApi(unittest.TestCase):
    """BrandTypeRevisionsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = BrandTypeRevisionsApi()

    def tearDown(self) -> None:
        pass

    def test_brand_type_revisions_o_data_list(self) -> None:
        """Test case for brand_type_revisions_o_data_list

        Get a list of brandtyperevisions
        """
        pass


if __name__ == '__main__':
    unittest.main()
