# coding: utf-8

"""
    Centix API 2.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v2
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from centix_api_client_v2.models.centix_api_core_list_result_centix_apidto_read_brand_type import CentixAPICoreListResultCentixAPIDTOReadBrandType

class TestCentixAPICoreListResultCentixAPIDTOReadBrandType(unittest.TestCase):
    """CentixAPICoreListResultCentixAPIDTOReadBrandType unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> CentixAPICoreListResultCentixAPIDTOReadBrandType:
        """Test CentixAPICoreListResultCentixAPIDTOReadBrandType
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `CentixAPICoreListResultCentixAPIDTOReadBrandType`
        """
        model = CentixAPICoreListResultCentixAPIDTOReadBrandType()
        if include_optional:
            return CentixAPICoreListResultCentixAPIDTOReadBrandType(
                items = [
                    centix_api_client_v2.models.centix/api/dto/read_brand_type.Centix.API.DTO.ReadBrandType(
                        brand_id = 56, 
                        default_object_type_id = 56, 
                        default_product_id = 56, 
                        archive = True, 
                        id = '', 
                        descr = '', 
                        auto_id = 56, 
                        created_by = '', 
                        modified_by = '', 
                        create_date = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                        time_stamp = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), )
                    ],
                top = 56,
                skip = 56,
                total_count = 56
            )
        else:
            return CentixAPICoreListResultCentixAPIDTOReadBrandType(
        )
        """

    def testCentixAPICoreListResultCentixAPIDTOReadBrandType(self):
        """Test CentixAPICoreListResultCentixAPIDTOReadBrandType"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
