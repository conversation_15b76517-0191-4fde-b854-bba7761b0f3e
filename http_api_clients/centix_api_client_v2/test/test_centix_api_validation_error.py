# coding: utf-8

"""
    Centix API 2.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v2
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from centix_api_client_v2.models.centix_api_validation_error import CentixAPIValidationError

class TestCentixAPIValidationError(unittest.TestCase):
    """CentixAPIValidationError unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> CentixAPIValidationError:
        """Test CentixAPIValidationError
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `CentixAPIValidationError`
        """
        model = CentixAPIValidationError()
        if include_optional:
            return CentixAPIValidationError(
                var_field = '',
                message = [
                    ''
                    ]
            )
        else:
            return CentixAPIValidationError(
        )
        """

    def testCentixAPIValidationError(self):
        """Test CentixAPIValidationError"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
