# coding: utf-8

"""
    Centix API 2.0

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: v2
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from centix_api_client_v2.api.brand_types_api import BrandTypesApi


class TestBrandTypesApi(unittest.TestCase):
    """BrandTypesApi unit test stubs"""

    def setUp(self) -> None:
        self.api = BrandTypesApi()

    def tearDown(self) -> None:
        pass

    def test_brand_types_o_data_list(self) -> None:
        """Test case for brand_types_o_data_list

        Get a list of brandtypes
        """
        pass


if __name__ == '__main__':
    unittest.main()
