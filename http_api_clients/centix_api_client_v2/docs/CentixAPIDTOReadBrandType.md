# CentixAPIDTOReadBrandType


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**brand_id** | **int** |  | [optional] 
**default_object_type_id** | **int** |  | [optional] 
**default_product_id** | **int** |  | [optional] 
**archive** | **bool** |  | [optional] 
**id** | **str** |  | [optional] 
**descr** | **str** |  | [optional] 
**auto_id** | **int** |  | [optional] 
**created_by** | **str** |  | [optional] 
**modified_by** | **str** |  | [optional] 
**create_date** | **datetime** |  | [optional] 
**time_stamp** | **datetime** |  | [optional] 

## Example

```python
from centix_api_client_v2.models.centix_apidto_read_brand_type import CentixAPIDTOReadBrandType

# TODO update the JSON string below
json = "{}"
# create an instance of CentixAPIDTOReadBrandType from a JSON string
centix_apidto_read_brand_type_instance = CentixAPIDTOReadBrandType.from_json(json)
# print the JSON string representation of the object
print(CentixAPIDTOReadBrandType.to_json())

# convert the object into a dict
centix_apidto_read_brand_type_dict = centix_apidto_read_brand_type_instance.to_dict()
# create an instance of CentixAPIDTOReadBrandType from a dict
centix_apidto_read_brand_type_from_dict = CentixAPIDTOReadBrandType.from_dict(centix_apidto_read_brand_type_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


