# CentixAPIDTOReadBrand


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**archive** | **bool** |  | [optional] 
**id** | **str** |  | [optional] 
**descr** | **str** |  | [optional] 
**auto_id** | **int** |  | [optional] 
**created_by** | **str** |  | [optional] 
**modified_by** | **str** |  | [optional] 
**create_date** | **datetime** |  | [optional] 
**time_stamp** | **datetime** |  | [optional] 

## Example

```python
from centix_api_client_v2.models.centix_apidto_read_brand import CentixAPIDTOReadBrand

# TODO update the JSON string below
json = "{}"
# create an instance of CentixAPIDTOReadBrand from a JSON string
centix_apidto_read_brand_instance = CentixAPIDTOReadBrand.from_json(json)
# print the JSON string representation of the object
print(CentixAPIDTOReadBrand.to_json())

# convert the object into a dict
centix_apidto_read_brand_dict = centix_apidto_read_brand_instance.to_dict()
# create an instance of CentixAPIDTOReadBrand from a dict
centix_apidto_read_brand_from_dict = CentixAPIDTOReadBrand.from_dict(centix_apidto_read_brand_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


