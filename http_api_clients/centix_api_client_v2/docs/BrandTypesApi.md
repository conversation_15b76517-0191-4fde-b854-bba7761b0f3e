# centix_api_client_v2.BrandTypesApi

All URIs are relative to *https://holmatro-dev.centix.com/api*

Method | HTTP request | Description
------------- | ------------- | -------------
[**brand_types_o_data_list**](BrandTypesApi.md#brand_types_o_data_list) | **GET** /v2/brandtypes | Get a list of brandtypes


# **brand_types_o_data_list**
> CentixAPICoreListResultCentixAPIDTOReadBrandType brand_types_o_data_list(filter=filter, orderby=orderby, skip=skip, top=top)

Get a list of brandtypes

### Example

* OAuth Authentication (oauth2):

```python
import centix_api_client_v2
from centix_api_client_v2.models.centix_api_core_list_result_centix_apidto_read_brand_type import CentixAPICoreListResultCentixAPIDTOReadBrandType
from centix_api_client_v2.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://holmatro-dev.centix.com/api
# See configuration.py for a list of all supported configuration parameters.
configuration = centix_api_client_v2.Configuration(
    host = "https://holmatro-dev.centix.com/api"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

configuration.access_token = os.environ["ACCESS_TOKEN"]

# Enter a context with an instance of the API client
with centix_api_client_v2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = centix_api_client_v2.BrandTypesApi(api_client)
    filter = 'filter_example' # str | Filter the results using OData syntax. (optional)
    orderby = 'orderby_example' # str | Order the results using OData syntax. (optional)
    skip = 56 # int | The number of results to skip. (optional)
    top = 56 # int | The number of results to return. (optional)

    try:
        # Get a list of brandtypes
        api_response = api_instance.brand_types_o_data_list(filter=filter, orderby=orderby, skip=skip, top=top)
        print("The response of BrandTypesApi->brand_types_o_data_list:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling BrandTypesApi->brand_types_o_data_list: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **filter** | **str**| Filter the results using OData syntax. | [optional] 
 **orderby** | **str**| Order the results using OData syntax. | [optional] 
 **skip** | **int**| The number of results to skip. | [optional] 
 **top** | **int**| The number of results to return. | [optional] 

### Return type

[**CentixAPICoreListResultCentixAPIDTOReadBrandType**](CentixAPICoreListResultCentixAPIDTOReadBrandType.md)

### Authorization

[oauth2](../README.md#oauth2)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | OK |  -  |
**400** | BadRequest |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

