# CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**items** | [**List[CentixAPIDTOReadBrandTypeRevision]**](CentixAPIDTOReadBrandTypeRevision.md) |  | [optional] 
**top** | **int** |  | [optional] 
**skip** | **int** |  | [optional] 
**total_count** | **int** |  | [optional] 

## Example

```python
from centix_api_client_v2.models.centix_api_core_list_result_centix_apidto_read_brand_type_revision import CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision

# TODO update the JSON string below
json = "{}"
# create an instance of CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision from a JSON string
centix_api_core_list_result_centix_apidto_read_brand_type_revision_instance = CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision.from_json(json)
# print the JSON string representation of the object
print(CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision.to_json())

# convert the object into a dict
centix_api_core_list_result_centix_apidto_read_brand_type_revision_dict = centix_api_core_list_result_centix_apidto_read_brand_type_revision_instance.to_dict()
# create an instance of CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision from a dict
centix_api_core_list_result_centix_apidto_read_brand_type_revision_from_dict = CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision.from_dict(centix_api_core_list_result_centix_apidto_read_brand_type_revision_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


