# CentixAPICoreListResultCentixAPIDTOReadBrand


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**items** | [**List[CentixAPIDTOReadBrand]**](CentixAPIDTOReadBrand.md) |  | [optional] 
**top** | **int** |  | [optional] 
**skip** | **int** |  | [optional] 
**total_count** | **int** |  | [optional] 

## Example

```python
from centix_api_client_v2.models.centix_api_core_list_result_centix_apidto_read_brand import CentixAPICoreListResultCentixAPIDTOReadBrand

# TODO update the JSON string below
json = "{}"
# create an instance of CentixAPICoreListResultCentixAPIDTOReadBrand from a JSON string
centix_api_core_list_result_centix_apidto_read_brand_instance = CentixAPICoreListResultCentixAPIDTOReadBrand.from_json(json)
# print the JSON string representation of the object
print(CentixAPICoreListResultCentixAPIDTOReadBrand.to_json())

# convert the object into a dict
centix_api_core_list_result_centix_apidto_read_brand_dict = centix_api_core_list_result_centix_apidto_read_brand_instance.to_dict()
# create an instance of CentixAPICoreListResultCentixAPIDTOReadBrand from a dict
centix_api_core_list_result_centix_apidto_read_brand_from_dict = CentixAPICoreListResultCentixAPIDTOReadBrand.from_dict(centix_api_core_list_result_centix_apidto_read_brand_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


