# CentixAPIValidationError


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**var_field** | **str** |  | [optional] [readonly] 
**message** | **List[str]** |  | [optional] [readonly] 

## Example

```python
from centix_api_client_v2.models.centix_api_validation_error import CentixAPIValidationError

# TODO update the JSON string below
json = "{}"
# create an instance of CentixAPIValidationError from a JSON string
centix_api_validation_error_instance = CentixAPIValidationError.from_json(json)
# print the JSON string representation of the object
print(CentixAPIValidationError.to_json())

# convert the object into a dict
centix_api_validation_error_dict = centix_api_validation_error_instance.to_dict()
# create an instance of CentixAPIValidationError from a dict
centix_api_validation_error_from_dict = CentixAPIValidationError.from_dict(centix_api_validation_error_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


