.github/workflows/python.yml
.gitignore
.gitlab-ci.yml
.openapi-generator-ignore
.travis.yml
README.md
centix_api_client_v2/__init__.py
centix_api_client_v2/api/__init__.py
centix_api_client_v2/api/brand_type_revisions_api.py
centix_api_client_v2/api/brand_types_api.py
centix_api_client_v2/api/brands_api.py
centix_api_client_v2/api_client.py
centix_api_client_v2/api_response.py
centix_api_client_v2/configuration.py
centix_api_client_v2/exceptions.py
centix_api_client_v2/models/__init__.py
centix_api_client_v2/models/centix_api_core_list_result_centix_apidto_read_brand.py
centix_api_client_v2/models/centix_api_core_list_result_centix_apidto_read_brand_type.py
centix_api_client_v2/models/centix_api_core_list_result_centix_apidto_read_brand_type_revision.py
centix_api_client_v2/models/centix_api_validation_error.py
centix_api_client_v2/models/centix_apidto_read_brand.py
centix_api_client_v2/models/centix_apidto_read_brand_type.py
centix_api_client_v2/models/centix_apidto_read_brand_type_revision.py
centix_api_client_v2/py.typed
centix_api_client_v2/rest.py
docs/BrandTypeRevisionsApi.md
docs/BrandTypesApi.md
docs/BrandsApi.md
docs/CentixAPICoreListResultCentixAPIDTOReadBrand.md
docs/CentixAPICoreListResultCentixAPIDTOReadBrandType.md
docs/CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision.md
docs/CentixAPIDTOReadBrand.md
docs/CentixAPIDTOReadBrandType.md
docs/CentixAPIDTOReadBrandTypeRevision.md
docs/CentixAPIValidationError.md
git_push.sh
pyproject.toml
requirements.txt
setup.cfg
setup.py
test-requirements.txt
test/__init__.py
test/test_brand_type_revisions_api.py
test/test_brand_types_api.py
test/test_brands_api.py
test/test_centix_api_core_list_result_centix_apidto_read_brand.py
test/test_centix_api_core_list_result_centix_apidto_read_brand_type.py
test/test_centix_api_core_list_result_centix_apidto_read_brand_type_revision.py
test/test_centix_api_validation_error.py
test/test_centix_apidto_read_brand.py
test/test_centix_apidto_read_brand_type.py
test/test_centix_apidto_read_brand_type_revision.py
tox.ini
