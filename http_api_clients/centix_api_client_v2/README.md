# centix-api-client-v2
No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: v2
- Package version: 1.0.0
- Generator version: 7.13.0
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.9+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import centix_api_client_v2
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import centix_api_client_v2
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import centix_api_client_v2
from centix_api_client_v2.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://holmatro-dev.centix.com/api
# See configuration.py for a list of all supported configuration parameters.
configuration = centix_api_client_v2.Configuration(
    host = "https://holmatro-dev.centix.com/api"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

configuration.access_token = os.environ["ACCESS_TOKEN"]


# Enter a context with an instance of the API client
with centix_api_client_v2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = centix_api_client_v2.BrandTypeRevisionsApi(api_client)
    filter = 'filter_example' # str | Filter the results using OData syntax. (optional)
    orderby = 'orderby_example' # str | Order the results using OData syntax. (optional)
    skip = 56 # int | The number of results to skip. (optional)
    top = 56 # int | The number of results to return. (optional)

    try:
        # Get a list of brandtyperevisions
        api_response = api_instance.brand_type_revisions_o_data_list(filter=filter, orderby=orderby, skip=skip, top=top)
        print("The response of BrandTypeRevisionsApi->brand_type_revisions_o_data_list:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling BrandTypeRevisionsApi->brand_type_revisions_o_data_list: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://holmatro-dev.centix.com/api*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*BrandTypeRevisionsApi* | [**brand_type_revisions_o_data_list**](docs/BrandTypeRevisionsApi.md#brand_type_revisions_o_data_list) | **GET** /v2/brandtyperevisions | Get a list of brandtyperevisions
*BrandTypesApi* | [**brand_types_o_data_list**](docs/BrandTypesApi.md#brand_types_o_data_list) | **GET** /v2/brandtypes | Get a list of brandtypes
*BrandsApi* | [**brands_o_data_list**](docs/BrandsApi.md#brands_o_data_list) | **GET** /v2/brands | Get a list of brands


## Documentation For Models

 - [CentixAPICoreListResultCentixAPIDTOReadBrand](docs/CentixAPICoreListResultCentixAPIDTOReadBrand.md)
 - [CentixAPICoreListResultCentixAPIDTOReadBrandType](docs/CentixAPICoreListResultCentixAPIDTOReadBrandType.md)
 - [CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision](docs/CentixAPICoreListResultCentixAPIDTOReadBrandTypeRevision.md)
 - [CentixAPIDTOReadBrand](docs/CentixAPIDTOReadBrand.md)
 - [CentixAPIDTOReadBrandType](docs/CentixAPIDTOReadBrandType.md)
 - [CentixAPIDTOReadBrandTypeRevision](docs/CentixAPIDTOReadBrandTypeRevision.md)
 - [CentixAPIValidationError](docs/CentixAPIValidationError.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization


Authentication schemes defined for the API:
<a id="oauth2"></a>
### oauth2

- **Type**: OAuth
- **Flow**: application
- **Authorization URL**: 
- **Scopes**: N/A


## Author




