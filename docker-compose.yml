services:
  centix_load:
    container_name: ${COMPOSE_PROJECT_NAME}_centix_load_app
    build:
      context: .
      dockerfile: ./centix_load/Dockerfile
    command: make dev
    stdin_open: true
    tty: true
    env_file:
      - .env
      - ./centix_load/.env
    volumes:
      - ./centix_load/src:/app/src
      - ./centix_load/tests:/app/tests
      - ./centix_load/poetry.lock:/app/poetry.lock
      - ./centix_load/pyproject.toml:/app/pyproject.toml
      - ./centix_load/mypy.ini:/app/mypy.ini
      - ./http_api_clients:/http_api_clients

  my_holmatro_portal:
    container_name: ${COMPOSE_PROJECT_NAME}_my_holmatro_portal_api
    build:
      context: ./my_holmatro_portal
    command: make dev
    working_dir: /app
    ports:
      - '8000:8000'
    depends_on:
      sqlserver:
        condition: service_healthy
    env_file:
      - .env
      - ./my_holmatro_portal/.env
    environment:
      - DB_SERVER=sqlserver
      - DB_NAME=${DB_NAME:-master}
      - DB_USER=sa
      - DB_PASSWORD=${DB_SA_PASSWORD}
    volumes:
      - ./my_holmatro_portal/src:/app/src
      - ./my_holmatro_portal/tests:/app/tests
      - ./my_holmatro_portal/poetry.lock:/app/poetry.lock
      - ./my_holmatro_portal/pyproject.toml:/app/pyproject.toml
      - ./my_holmatro_portal/alembic.ini:/app/alembic.ini
    healthcheck:
      test:
        ['CMD', 'curl', '-f', 'http://localhost:8000/general/health || exit 1']
      interval: 10s
      timeout: 5s

  sqlserver:
    image: mcr.microsoft.com/mssql/server:latest
    container_name: ${COMPOSE_PROJECT_NAME}_sqlserver
    platform: linux/amd64
    ports:
      - '${DB_SERVICE_PORT:-1433}:1433'
    env_file:
      - .env
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_SA_PASSWORD}
      - MSSQL_PID=Developer
      - MSSQL_DB=${DB_NAME}
    volumes:
      - sqlserver_data:/var/opt/mssql
    healthcheck:
      test: /opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P Password123# -Q "SELECT 1" -b -o /dev/null -C
      interval: 10s
      timeout: 3s
      retries: 10
      start_period: 10s

volumes:
  sqlserver_data:
    driver: local