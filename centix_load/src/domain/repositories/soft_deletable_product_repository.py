from abc import ABC
from abc import abstractmethod

from src.domain.repositories.target_product_repository import TargetProductRepository


class SoftDeletableProductRepository(TargetProductRepository, ABC):
    """
    Interface for product repository operations that support soft deletion
    or unpublishing of products.
    """

    @abstractmethod
    def soft_delete_products(self, product_article_numbers: list[str]) -> list[str]:
        """
        Soft-deletes (unpublishes) products in the target repository.

        Args:
            product_article_numbers: A list of product article numbers that exist in the source product repository
        Returns:
            A list of article numbers of products that had their status successfully updated to "draft" in WooCommerce.
        """
        raise NotImplementedError
