import logging
from typing import Dict
from typing import List

from woocommerce import API

from src.data.models.woocommerce.product import Product as WCProduct


class ProductNotFound(Exception):
    pass


class WoocommerceProductDatasource:
    def __init__(self, woocommerce_api: API):
        self._woocommerce_api = woocommerce_api
        self.page_size = 100

    def get_product_by_sku(self, sku: str) -> WCProduct:
        response = self._woocommerce_api.get("products", params={"sku": sku})
        response.raise_for_status()

        products = response.json()
        if products:
            return self._map_response_product(products[0])
        raise ProductNotFound(f"Product with SKU {sku} not found")

    def create(self, payload: dict) -> WCProduct:
        response = self._woocommerce_api.post("products", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_product(data)

    def update(self, product_id: int, payload: dict) -> WCProduct:
        response = self._woocommerce_api.put(f"products/{product_id}", payload)
        response.raise_for_status()
        data = response.json()
        return self._map_response_product(data)

    def delete(self, product_id: int) -> None:
        response = self._woocommerce_api.delete(f"products/{product_id}", params={"force": True})
        response.raise_for_status()

    def get_all(self) -> list[WCProduct]:
        """Fetches all products from WooCommerce."""
        all_products = []
        for products_data in self._paginate_products():
            for product_data in products_data:
                all_products.append(self._map_response_product(product_data))
        return all_products

    def get_all_products_with_fields(self, fields: List[str] | None = None) -> List[dict]:
        """Fetches all product IDs from WooCommerce."""
        all_pages = []
        for products_data_page in self._paginate_products(fields=fields):
            all_pages += products_data_page
        return all_pages

    def _paginate_products(self, fields: List[str] | None = None):
        """Generator that yields product data pages from WooCommerce."""
        page = 1
        per_page = self.page_size
        while True:
            params: Dict[str, int | str] = {
                "per_page": per_page,
                "page": page,
            }

            if fields:
                params["_fields"] = ",".join(fields)

            response = self._woocommerce_api.get("products", params=params)
            response.raise_for_status()
            products_data = response.json()
            if not products_data:
                break
            yield products_data
            if len(products_data) < per_page:
                break
            page += 1

    @staticmethod
    def _map_response_product(product_data: dict) -> WCProduct:
        return WCProduct(
            id=product_data["id"],
            name=product_data["name"],
            sku=product_data["sku"],
            description=product_data.get("description"),
            category_ids=[cat["id"] for cat in product_data.get("categories", [])],
        )

    def batch_update(self, updates: list[dict]) -> dict:
        """
        Updates products in WooCommerce in a batch using the batch endpoint.
        Each update dict should contain at least 'id' and the fields to update.
        Returns a dict with 'updated' and 'failed' lists of product IDs.
        """
        if not updates:
            logging.info("No products to batch update.")
            return {"updated": [], "failed": []}

        response = self._woocommerce_api.post("products/batch", {"update": updates})
        response.raise_for_status()
        updated_ids = [prod["id"] for prod in response.get("update", [])]
        failed_ids = [prod["id"] for prod in updates if prod["id"] not in updated_ids]
        if failed_ids:
            logging.warning(f"Batch update failed for product IDs: {failed_ids}")
        return {"updated": updated_ids, "failed": failed_ids}
