import logging

from src.data.woocommerce_category_datasource import WoocommerceCategoryDatasource
from src.data.woocommerce_product_datasource import ProductNotFound
from src.data.woocommerce_product_datasource import WoocommerceProductDatasource
from src.domain.models.asset import AssetDetails
from src.domain.models.product import Product
from src.domain.repositories.soft_deletable_product_repository import SoftDeletableProductRepository
from src.shared.enum import LanguageEnum


class WoocommerceProductRepositoryImpl(SoftDeletableProductRepository):
    def __init__(
        self,
        woocommerce_category_datasource: WoocommerceCategoryDatasource,
        woocommerce_product_datasource: WoocommerceProductDatasource,
    ):
        self._woocommerce_category_datasource = woocommerce_category_datasource
        self._woocommerce_product_datasource = woocommerce_product_datasource

    def upsert(
        self,
        product: Product,
        assets: list[AssetDetails],
        img_path: str | None = None,
    ) -> None:
        all_categories = self._woocommerce_category_datasource.get_categories()
        category_guid_to_id = {cat.guid: cat.id for cat in all_categories if cat.guid}

        try:
            existing_product = self._woocommerce_product_datasource.get_product_by_sku(product.article_number)
            self.update(existing_product.id, product, category_guid_to_id)
        except ProductNotFound:
            self.create(product, category_guid_to_id)

    def create(self, product: Product, category_guid_to_id: dict) -> int:
        payload = self._build_payload(product, category_guid_to_id)
        wc_product = self._woocommerce_product_datasource.create(payload)
        return wc_product.id

    def update(self, product_id: int, product: Product, category_guid_to_id: dict):
        payload = self._build_payload(product, category_guid_to_id)
        self._woocommerce_product_datasource.update(product_id, payload)

    def soft_delete_products(self, product_article_numbers: list[str]) -> list[str]:
        """
        Soft-deletes (unpublishes) products in WooCommerce that are no longer in the source product repository.
        Uses batch update for efficiency and logs failures.
        """
        if not product_article_numbers:
            return []

        # Fetch all products from WooCommerce
        all_products = self._woocommerce_product_datasource.get_all_products_with_fields(["id", "sku"])
        all_wc_skus = {product["sku"]: product["id"] for product in all_products}

        # Find products to soft-delete
        to_soft_delete = [
            {"id": product_id, "status": "draft"}
            for wc_article_number, product_id in all_wc_skus.items()
            if wc_article_number not in product_article_numbers
        ]
        if not to_soft_delete:
            logging.info("No products to soft-delete.")
            return []

        # Batch update
        result = self._woocommerce_product_datasource.batch_update(to_soft_delete)
        updated_skus = [sku for sku, pid in all_wc_skus.items() if pid in result["updated"]]
        failed_skus = [sku for sku, pid in all_wc_skus.items() if pid in result["failed"]]
        if failed_skus:
            logging.warning(f"Failed to soft-delete products with SKUs: {failed_skus}")
        return updated_skus

    def _build_payload(self, product: Product, category_guid_to_id: dict) -> dict:
        name = product.name.get_translation(LanguageEnum.EN) or product.name.get_translation(LanguageEnum.NL)
        description = self._get_description(product, LanguageEnum.EN)
        category_id_dicts = [
            {"id": category_guid_to_id[guid]} for guid in product.leaf_category_guids if guid in category_guid_to_id
        ]
        return {
            "name": name,
            "type": "simple",
            "status": "publish",
            "description": description,
            "sku": product.article_number,
            "purchasable": False,
            "catalog_visibility": "visible",
            "manage_stock": False,
            "categories": category_id_dicts,
        }

    def _get_description(self, product: Product, language: LanguageEnum = LanguageEnum.EN) -> str | None:
        for text in product.texts:
            translation = text.get_translation(language)
            if translation:
                return translation
        return None
