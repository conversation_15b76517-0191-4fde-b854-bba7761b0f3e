import unittest
from unittest.mock import MagicMock

from src.data.woocommerce_product_repository_impl import WoocommerceProductRepositoryImpl


class TestWoocommerceProductRepositoryImpl(unittest.TestCase):

    def test_soft_delete_products_soft_deletes_unmatched_products(self):
        datasource = MagicMock()
        datasource.get_all_products_with_fields.return_value = [
            {"sku": "sku1", "id": 1},
            {"sku": "sku2", "id": 2},
        ]
        repo = WoocommerceProductRepositoryImpl(
            woocommerce_product_datasource=datasource, woocommerce_category_datasource=MagicMock()
        )
        # Only sku1 should remain, sku2 should be soft deleted
        repo.soft_delete_products(["sku1"])
        # Assert
        datasource.batch_update.assert_called_once_with([{"id": 2, "status": "draft"}])

    def test_soft_delete_products_no_products_to_delete(self):
        datasource = MagicMock()
        datasource.get_all_products_with_fields.return_value = [{"sku": "sku1", "id": 1}]
        repo = WoocommerceProductRepositoryImpl(
            woocommerce_product_datasource=datasource, woocommerce_category_datasource=MagicMock()
        )
        # All products are present, nothing to delete
        result = repo.soft_delete_products(["sku1"])
        datasource.batch_update.assert_not_called()
        assert result == []

    def test_soft_delete_products_empty_input(self):
        datasource = MagicMock()
        repo = WoocommerceProductRepositoryImpl(
            woocommerce_product_datasource=datasource, woocommerce_category_datasource=MagicMock()
        )
        result = repo.soft_delete_products([])
        assert result == []
        datasource.get_all_products_with_fields.assert_not_called()
        datasource.batch_update.assert_not_called()
