import unittest
from unittest.mock import MagicMock

from src.data.models.woocommerce.product import Product as WCProduct
from src.data.woocommerce_product_datasource import ProductNotFound
from src.data.woocommerce_product_datasource import WoocommerceProductDatasource


class TestWoocommerceProductDatasource(unittest.TestCase):

    def setUp(self):
        self.mock_api = MagicMock()
        self.datasource = WoocommerceProductDatasource(self.mock_api)

    def test_get_product_by_sku_found(self):
        sku = "TESTSKU123"
        mock_product_data = {
            "id": 1,
            "name": "Test Product",
            "sku": sku,
            "description": "A test product.",
            "categories": [{"id": 10, "name": "Test Category"}],
        }
        self.mock_api.get.return_value.json.return_value = [mock_product_data]
        self.mock_api.get.return_value.raise_for_status.return_value = None

        product = self.datasource.get_product_by_sku(sku)

        self.mock_api.get.assert_called_once_with("products", params={"sku": sku})
        self.assertIsInstance(product, WCProduct)
        self.assertEqual(product.sku, sku)
        self.assertEqual(product.name, "Test Product")
        self.assertEqual(product.category_ids, [10])

    def test_get_product_by_sku_not_found(self):
        sku = "NONEXISTENTSKU"
        self.mock_api.get.return_value.json.return_value = []
        self.mock_api.get.return_value.raise_for_status.return_value = None

        with self.assertRaises(ProductNotFound):
            self.datasource.get_product_by_sku(sku)
        self.mock_api.get.assert_called_once_with("products", params={"sku": sku})

    def test_create_product(self):
        payload = {"name": "New Product", "sku": "NEWPROD"}
        mock_response_data = {"id": 2, "name": "New Product", "sku": "NEWPROD", "description": None, "categories": []}
        self.mock_api.post.return_value.json.return_value = mock_response_data
        self.mock_api.post.return_value.raise_for_status.return_value = None

        product = self.datasource.create(payload)

        self.mock_api.post.assert_called_once_with("products", payload)
        self.assertIsInstance(product, WCProduct)
        self.assertEqual(product.sku, "NEWPROD")
        self.assertIsNone(product.description)

    def test_update_product(self):
        product_id = 3
        payload = {"description": "Updated description"}
        mock_response_data = {
            "id": product_id,
            "name": "Existing Product",
            "sku": "EXISTING1",
            "description": "Updated description",
            "categories": [],
        }
        self.mock_api.put.return_value.json.return_value = mock_response_data
        self.mock_api.put.return_value.raise_for_status.return_value = None

        product = self.datasource.update(product_id, payload)

        self.mock_api.put.assert_called_once_with(f"products/{product_id}", payload)
        self.assertEqual(product.id, product_id)
        self.assertEqual(product.description, "Updated description")

    def test_delete_product(self):
        product_id = 4
        self.mock_api.delete.return_value.raise_for_status.return_value = None

        self.datasource.delete(product_id)

        self.mock_api.delete.assert_called_once_with(f"products/{product_id}", params={"force": True})

    def test_get_all_product_skus_empty(self):
        self.mock_api.get.return_value.json.return_value = []
        self.mock_api.get.return_value.raise_for_status.return_value = None

        skus = self.datasource.get_all()

        self.assertEqual(skus, [])
        self.mock_api.get.assert_called_once_with("products", params={"per_page": 100, "page": 1})

    def test_get_all_product_skus_with_data(self):
        mock_products_page1 = [
            {"id": 5, "sku": "SKU001", "name": "Product A", "categories": []},
            {"id": 6, "sku": "SKU002", "name": "Product B", "categories": []},
        ]
        mock_products_page2 = [
            {"id": 7, "sku": "SKU003", "name": "Product C", "categories": []},
        ]

        # Create mock responses for each page
        mock_response_page1 = MagicMock()
        mock_response_page1.json.return_value = mock_products_page1
        mock_response_page1.raise_for_status.return_value = None

        mock_response_page2 = MagicMock()
        mock_response_page2.json.return_value = mock_products_page2
        mock_response_page2.raise_for_status.return_value = None

        # Configure the mock_api.get to return different responses based on the page number
        def mock_get_side_effect(*args, **kwargs):
            # Check the 'page' parameter to determine which response to return
            if kwargs.get("params", {}).get("page") == 1:
                return mock_response_page1
            elif kwargs.get("params", {}).get("page") == 2:
                return mock_response_page2
            # Return a default mock for any other calls
            return MagicMock(json=lambda: [], raise_for_status=lambda: None)

        self.mock_api.get.side_effect = mock_get_side_effect

        # Set per_page to 2 for this test so pagination is triggered
        self.datasource.page_size = 2

        skus = self.datasource.get_all()
        p1 = WCProduct(id=5, name="Product A", sku="SKU001", description=None, category_ids=[])
        p2 = WCProduct(id=6, name="Product B", sku="SKU002", description=None, category_ids=[])
        p3 = WCProduct(id=7, name="Product C", sku="SKU003", description=None, category_ids=[])

        self.assertEqual(skus, [p1, p2, p3])
        # Assert calls for both pages
        self.mock_api.get.assert_any_call("products", params={"per_page": 2, "page": 1})
        self.mock_api.get.assert_any_call("products", params={"per_page": 2, "page": 2})
        self.assertEqual(self.mock_api.get.call_count, 2)
