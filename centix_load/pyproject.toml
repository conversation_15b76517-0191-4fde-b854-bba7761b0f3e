[tool.poetry]
name = "centix-load"
version = "0.1.0"
description = "Integrations app for Holmatro."
authors = ["Harborn Digital <<EMAIL>>"]
readme = "README.md"
package-mode = false
packages = [
    { include = "src" },
]

[tool.poetry.dependencies]
python = "~3.12.0" # Also update tool.black and tool.mypy config when changed
centix-api-client = { path = "../http_api_clients/centix_api_client" }
centix-api-client-v2 = { path = "../http_api_clients/centix_api_client_v2" }
sentry-sdk = "^1.37.1"
pydantic = "^2.5.2"
pydantic-settings = "^2.3.3"
cachetools = "^5.5.0"
requests = "^2.32.3"
pyhumps = "^3.8.0"
six = "^1.17.0"
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
fastapi = "^0.115.6"
uvicorn = "^0.34.0"
cryptography = "^44.0.0"
pyodbc = "^5.2.0"
sqlalchemy-utils = "^0.41.2"
python-jose = "^3.3.0"
httpx = "^0.28.1"
authlib = "^1.4.1"
alembic-autogen-check = "^1.1.1"
aiocache = "^0.12.3"
email-validator = "^2.2.0"
tqdm = "^4.67.1"
woocommerce = "^3.0.0"
python-slugify = "^8.0.4"

[tool.poetry.group.dev.dependencies]
alembic-autogen-check = "^1.1.1"
black = "^24.10.0"
autoflake = "^2.3.1"
isort = "^5.13.2"
pytest = "^8.3.4"
pytest-cov = "^6.0.0"
pytest-timeout = "^2.3.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"
pytest-mock = "^3.14.0"
pytest-asyncio = "^0.25.3"
pytest-httpx = "^0.35.0"
vcrpy = "^7.0.0"
mypy = "^1.14.1"
flake8 = "^7.1.1"
flake8-bugbear = "^24.12.12"
flake8-deprecated = "^2.2.1"
flake8-executable = "^2.1.3"
flake8-simplify = "^0.21.0"
flake8-debugger = "^4.1.2"
flake8-cognitive-complexity = "^0.1.0"
flake8-annotations-complexity = "^0.0.8"
flake8-functions = "^0.0.8"
flake8-pyproject = "^1.2.3"
bandit = "^1.8.2"
pre-commit = "^4.0.1"
poethepoet = "^0.32.1"
tqdm = "^4.67.1"
types-requests = "<2.32.0.20241016"
types-cachetools = "^5.5.0.20240820"
types-tqdm = "^4.67.0.20241119"
types-python-jose = "^3.3.4.20240106"
pytest-recording = "^0.13.2"
polyfactory = "^2.22.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
target-version = ["py312"]
include = "\\.pyi?$"
exclude = '/(http_api_clients)/'

[tool.autoflake]
in-place = true
expand-star-imports = true
recursive = true

[tool.isort]
profile = "black"
skip_glob = ["**/http_api_clients/**"]
line_length = 120
atomic = true
force_single_line = true


[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["./tests"]
pythonpath = ["."]
filterwarnings = [
    "error",
    "ignore:datetime.datetime.utcnow" # from sqlalchemy-utils: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
]
markers = [
    "sequential: marks tests as sequential to run serially without randomization.",
]
asyncio_default_fixture_loop_scope = "module"

[tool.bandit]
exclude_dirs = ["tests", "http_api_clients"]
recursive = true

[tool.flake8]
max-line-length = 130
ignore = ["EXE002", "W503", "CCR001", "CFQ004", "B008", "B023", "TAE002", "E704", "CFQ001"]
# This tells flake8 to ignore the specified directories.
exclude = [
    ".git",
    "__pycache__",
    "http_api_clients",
    "build",
    "dist",
    ".venv"
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disable_error_code = ["import-untyped", "union-attr", "arg-type"]
plugins = ["pydantic.mypy"] 
exclude = '(?x)(^http_api_clients/)'

[[tool.mypy.overrides]]
module = "sqlalchemy_utils.*"
ignore_missing_imports = true

[tool.poe.tasks]
fix = [
    { cmd = "autoflake ."},
    { cmd = "black ." },
    { cmd = "isort ." },
    { cmd = "mypy --explicit-package-bases ." },
]
test = "pytest --cov=/*/src --cov-report=term-missing:skip-covered --cov-report=lcov:coverage.info -n auto --dist loadfile --random-order --log-level ERROR --junitxml=test-results.xml"
retest = "pytest --lf"
security = "bandit -c pyproject.toml -r ."
qa = [
    { cmd = "flake8" },
    { cmd = "mypy --explicit-package-bases ." },
]
check-migrations = [
    { cmd = "alembic upgrade head" },
    { cmd = "alembic-autogen-check" },
]